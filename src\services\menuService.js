// Menu Service
const databaseManager = require('../config/database');

class MenuService {
  // Get all menu categories
  static async getCategories() {
    const pool = databaseManager.getPool();

    try {
      const query = `
        SELECT 
          mc.id, mc.name, mc.description, mc.sort_order, mc.is_active,
          COUNT(mi.id) as item_count
        FROM menu_categories mc
        LEFT JOIN menu_items mi ON mc.id = mi.category_id AND mi.is_available = true
        WHERE mc.is_active = true
        GROUP BY mc.id, mc.name, mc.description, mc.sort_order, mc.is_active
        ORDER BY mc.sort_order ASC, mc.name ASC
      `;

      const result = await pool.query(query);

      return result.rows.map(category => ({
        id: category.id,
        name: category.name,
        description: category.description,
        image: null, // TODO: Add category images
        sortOrder: category.sort_order,
        active: category.is_active,
        itemCount: parseInt(category.item_count)
      }));
    } catch (error) {
      throw error;
    }
  }

  // Get menu items with filtering and pagination
  static async getMenuItems(filters) {
    const pool = databaseManager.getPool();

    try {
      const { categoryId, page, limit, available } = filters;
      const offset = (page - 1) * limit;

      let whereConditions = ['mi.is_available = $1'];
      let queryParams = [available];
      let paramIndex = 2;

      if (categoryId) {
        whereConditions.push(`mi.category_id = $${paramIndex}`);
        queryParams.push(categoryId);
        paramIndex++;
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM menu_items mi
        WHERE ${whereClause}
      `;

      const countResult = await pool.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].total);

      // Get items
      const itemsQuery = `
        SELECT 
          mi.id, mi.name, mi.description, mi.price, mi.image_url,
          mi.category_id, mi.is_vegetarian, mi.is_vegan, mi.is_gluten_free,
          mi.allergens, mi.calories, mi.prep_time, mi.is_available,
          mc.name as category_name
        FROM menu_items mi
        LEFT JOIN menu_categories mc ON mi.category_id = mc.id
        WHERE ${whereClause}
        ORDER BY mi.sort_order ASC, mi.name ASC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      queryParams.push(limit, offset);
      const itemsResult = await pool.query(itemsQuery, queryParams);

      const items = await Promise.all(itemsResult.rows.map(async (item) => {
        // Get customizations for this menu item
        const customizations = await this.getMenuItemCustomizations(item.id);

        return {
          id: item.id,
          name: item.name,
          description: item.description,
          price: parseFloat(item.price),
          image: item.image_url,
          categoryId: item.category_id,
          available: item.is_available,
          popular: false, // TODO: Implement popularity logic based on order frequency
          ingredients: item.allergens ? item.allergens.filter(a => !['Gluten', 'Dairy', 'Nuts'].includes(a)) : [],
          allergens: item.allergens ? item.allergens.filter(a => ['Gluten', 'Dairy', 'Nuts'].includes(a)) : [],
          preparationTime: item.prep_time,
          nutritionInfo: {
            calories: item.calories,
            protein: null, // TODO: Add nutrition fields to database
            carbs: null,
            fat: null
          },
          customizations
        };
      }));

      return {
        items,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get specific menu item by ID
  static async getMenuItemById(menuItemId) {
    const pool = databaseManager.getPool();

    try {
      const query = `
        SELECT 
          mi.id, mi.name, mi.description, mi.price, mi.image_url,
          mi.category_id, mi.is_vegetarian, mi.is_vegan, mi.is_gluten_free,
          mi.allergens, mi.calories, mi.prep_time, mi.is_available,
          mc.name as category_name
        FROM menu_items mi
        LEFT JOIN menu_categories mc ON mi.category_id = mc.id
        WHERE mi.id = $1 AND mi.is_available = true
      `;

      const result = await pool.query(query, [menuItemId]);

      if (result.rows.length === 0) {
        return null;
      }

      const item = result.rows[0];

      // Get customizations for this menu item
      const customizations = await this.getMenuItemCustomizations(item.id);

      return {
        id: item.id,
        name: item.name,
        description: item.description,
        price: parseFloat(item.price),
        image: item.image_url,
        categoryId: item.category_id,
        available: item.is_available,
        popular: false, // TODO: Implement popularity logic based on order frequency
        ingredients: item.allergens ? item.allergens.filter(a => !['Gluten', 'Dairy', 'Nuts'].includes(a)) : [],
        allergens: item.allergens ? item.allergens.filter(a => ['Gluten', 'Dairy', 'Nuts'].includes(a)) : [],
        preparationTime: item.prep_time,
        nutritionInfo: {
          calories: item.calories,
          protein: null, // TODO: Add nutrition fields to database
          carbs: null,
          fat: null
        },
        customizations,
        rating: 4.7, // TODO: Calculate from reviews table
        reviewCount: 124 // TODO: Count from reviews table
      };
    } catch (error) {
      throw error;
    }
  }

  // Search menu items
  static async searchMenuItems(searchParams) {
    const pool = databaseManager.getPool();

    try {
      const { q, categoryId, minPrice, maxPrice, dietary } = searchParams;

      let whereConditions = ['mi.is_available = true'];
      let queryParams = [];
      let paramIndex = 1;

      if (q) {
        whereConditions.push(`(mi.name ILIKE $${paramIndex} OR mi.description ILIKE $${paramIndex})`);
        queryParams.push(`%${q}%`);
        paramIndex++;
      }

      if (categoryId) {
        whereConditions.push(`mi.category_id = $${paramIndex}`);
        queryParams.push(categoryId);
        paramIndex++;
      }

      if (minPrice !== null) {
        whereConditions.push(`mi.price >= $${paramIndex}`);
        queryParams.push(minPrice);
        paramIndex++;
      }

      if (maxPrice !== null) {
        whereConditions.push(`mi.price <= $${paramIndex}`);
        queryParams.push(maxPrice);
        paramIndex++;
      }

      if (dietary.includes('vegetarian')) {
        whereConditions.push('mi.is_vegetarian = true');
      }

      if (dietary.includes('vegan')) {
        whereConditions.push('mi.is_vegan = true');
      }

      if (dietary.includes('gluten_free')) {
        whereConditions.push('mi.is_gluten_free = true');
      }

      const whereClause = whereConditions.join(' AND ');

      const query = `
        SELECT 
          mi.id, mi.name, mi.description, mi.price, mi.image_url,
          mi.category_id, mi.is_available
        FROM menu_items mi
        WHERE ${whereClause}
        ORDER BY mi.name ASC
        LIMIT 50
      `;

      const result = await pool.query(query, queryParams);

      return result.rows.map(item => ({
        id: item.id,
        name: item.name,
        description: item.description,
        price: parseFloat(item.price),
        image: item.image_url,
        categoryId: item.category_id,
        available: item.is_available,
        rating: 4.7 // TODO: Calculate from reviews table
      }));
    } catch (error) {
      throw error;
    }
  }

  // Helper method to get menu item customizations
  static async getMenuItemCustomizations(menuItemId) {
    const pool = databaseManager.getPool();

    try {
      const query = `
        SELECT
          mic.id, mic.name, mic.is_required, mic.sort_order,
          mico.id as option_id, mico.name as option_name,
          mico.price as option_price, mico.sort_order as option_sort_order
        FROM menu_item_customizations mic
        LEFT JOIN menu_item_customization_options mico ON mic.id = mico.customization_id
        WHERE mic.menu_item_id = $1
        ORDER BY mic.sort_order ASC, mico.sort_order ASC
      `;

      const result = await pool.query(query, [menuItemId]);

      // Group customizations and their options
      const customizationsMap = new Map();

      result.rows.forEach(row => {
        if (!customizationsMap.has(row.id)) {
          customizationsMap.set(row.id, {
            id: row.id,
            name: row.name,
            required: row.is_required,
            multiple: false, // TODO: Add multiple field to database
            options: []
          });
        }

        if (row.option_id) {
          customizationsMap.get(row.id).options.push({
            id: row.option_id,
            name: row.option_name,
            price: parseFloat(row.option_price || 0)
          });
        }
      });

      return Array.from(customizationsMap.values());
    } catch (error) {
      console.error('Error getting menu item customizations:', error);
      return [];
    }
  }
}

module.exports = MenuService;
