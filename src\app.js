// FoodWay Backend Application
const express = require('express');
const compression = require('compression');
const morgan = require('morgan');

// Import configurations and middleware
const config = require('./config/app');
const { helmet, cors } = require('./middleware/security');
const { notFound<PERSON>and<PERSON>, errorHandler } = require('./middleware/errorHandler');

// Import routes
const routes = require('./routes');

// Create Express application
const app = express();

// ================================
// MIDDLEWARE SETUP
// ================================

// Security middleware
app.use(helmet);
app.use(cors);

// Performance middleware
app.use(compression());

// Logging middleware
app.use(morgan(config.isDevelopment ? 'dev' : 'combined'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// ================================
// ROUTES
// ================================

// Mount all routes
app.use('/', routes);

// ================================
// ERROR HANDLING
// ================================

// 404 handler
app.use('*', notFoundHandler);

// Global error handler
app.use(errorHandler);

module.exports = app;
