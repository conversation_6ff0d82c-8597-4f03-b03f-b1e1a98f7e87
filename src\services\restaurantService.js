// Restaurant Service
const databaseManager = require('../config/database');

class RestaurantService {
  // Get restaurants with filtering and pagination
  static async getRestaurants(filters = {}) {
    const pool = databaseManager.getPool();
    const {
      page = 1,
      limit = 20,
      latitude,
      longitude,
      radius = 10000, // 10km default
      cuisine,
      rating,
      priceRange,
      deliveryFee,
      search,
      sortBy = 'distance',
      featured = false
    } = filters;

    try {
      let whereConditions = ['r.is_active = true'];
      let queryParams = [];
      let paramIndex = 1;

      // Location-based filtering
      let distanceSelect = '';
      let distanceOrderBy = '';
      if (latitude && longitude) {
        distanceSelect = `, calculate_distance(r.latitude, r.longitude, $${paramIndex}, $${paramIndex + 1}) as distance`;
        whereConditions.push(`calculate_distance(r.latitude, r.longitude, $${paramIndex}, $${paramIndex + 1}) <= $${paramIndex + 2}`);
        queryParams.push(parseFloat(latitude), parseFloat(longitude), parseFloat(radius) / 1000); // Convert meters to km
        paramIndex += 3;
        distanceOrderBy = 'distance ASC';
      }

      // Cuisine filter
      if (cuisine) {
        whereConditions.push(`r.cuisine_type ILIKE $${paramIndex}`);
        queryParams.push(`%${cuisine}%`);
        paramIndex++;
      }

      // Rating filter
      if (rating) {
        whereConditions.push(`r.rating >= $${paramIndex}`);
        queryParams.push(parseFloat(rating));
        paramIndex++;
      }

      // Price range filter
      if (priceRange) {
        whereConditions.push(`r.price_range <= $${paramIndex}`);
        queryParams.push(parseInt(priceRange));
        paramIndex++;
      }

      // Delivery fee filter
      if (deliveryFee) {
        whereConditions.push(`r.delivery_fee <= $${paramIndex}`);
        queryParams.push(parseFloat(deliveryFee));
        paramIndex++;
      }

      // Search filter
      if (search) {
        whereConditions.push(`(r.name ILIKE $${paramIndex} OR r.description ILIKE $${paramIndex} OR r.cuisine_type ILIKE $${paramIndex})`);
        queryParams.push(`%${search}%`);
        paramIndex++;
      }

      // Featured filter
      if (featured) {
        whereConditions.push('r.is_featured = true');
      }

      // Sorting
      let orderBy = '';
      switch (sortBy) {
        case 'rating':
          orderBy = 'r.rating DESC, r.review_count DESC';
          break;
        case 'deliveryTime':
          orderBy = 'r.delivery_time_min ASC';
          break;
        case 'popularity':
          orderBy = 'r.review_count DESC, r.rating DESC';
          break;
        case 'distance':
        default:
          orderBy = distanceOrderBy || 'r.created_at DESC';
          break;
      }

      // Calculate offset
      const offset = (page - 1) * limit;

      // Build the main query
      const query = `
        SELECT 
          r.id, r.name, r.description, r.cuisine_type, r.logo_url, r.cover_image_url,
          r.street_address, r.city, r.state, r.rating, r.review_count, r.price_range,
          r.delivery_fee, r.minimum_order, r.delivery_time_min, r.delivery_time_max,
          r.is_featured ${distanceSelect}
        FROM restaurants r
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY ${orderBy}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      queryParams.push(limit, offset);

      // Get total count for pagination
      const countQuery = `
        SELECT COUNT(*) as total
        FROM restaurants r
        WHERE ${whereConditions.join(' AND ')}
      `;

      const [restaurantsResult, countResult] = await Promise.all([
        pool.query(query, queryParams),
        pool.query(countQuery, queryParams.slice(0, -2)) // Remove limit and offset for count
      ]);

      const restaurants = restaurantsResult.rows.map(restaurant => ({
        id: restaurant.id,
        name: restaurant.name,
        description: restaurant.description,
        cuisineType: restaurant.cuisine_type,
        logoUrl: restaurant.logo_url,
        coverImageUrl: restaurant.cover_image_url,
        rating: restaurant.rating ? parseFloat(restaurant.rating) : 0,
        reviewCount: restaurant.review_count,
        priceRange: restaurant.price_range,
        deliveryFee: restaurant.delivery_fee ? parseFloat(restaurant.delivery_fee) : 0,
        minimumOrder: restaurant.minimum_order ? parseFloat(restaurant.minimum_order) : 0,
        deliveryTimeMin: restaurant.delivery_time_min,
        deliveryTimeMax: restaurant.delivery_time_max,
        distance: restaurant.distance ? parseFloat(restaurant.distance) : null,
        isFeatured: restaurant.is_featured,
        address: {
          streetAddress: restaurant.street_address,
          city: restaurant.city,
          state: restaurant.state
        }
      }));

      const total = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / limit);

      return {
        restaurants,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get restaurant details with menu
  static async getRestaurantById(restaurantId) {
    const pool = databaseManager.getPool();

    try {
      // Get restaurant details
      const restaurantQuery = `
        SELECT 
          r.id, r.name, r.description, r.cuisine_type, r.phone, r.email, r.website_url,
          r.logo_url, r.cover_image_url, r.street_address, r.city, r.state, r.postal_code,
          r.latitude, r.longitude, r.rating, r.review_count, r.price_range,
          r.delivery_fee, r.minimum_order, r.delivery_time_min, r.delivery_time_max,
          r.is_featured, r.created_at
        FROM restaurants r
        WHERE r.id = $1 AND r.is_active = true
      `;

      const restaurantResult = await pool.query(restaurantQuery, [restaurantId]);

      if (restaurantResult.rows.length === 0) {
        return null;
      }

      const restaurant = restaurantResult.rows[0];

      // Get menu categories
      const categoriesQuery = `
        SELECT id, name, description, sort_order
        FROM menu_categories
        WHERE restaurant_id = $1 AND is_active = true
        ORDER BY sort_order ASC, name ASC
      `;

      const categoriesResult = await pool.query(categoriesQuery, [restaurantId]);

      // Get menu items for each category
      const menuCategories = [];
      for (const category of categoriesResult.rows) {
        const itemsQuery = `
          SELECT
            id, name, description, price, image_url, is_vegetarian, is_vegan,
            is_gluten_free, allergens, calories, prep_time, is_available, sort_order
          FROM menu_items
          WHERE restaurant_id = $1 AND category_id = $2 AND is_available = true
          ORDER BY sort_order ASC, name ASC
        `;

        const itemsResult = await pool.query(itemsQuery, [restaurantId, category.id]);

        const items = itemsResult.rows.map(item => ({
          id: item.id,
          name: item.name,
          description: item.description,
          price: parseFloat(item.price),
          imageUrl: item.image_url,
          isVegetarian: item.is_vegetarian,
          isVegan: item.is_vegan,
          isGlutenFree: item.is_gluten_free,
          allergens: item.allergens || [],
          calories: item.calories,
          prepTime: item.prep_time,
          isAvailable: item.is_available,
          customizations: [], // TODO: Implement customizations
          addOns: [] // TODO: Implement add-ons
        }));

        menuCategories.push({
          id: category.id,
          name: category.name,
          description: category.description,
          sortOrder: category.sort_order,
          items
        });
      }

      return {
        restaurant: {
          id: restaurant.id,
          name: restaurant.name,
          description: restaurant.description,
          cuisineType: restaurant.cuisine_type,
          phone: restaurant.phone,
          email: restaurant.email,
          websiteUrl: restaurant.website_url,
          logoUrl: restaurant.logo_url,
          coverImageUrl: restaurant.cover_image_url,
          address: {
            streetAddress: restaurant.street_address,
            city: restaurant.city,
            state: restaurant.state,
            postalCode: restaurant.postal_code,
            latitude: restaurant.latitude ? parseFloat(restaurant.latitude) : null,
            longitude: restaurant.longitude ? parseFloat(restaurant.longitude) : null
          },
          rating: restaurant.rating ? parseFloat(restaurant.rating) : 0,
          reviewCount: restaurant.review_count,
          priceRange: restaurant.price_range,
          deliveryFee: restaurant.delivery_fee ? parseFloat(restaurant.delivery_fee) : 0,
          minimumOrder: restaurant.minimum_order ? parseFloat(restaurant.minimum_order) : 0,
          deliveryTimeMin: restaurant.delivery_time_min,
          deliveryTimeMax: restaurant.delivery_time_max,
          isFeatured: restaurant.is_featured,
          menu: {
            categories: menuCategories
          }
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get featured restaurants
  static async getFeaturedRestaurants(limit = 10) {
    return this.getRestaurants({ featured: true, limit });
  }

  // Get nearby restaurants
  static async getNearbyRestaurants(latitude, longitude, radius = 10000, limit = 20) {
    return this.getRestaurants({ latitude, longitude, radius, limit });
  }

  // Get main restaurant information (for single restaurant app)
  static async getRestaurantInfo() {
    const pool = databaseManager.getPool();

    try {
      // For now, get the first active restaurant (assuming single restaurant app)
      const query = `
        SELECT
          id, name, description, cuisine_type, phone, email, website_url,
          logo_url, cover_image_url, street_address, city, state, postal_code,
          country, latitude, longitude, rating, review_count, price_range,
          delivery_fee, minimum_order, delivery_time_min, delivery_time_max,
          delivery_radius, is_open, is_featured, is_active
        FROM restaurants
        WHERE is_active = true
        ORDER BY created_at ASC
        LIMIT 1
      `;

      const result = await pool.query(query);

      if (result.rows.length === 0) {
        throw new Error('Restaurant not found');
      }

      const restaurant = result.rows[0];

      return {
        id: restaurant.id,
        name: restaurant.name,
        description: restaurant.description,
        image: restaurant.cover_image_url,
        logo: restaurant.logo_url,
        rating: restaurant.rating ? parseFloat(restaurant.rating) : 0,
        reviewCount: restaurant.review_count || 0,
        cuisineTypes: restaurant.cuisine_type ? [restaurant.cuisine_type] : [],
        address: {
          street: restaurant.street_address,
          city: restaurant.city,
          state: restaurant.state,
          zipCode: restaurant.postal_code,
          country: restaurant.country,
          latitude: restaurant.latitude ? parseFloat(restaurant.latitude) : null,
          longitude: restaurant.longitude ? parseFloat(restaurant.longitude) : null
        },
        phone: restaurant.phone,
        email: restaurant.email,
        website: restaurant.website_url,
        openingHours: {
          monday: { open: "11:00", close: "22:00" },
          tuesday: { open: "11:00", close: "22:00" },
          wednesday: { open: "11:00", close: "22:00" },
          thursday: { open: "11:00", close: "22:00" },
          friday: { open: "11:00", close: "23:00" },
          saturday: { open: "10:00", close: "23:00" },
          sunday: { open: "10:00", close: "21:00" }
        },
        deliveryFee: restaurant.delivery_fee ? parseFloat(restaurant.delivery_fee) : 0,
        minimumOrder: restaurant.minimum_order ? parseFloat(restaurant.minimum_order) : 0,
        maxDeliveryDistance: restaurant.delivery_radius ? parseFloat(restaurant.delivery_radius) / 1000 : 10,
        estimatedDeliveryTime: `${restaurant.delivery_time_min}-${restaurant.delivery_time_max} min`,
        acceptingOrders: restaurant.is_open,
        features: ["delivery", "pickup", "dine_in"]
      };
    } catch (error) {
      throw error;
    }
  }

  // Get restaurant availability status
  static async getRestaurantAvailability() {
    const pool = databaseManager.getPool();

    try {
      // Get the first active restaurant
      const restaurantQuery = `
        SELECT id, is_open, delivery_time_min, delivery_time_max, name
        FROM restaurants
        WHERE is_active = true
        ORDER BY created_at ASC
        LIMIT 1
      `;

      const restaurantResult = await pool.query(restaurantQuery);

      if (restaurantResult.rows.length === 0) {
        throw new Error('Restaurant not found');
      }

      const restaurant = restaurantResult.rows[0];
      const now = new Date();
      const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

      // Check for special hours first (holidays, exceptions)
      const specialHoursQuery = `
        SELECT open_time, close_time, is_closed, reason
        FROM restaurant_special_hours
        WHERE restaurant_id = $1 AND date = CURRENT_DATE
      `;

      const specialHoursResult = await pool.query(specialHoursQuery, [restaurant.id]);

      let isWithinBusinessHours = false;
      let nextOpenTime = null;
      let businessHours = null;

      if (specialHoursResult.rows.length > 0) {
        // Use special hours
        const specialHours = specialHoursResult.rows[0];
        if (specialHours.is_closed) {
          isWithinBusinessHours = false;
          nextOpenTime = await this.getNextOpenTime(restaurant.id);
        } else {
          const openTime = specialHours.open_time;
          const closeTime = specialHours.close_time;
          isWithinBusinessHours = currentTime >= openTime && currentTime <= closeTime;
          businessHours = { open: openTime, close: closeTime };
        }
      } else {
        // Use regular business hours
        const businessHoursQuery = `
          SELECT open_time, close_time, is_closed
          FROM restaurant_business_hours
          WHERE restaurant_id = $1 AND day_of_week = $2
        `;

        const businessHoursResult = await pool.query(businessHoursQuery, [restaurant.id, currentDay]);

        if (businessHoursResult.rows.length > 0) {
          const hours = businessHoursResult.rows[0];
          if (hours.is_closed) {
            isWithinBusinessHours = false;
            nextOpenTime = await this.getNextOpenTime(restaurant.id);
          } else {
            const openTime = hours.open_time;
            const closeTime = hours.close_time;
            isWithinBusinessHours = currentTime >= openTime && currentTime <= closeTime;
            businessHours = { open: openTime, close: closeTime };

            if (!isWithinBusinessHours) {
              nextOpenTime = await this.getNextOpenTime(restaurant.id);
            }
          }
        } else {
          // Fallback to default hours if no business hours set
          const defaultOpen = currentDay === 0 ? '10:00' : (currentDay >= 5 ? '10:00' : '11:00');
          const defaultClose = currentDay === 0 ? '21:00' : (currentDay >= 5 ? '23:00' : '22:00');
          isWithinBusinessHours = currentTime >= defaultOpen && currentTime <= defaultClose;
          businessHours = { open: defaultOpen, close: defaultClose };
        }
      }

      // Calculate busy level based on current orders
      const busyLevel = await this.calculateBusyLevel(restaurant.id);

      return {
        isOpen: restaurant.is_open && isWithinBusinessHours,
        acceptingOrders: restaurant.is_open && isWithinBusinessHours,
        nextOpenTime,
        estimatedDeliveryTime: `${restaurant.delivery_time_min}-${restaurant.delivery_time_max} min`,
        busyLevel,
        businessHours
      };
    } catch (error) {
      throw error;
    }
  }

  // Helper method to get next open time
  static async getNextOpenTime(restaurantId) {
    const pool = databaseManager.getPool();

    try {
      // Look for the next open day within the next 7 days
      for (let i = 1; i <= 7; i++) {
        const checkDate = new Date();
        checkDate.setDate(checkDate.getDate() + i);
        const dayOfWeek = checkDate.getDay();

        // Check special hours first
        const specialQuery = `
          SELECT open_time, is_closed
          FROM restaurant_special_hours
          WHERE restaurant_id = $1 AND date = $2
        `;

        const specialResult = await pool.query(specialQuery, [restaurantId, checkDate.toISOString().split('T')[0]]);

        if (specialResult.rows.length > 0) {
          const special = specialResult.rows[0];
          if (!special.is_closed) {
            return special.open_time;
          }
        } else {
          // Check regular business hours
          const hoursQuery = `
            SELECT open_time, is_closed
            FROM restaurant_business_hours
            WHERE restaurant_id = $1 AND day_of_week = $2
          `;

          const hoursResult = await pool.query(hoursQuery, [restaurantId, dayOfWeek]);

          if (hoursResult.rows.length > 0 && !hoursResult.rows[0].is_closed) {
            return hoursResult.rows[0].open_time;
          }
        }
      }

      return null; // No open times found in next 7 days
    } catch (error) {
      console.error('Error getting next open time:', error);
      return null;
    }
  }

  // Helper method to calculate busy level
  static async calculateBusyLevel(restaurantId) {
    const pool = databaseManager.getPool();

    try {
      // Count active orders in the last hour
      const activeOrdersQuery = `
        SELECT COUNT(*) as count
        FROM orders
        WHERE restaurant_id = $1
        AND status IN ('pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery')
        AND created_at >= NOW() - INTERVAL '1 hour'
      `;

      const result = await pool.query(activeOrdersQuery, [restaurantId]);
      const activeOrders = parseInt(result.rows[0].count);

      if (activeOrders >= 20) return 'very_busy';
      if (activeOrders >= 10) return 'busy';
      if (activeOrders >= 5) return 'moderate';
      return 'quiet';

    } catch (error) {
      console.error('Error calculating busy level:', error);
      return 'moderate';
    }
  }
}

module.exports = RestaurantService;
