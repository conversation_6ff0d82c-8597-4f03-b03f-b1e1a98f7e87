// Categories Routes
const express = require('express');
const CategoryController = require('../controllers/categoryController');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting to all routes
router.use(apiLimiter);

// Public routes (no authentication required)

// GET /api/v1/categories - Get all categories
router.get('/',
  CategoryController.getCategories
);

// GET /api/v1/categories/popular - Get popular categories
router.get('/popular',
  CategoryController.getPopularCategories
);

// GET /api/v1/categories/:id - Get category by ID with menu items
router.get('/:id',
  ValidationMiddleware.validateUUID('id'),
  CategoryController.getCategoryById
);

// GET /api/v1/categories/restaurant/:restaurantId - Get categories by restaurant
router.get('/restaurant/:restaurantId',
  ValidationMiddleware.validateUUID('restaurantId'),
  CategoryController.getCategoriesByRestaurant
);

module.exports = router;
