// OrderStatusHistory Model
const BaseModel = require('./BaseModel');

class OrderStatusHistory extends BaseModel {
  constructor() {
    super('order_status_history');
  }

  // Get order status history
  async getOrderHistory(orderId) {
    try {
      const query = `
        SELECT 
          osh.*,
          u.first_name,
          u.last_name
        FROM order_status_history osh
        LEFT JOIN users u ON osh.changed_by = u.id
        WHERE osh.order_id = $1
        ORDER BY osh.created_at ASC
      `;

      const result = await this.query(query, [orderId]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Add status history entry
  async addStatusHistory(orderId, status, note = null, changedBy = null) {
    try {
      const result = await this.query(
        'INSERT INTO order_status_history (order_id, status, note, changed_by) VALUES ($1, $2, $3, $4) RETURNING *',
        [orderId, status, note, changedBy]
      );

      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Get latest status for order
  async getLatestStatus(orderId) {
    try {
      const query = `
        SELECT *
        FROM order_status_history
        WHERE order_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `;

      const result = await this.query(query, [orderId]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  // Get status history with timeline
  async getOrderTimeline(orderId) {
    try {
      const query = `
        SELECT 
          osh.status,
          osh.note,
          osh.created_at,
          CASE 
            WHEN osh.changed_by IS NOT NULL THEN 
              CONCAT(u.first_name, ' ', u.last_name)
            ELSE 'System'
          END as changed_by_name
        FROM order_status_history osh
        LEFT JOIN users u ON osh.changed_by = u.id
        WHERE osh.order_id = $1
        ORDER BY osh.created_at ASC
      `;

      const result = await this.query(query, [orderId]);
      
      // Format timeline with status descriptions
      const timeline = result.rows.map(entry => ({
        ...entry,
        status_description: this.getStatusDescription(entry.status),
        is_completed: true // All history entries are completed
      }));

      return timeline;
    } catch (error) {
      throw error;
    }
  }

  // Get status description
  getStatusDescription(status) {
    const descriptions = {
      'pending': 'Order placed and awaiting confirmation',
      'confirmed': 'Order confirmed by restaurant',
      'preparing': 'Your order is being prepared',
      'ready': 'Order is ready for pickup/delivery',
      'out_for_delivery': 'Order is out for delivery',
      'delivered': 'Order has been delivered',
      'cancelled': 'Order has been cancelled'
    };

    return descriptions[status] || status;
  }

  // Get orders by status change date range
  async getOrdersByStatusDateRange(status, startDate, endDate, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      const query = `
        SELECT DISTINCT
          o.id,
          o.order_number,
          o.total_amount,
          o.created_at,
          r.name as restaurant_name,
          u.first_name,
          u.last_name,
          osh.created_at as status_changed_at
        FROM order_status_history osh
        JOIN orders o ON osh.order_id = o.id
        JOIN restaurants r ON o.restaurant_id = r.id
        JOIN users u ON o.user_id = u.id
        WHERE osh.status = $1
        AND osh.created_at >= $2
        AND osh.created_at <= $3
        ORDER BY osh.created_at DESC
        LIMIT $4 OFFSET $5
      `;

      const countQuery = `
        SELECT COUNT(DISTINCT osh.order_id) as count
        FROM order_status_history osh
        WHERE osh.status = $1
        AND osh.created_at >= $2
        AND osh.created_at <= $3
      `;

      const [results, countResult] = await Promise.all([
        this.query(query, [status, startDate, endDate, limit, offset]),
        this.query(countQuery, [status, startDate, endDate])
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        orders: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get status change statistics
  async getStatusChangeStats(days = 30) {
    try {
      const query = `
        SELECT 
          status,
          COUNT(*) as change_count,
          COUNT(DISTINCT order_id) as unique_orders
        FROM order_status_history
        WHERE created_at >= NOW() - INTERVAL '${days} days'
        GROUP BY status
        ORDER BY change_count DESC
      `;

      const result = await this.query(query);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Get average time between status changes
  async getAverageStatusTransitionTimes(days = 30) {
    try {
      const query = `
        WITH status_transitions AS (
          SELECT 
            order_id,
            status,
            created_at,
            LAG(status) OVER (PARTITION BY order_id ORDER BY created_at) as prev_status,
            LAG(created_at) OVER (PARTITION BY order_id ORDER BY created_at) as prev_time
          FROM order_status_history
          WHERE created_at >= NOW() - INTERVAL '${days} days'
        )
        SELECT 
          CONCAT(prev_status, ' -> ', status) as transition,
          COUNT(*) as transition_count,
          AVG(EXTRACT(EPOCH FROM (created_at - prev_time))/60) as avg_minutes
        FROM status_transitions
        WHERE prev_status IS NOT NULL
        GROUP BY prev_status, status
        HAVING COUNT(*) >= 5  -- Only show transitions with at least 5 occurrences
        ORDER BY transition_count DESC
      `;

      const result = await this.query(query);
      return result.rows.map(row => ({
        ...row,
        avg_minutes: Math.round(row.avg_minutes * 100) / 100 // Round to 2 decimal places
      }));
    } catch (error) {
      throw error;
    }
  }

  // Get orders stuck in status
  async getOrdersStuckInStatus(status, hoursThreshold = 2) {
    try {
      const query = `
        SELECT 
          o.id,
          o.order_number,
          o.created_at as order_created,
          osh.created_at as status_changed,
          EXTRACT(EPOCH FROM (NOW() - osh.created_at))/3600 as hours_in_status,
          r.name as restaurant_name,
          u.first_name,
          u.last_name
        FROM orders o
        JOIN order_status_history osh ON o.id = osh.order_id
        JOIN restaurants r ON o.restaurant_id = r.id
        JOIN users u ON o.user_id = u.id
        WHERE o.status = $1
        AND osh.status = $1
        AND osh.created_at = (
          SELECT MAX(created_at)
          FROM order_status_history
          WHERE order_id = o.id
        )
        AND osh.created_at < NOW() - INTERVAL '${hoursThreshold} hours'
        ORDER BY osh.created_at ASC
      `;

      const result = await this.query(query, [status]);
      return result.rows.map(row => ({
        ...row,
        hours_in_status: Math.round(row.hours_in_status * 100) / 100
      }));
    } catch (error) {
      throw error;
    }
  }

  // Clean up old status history (keep last 6 months)
  async cleanupOldHistory(monthsToKeep = 6) {
    try {
      const result = await this.query(
        `DELETE FROM order_status_history 
         WHERE created_at < NOW() - INTERVAL '${monthsToKeep} months'
         RETURNING COUNT(*) as deleted_count`
      );

      return {
        deletedCount: result.rowCount,
        message: `Cleaned up status history older than ${monthsToKeep} months`
      };
    } catch (error) {
      throw error;
    }
  }

  // Get status history for multiple orders
  async getBulkOrderHistory(orderIds) {
    try {
      if (!Array.isArray(orderIds) || orderIds.length === 0) {
        return {};
      }

      const placeholders = orderIds.map((_, index) => `$${index + 1}`).join(',');
      
      const query = `
        SELECT 
          osh.order_id,
          osh.status,
          osh.note,
          osh.created_at,
          CASE 
            WHEN osh.changed_by IS NOT NULL THEN 
              CONCAT(u.first_name, ' ', u.last_name)
            ELSE 'System'
          END as changed_by_name
        FROM order_status_history osh
        LEFT JOIN users u ON osh.changed_by = u.id
        WHERE osh.order_id IN (${placeholders})
        ORDER BY osh.order_id, osh.created_at ASC
      `;

      const result = await this.query(query, orderIds);
      
      // Group by order_id
      const historyByOrder = {};
      result.rows.forEach(row => {
        if (!historyByOrder[row.order_id]) {
          historyByOrder[row.order_id] = [];
        }
        historyByOrder[row.order_id].push({
          status: row.status,
          note: row.note,
          created_at: row.created_at,
          changed_by_name: row.changed_by_name,
          status_description: this.getStatusDescription(row.status)
        });
      });

      return historyByOrder;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = OrderStatusHistory;
