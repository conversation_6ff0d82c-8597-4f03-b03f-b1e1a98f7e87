// Database Configuration and Connection Management
require('dotenv').config();
const { Pool } = require('pg');

class DatabaseManager {
  constructor() {
    this.pool = null;
  }

  async initialize() {
    if (!process.env.DATABASE_URL) {
      console.log('⚠️  DATABASE_URL not found in environment variables');
      return false;
    }

    try {
      this.pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
        // Optimized connection pool settings for Railway
        max: process.env.NODE_ENV === 'production' ? 25 : 10, // More connections in production
        min: 2, // Keep minimum connections alive
        idleTimeoutMillis: 60000, // Keep connections alive longer (1 minute)
        connectionTimeoutMillis: 10000, // Longer timeout for Railway (10 seconds)
        acquireTimeoutMillis: 15000, // Time to wait for connection from pool
        createTimeoutMillis: 10000, // Time to wait for new connection creation
        destroyTimeoutMillis: 5000, // Time to wait for connection destruction
        reapIntervalMillis: 1000, // Check for idle connections every second
        createRetryIntervalMillis: 200, // Retry connection creation every 200ms
        // Performance optimizations
        statement_timeout: 30000, // 30 second query timeout
        query_timeout: 25000, // 25 second individual query timeout
        application_name: 'FoodWay-Backend'
      });

      // Test the connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();

      console.log('✅ PostgreSQL connected successfully');
      return true;
    } catch (error) {
      console.error('❌ PostgreSQL connection failed:', error.message);
      return false;
    }
  }

  getPool() {
    return this.pool;
  }

  async close() {
    if (this.pool) {
      await this.pool.end();
      console.log('✅ PostgreSQL pool closed');
    }
  }

  async query(text, params) {
    if (!this.pool) {
      throw new Error('Database not initialized');
    }
    return this.pool.query(text, params);
  }

  async getClient() {
    if (!this.pool) {
      throw new Error('Database not initialized');
    }
    return this.pool.connect();
  }
}

// Export singleton instance
const databaseManager = new DatabaseManager();
module.exports = databaseManager;
