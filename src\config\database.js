// Database Configuration and Connection Management
require('dotenv').config();
const { Pool } = require('pg');

class DatabaseManager {
  constructor() {
    this.pool = null;
  }

  async initialize() {
    if (!process.env.DATABASE_URL) {
      console.log('⚠️  DATABASE_URL not found in environment variables');
      return false;
    }

    try {
      this.pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 5000,
      });

      // Test the connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();

      console.log('✅ PostgreSQL connected successfully');
      return true;
    } catch (error) {
      console.error('❌ PostgreSQL connection failed:', error.message);
      return false;
    }
  }

  getPool() {
    return this.pool;
  }

  async close() {
    if (this.pool) {
      await this.pool.end();
      console.log('✅ PostgreSQL pool closed');
    }
  }

  async query(text, params) {
    if (!this.pool) {
      throw new Error('Database not initialized');
    }
    return this.pool.query(text, params);
  }

  async getClient() {
    if (!this.pool) {
      throw new Error('Database not initialized');
    }
    return this.pool.connect();
  }
}

// Export singleton instance
const databaseManager = new DatabaseManager();
module.exports = databaseManager;
