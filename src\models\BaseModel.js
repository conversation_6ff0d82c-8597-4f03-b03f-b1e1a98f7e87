// Base Model Class
const databaseManager = require('../config/database');

class BaseModel {
  constructor(tableName) {
    this.tableName = tableName;
    this.pool = databaseManager.getPool();
  }

  // Find record by ID
  async findById(id) {
    try {
      const query = `SELECT * FROM ${this.tableName} WHERE id = $1`;
      const result = await this.pool.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  // Find all records with optional conditions
  async findAll(conditions = {}, orderBy = 'created_at DESC', limit = null, offset = null) {
    try {
      let query = `SELECT * FROM ${this.tableName}`;
      const params = [];
      let paramIndex = 1;

      // Add WHERE conditions
      if (Object.keys(conditions).length > 0) {
        const whereConditions = [];
        for (const [key, value] of Object.entries(conditions)) {
          whereConditions.push(`${key} = $${paramIndex}`);
          params.push(value);
          paramIndex++;
        }
        query += ` WHERE ${whereConditions.join(' AND ')}`;
      }

      // Add ORDER BY
      if (orderBy) {
        query += ` ORDER BY ${orderBy}`;
      }

      // Add LIMIT
      if (limit) {
        query += ` LIMIT $${paramIndex}`;
        params.push(limit);
        paramIndex++;
      }

      // Add OFFSET
      if (offset) {
        query += ` OFFSET $${paramIndex}`;
        params.push(offset);
      }

      const result = await this.pool.query(query, params);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Find one record with conditions
  async findOne(conditions = {}) {
    try {
      const results = await this.findAll(conditions, null, 1);
      return results[0] || null;
    } catch (error) {
      throw error;
    }
  }

  // Create new record
  async create(data) {
    try {
      const keys = Object.keys(data);
      const values = Object.values(data);
      const placeholders = keys.map((_, index) => `$${index + 1}`).join(', ');
      
      const query = `
        INSERT INTO ${this.tableName} (${keys.join(', ')})
        VALUES (${placeholders})
        RETURNING *
      `;

      const result = await this.pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Update record by ID
  async updateById(id, data) {
    try {
      const keys = Object.keys(data);
      const values = Object.values(data);
      const setClause = keys.map((key, index) => `${key} = $${index + 2}`).join(', ');
      
      const query = `
        UPDATE ${this.tableName}
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `;

      const result = await this.pool.query(query, [id, ...values]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  // Delete record by ID
  async deleteById(id) {
    try {
      const query = `DELETE FROM ${this.tableName} WHERE id = $1 RETURNING *`;
      const result = await this.pool.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  // Count records with optional conditions
  async count(conditions = {}) {
    try {
      let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
      const params = [];
      let paramIndex = 1;

      // Add WHERE conditions
      if (Object.keys(conditions).length > 0) {
        const whereConditions = [];
        for (const [key, value] of Object.entries(conditions)) {
          whereConditions.push(`${key} = $${paramIndex}`);
          params.push(value);
          paramIndex++;
        }
        query += ` WHERE ${whereConditions.join(' AND ')}`;
      }

      const result = await this.pool.query(query, params);
      return parseInt(result.rows[0].count);
    } catch (error) {
      throw error;
    }
  }

  // Check if record exists
  async exists(conditions) {
    try {
      const count = await this.count(conditions);
      return count > 0;
    } catch (error) {
      throw error;
    }
  }

  // Execute raw query
  async query(sql, params = []) {
    try {
      const result = await this.pool.query(sql, params);
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Begin transaction
  async beginTransaction() {
    const client = await this.pool.connect();
    await client.query('BEGIN');
    return client;
  }

  // Commit transaction
  async commitTransaction(client) {
    await client.query('COMMIT');
    client.release();
  }

  // Rollback transaction
  async rollbackTransaction(client) {
    await client.query('ROLLBACK');
    client.release();
  }

  // Paginate results
  async paginate(page = 1, limit = 10, conditions = {}, orderBy = 'created_at DESC') {
    try {
      const offset = (page - 1) * limit;
      const [results, total] = await Promise.all([
        this.findAll(conditions, orderBy, limit, offset),
        this.count(conditions)
      ]);

      return {
        data: results,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = BaseModel;
