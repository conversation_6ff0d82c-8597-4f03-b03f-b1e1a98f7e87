// UserAddress Model
const BaseModel = require('./BaseModel');

class UserAddress extends BaseModel {
  constructor() {
    super('user_addresses');
  }

  // Get addresses by user
  async getAddressesByUser(userId, activeOnly = true) {
    try {
      const conditions = { user_id: userId };
      if (activeOnly) {
        conditions.is_active = true;
      }

      return await this.findAll(conditions, 'is_default DESC, created_at DESC');
    } catch (error) {
      throw error;
    }
  }

  // Get user's default address
  async getDefaultAddress(userId) {
    try {
      return await this.findOne({ 
        user_id: userId, 
        is_default: true, 
        is_active: true 
      });
    } catch (error) {
      throw error;
    }
  }

  // Create address and handle default logic
  async createAddress(addressData) {
    try {
      const client = await this.beginTransaction();

      try {
        // If this is set as default, unset other defaults for this user
        if (addressData.is_default) {
          await client.query(
            'UPDATE user_addresses SET is_default = false WHERE user_id = $1',
            [addressData.user_id]
          );
        }

        // If this is the user's first address, make it default
        const existingCount = await client.query(
          'SELECT COUNT(*) as count FROM user_addresses WHERE user_id = $1 AND is_active = true',
          [addressData.user_id]
        );

        const isFirstAddress = parseInt(existingCount.rows[0].count) === 0;
        if (isFirstAddress) {
          addressData.is_default = true;
        }

        // Create the address
        const result = await client.query(`
          INSERT INTO user_addresses (
            user_id, label, street, apartment, city, state, zip_code, country,
            latitude, longitude, delivery_instructions, is_default, is_active
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
          RETURNING *
        `, [
          addressData.user_id,
          addressData.label,
          addressData.street,
          addressData.apartment || null,
          addressData.city,
          addressData.state,
          addressData.zip_code,
          addressData.country || 'USA',
          addressData.latitude || null,
          addressData.longitude || null,
          addressData.delivery_instructions || null,
          addressData.is_default || false,
          true
        ]);

        await this.commitTransaction(client);
        return result.rows[0];
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Update address
  async updateAddress(addressId, userId, addressData) {
    try {
      const client = await this.beginTransaction();

      try {
        // Verify address belongs to user
        const existingAddress = await client.query(
          'SELECT * FROM user_addresses WHERE id = $1 AND user_id = $2',
          [addressId, userId]
        );

        if (existingAddress.rows.length === 0) {
          throw new Error('Address not found or access denied');
        }

        // If setting as default, unset other defaults
        if (addressData.is_default) {
          await client.query(
            'UPDATE user_addresses SET is_default = false WHERE user_id = $1 AND id != $2',
            [userId, addressId]
          );
        }

        // Update the address
        const updateFields = [];
        const updateValues = [];
        let paramIndex = 1;

        const allowedFields = [
          'label', 'street', 'apartment', 'city', 'state', 'zip_code', 'country',
          'latitude', 'longitude', 'delivery_instructions', 'is_default'
        ];

        for (const field of allowedFields) {
          if (addressData[field] !== undefined) {
            updateFields.push(`${field} = $${paramIndex}`);
            updateValues.push(addressData[field]);
            paramIndex++;
          }
        }

        if (updateFields.length === 0) {
          throw new Error('No valid fields to update');
        }

        updateFields.push(`updated_at = NOW()`);
        updateValues.push(addressId);

        const query = `
          UPDATE user_addresses 
          SET ${updateFields.join(', ')}
          WHERE id = $${paramIndex}
          RETURNING *
        `;

        const result = await client.query(query, updateValues);

        await this.commitTransaction(client);
        return result.rows[0];
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Set default address
  async setDefaultAddress(addressId, userId) {
    try {
      const client = await this.beginTransaction();

      try {
        // Verify address belongs to user
        const existingAddress = await client.query(
          'SELECT * FROM user_addresses WHERE id = $1 AND user_id = $2 AND is_active = true',
          [addressId, userId]
        );

        if (existingAddress.rows.length === 0) {
          throw new Error('Address not found or access denied');
        }

        // Unset all defaults for this user
        await client.query(
          'UPDATE user_addresses SET is_default = false WHERE user_id = $1',
          [userId]
        );

        // Set new default
        await client.query(
          'UPDATE user_addresses SET is_default = true WHERE id = $1',
          [addressId]
        );

        await this.commitTransaction(client);
        return true;
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Soft delete address
  async deleteAddress(addressId, userId) {
    try {
      const client = await this.beginTransaction();

      try {
        // Verify address belongs to user
        const existingAddress = await client.query(
          'SELECT * FROM user_addresses WHERE id = $1 AND user_id = $2',
          [addressId, userId]
        );

        if (existingAddress.rows.length === 0) {
          throw new Error('Address not found or access denied');
        }

        const address = existingAddress.rows[0];

        // Soft delete the address
        await client.query(
          'UPDATE user_addresses SET is_active = false WHERE id = $1',
          [addressId]
        );

        // If this was the default address, set another one as default
        if (address.is_default) {
          const nextAddress = await client.query(
            'SELECT id FROM user_addresses WHERE user_id = $1 AND is_active = true ORDER BY created_at DESC LIMIT 1',
            [userId]
          );

          if (nextAddress.rows.length > 0) {
            await client.query(
              'UPDATE user_addresses SET is_default = true WHERE id = $1',
              [nextAddress.rows[0].id]
            );
          }
        }

        await this.commitTransaction(client);
        return true;
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Validate address for delivery
  async validateDeliveryAddress(addressData) {
    try {
      // Basic validation
      const requiredFields = ['street', 'city', 'state', 'zip_code'];
      for (const field of requiredFields) {
        if (!addressData[field] || addressData[field].trim() === '') {
          throw new Error(`${field} is required`);
        }
      }

      // Validate coordinates if provided
      if (addressData.latitude && addressData.longitude) {
        const lat = parseFloat(addressData.latitude);
        const lng = parseFloat(addressData.longitude);

        if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
          throw new Error('Invalid coordinates provided');
        }
      }

      return {
        isValid: true,
        estimatedDeliveryTime: '25-35 min', // This would be calculated based on restaurant location
        deliveryFee: 3.99 // This would be calculated based on distance
      };
    } catch (error) {
      return {
        isValid: false,
        error: error.message
      };
    }
  }

  // Calculate distance between address and restaurant
  async calculateDeliveryDistance(addressId, restaurantId) {
    try {
      const query = `
        SELECT 
          ua.latitude as addr_lat,
          ua.longitude as addr_lng,
          (r.address->>'latitude')::float as rest_lat,
          (r.address->>'longitude')::float as rest_lng
        FROM user_addresses ua
        CROSS JOIN restaurants r
        WHERE ua.id = $1 AND r.id = $2
      `;

      const result = await this.query(query, [addressId, restaurantId]);
      
      if (result.rows.length === 0) {
        throw new Error('Address or restaurant not found');
      }

      const { addr_lat, addr_lng, rest_lat, rest_lng } = result.rows[0];

      if (!addr_lat || !addr_lng || !rest_lat || !rest_lng) {
        throw new Error('Coordinates not available for distance calculation');
      }

      // Calculate distance using Haversine formula
      const R = 6371; // Earth's radius in kilometers
      const dLat = (rest_lat - addr_lat) * Math.PI / 180;
      const dLng = (rest_lng - addr_lng) * Math.PI / 180;
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(addr_lat * Math.PI / 180) * Math.cos(rest_lat * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      const distance = R * c;

      return {
        distance: Math.round(distance * 100) / 100, // Round to 2 decimal places
        unit: 'km'
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = UserAddress;
