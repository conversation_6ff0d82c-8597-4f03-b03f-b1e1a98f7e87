// Database Seeding Script
require('dotenv').config();
const { Pool } = require('pg');

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  // Initialize database connection
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {

    // Check if restaurants already exist
    const existingRestaurants = await pool.query('SELECT COUNT(*) FROM restaurants');
    if (parseInt(existingRestaurants.rows[0].count) > 0) {
      console.log('ℹ️  Database already has restaurant data, skipping seed...');
      return;
    }

    console.log('🏪 Seeding restaurants...');

    // Insert sample restaurants
    const restaurants = [
      {
        name: "Mario's Pizza Palace",
        description: "Authentic Italian pizza made with fresh ingredients and traditional recipes",
        cuisine_type: "Italian",
        phone: "+1234567890",
        email: "<EMAIL>",
        website_url: "https://mariospizza.com",
        logo_url: "/images/restaurants/marios-logo.jpg",
        cover_image_url: "/images/restaurants/marios-cover.jpg",
        street_address: "123 Main St",
        city: "New York",
        state: "NY",
        postal_code: "10001",
        latitude: 40.7128,
        longitude: -74.0060,
        rating: 4.5,
        review_count: 324,
        price_range: 2,
        delivery_fee: 2.99,
        minimum_order: 15.00,
        delivery_time_min: 25,
        delivery_time_max: 35,
        is_featured: true
      },
      {
        name: "Burger Junction",
        description: "Gourmet burgers made with premium beef and fresh toppings",
        cuisine_type: "American",
        phone: "+1234567891",
        email: "<EMAIL>",
        website_url: "https://burgerjunction.com",
        logo_url: "/images/restaurants/burger-junction-logo.jpg",
        cover_image_url: "/images/restaurants/burger-junction-cover.jpg",
        street_address: "456 Oak Ave",
        city: "New York",
        state: "NY",
        postal_code: "10002",
        latitude: 40.7589,
        longitude: -73.9851,
        rating: 4.2,
        review_count: 189,
        price_range: 2,
        delivery_fee: 1.99,
        minimum_order: 12.00,
        delivery_time_min: 20,
        delivery_time_max: 30,
        is_featured: false
      },
      {
        name: "Dragon Palace",
        description: "Authentic Chinese cuisine with traditional flavors and modern presentation",
        cuisine_type: "Chinese",
        phone: "+1234567892",
        email: "<EMAIL>",
        website_url: "https://dragonpalace.com",
        logo_url: "/images/restaurants/dragon-palace-logo.jpg",
        cover_image_url: "/images/restaurants/dragon-palace-cover.jpg",
        street_address: "789 Broadway",
        city: "New York",
        state: "NY",
        postal_code: "10003",
        latitude: 40.7505,
        longitude: -73.9934,
        rating: 4.7,
        review_count: 456,
        price_range: 3,
        delivery_fee: 3.99,
        minimum_order: 20.00,
        delivery_time_min: 30,
        delivery_time_max: 45,
        is_featured: true
      },
      {
        name: "Taco Fiesta",
        description: "Fresh Mexican street food with authentic spices and ingredients",
        cuisine_type: "Mexican",
        phone: "+1234567893",
        email: "<EMAIL>",
        website_url: "https://tacofiesta.com",
        logo_url: "/images/restaurants/taco-fiesta-logo.jpg",
        cover_image_url: "/images/restaurants/taco-fiesta-cover.jpg",
        street_address: "321 Park Ave",
        city: "New York",
        state: "NY",
        postal_code: "10004",
        latitude: 40.7282,
        longitude: -73.9942,
        rating: 4.3,
        review_count: 267,
        price_range: 1,
        delivery_fee: 2.49,
        minimum_order: 10.00,
        delivery_time_min: 15,
        delivery_time_max: 25,
        is_featured: false
      },
      {
        name: "Sushi Zen",
        description: "Fresh sushi and Japanese cuisine prepared by master chefs",
        cuisine_type: "Japanese",
        phone: "+1234567894",
        email: "<EMAIL>",
        website_url: "https://sushizen.com",
        logo_url: "/images/restaurants/sushi-zen-logo.jpg",
        cover_image_url: "/images/restaurants/sushi-zen-cover.jpg",
        street_address: "654 Fifth Ave",
        city: "New York",
        state: "NY",
        postal_code: "10005",
        latitude: 40.7614,
        longitude: -73.9776,
        rating: 4.8,
        review_count: 512,
        price_range: 4,
        delivery_fee: 4.99,
        minimum_order: 25.00,
        delivery_time_min: 35,
        delivery_time_max: 50,
        is_featured: true
      }
    ];

    for (const restaurant of restaurants) {
      const result = await pool.query(`
        INSERT INTO restaurants (
          name, description, cuisine_type, phone, email, website_url,
          logo_url, cover_image_url, street_address, city, state, postal_code,
          latitude, longitude, rating, review_count, price_range,
          delivery_fee, minimum_order, delivery_time_min, delivery_time_max, is_featured
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
        ) RETURNING id
      `, [
        restaurant.name, restaurant.description, restaurant.cuisine_type,
        restaurant.phone, restaurant.email, restaurant.website_url,
        restaurant.logo_url, restaurant.cover_image_url,
        restaurant.street_address, restaurant.city, restaurant.state, restaurant.postal_code,
        restaurant.latitude, restaurant.longitude, restaurant.rating, restaurant.review_count,
        restaurant.price_range, restaurant.delivery_fee, restaurant.minimum_order,
        restaurant.delivery_time_min, restaurant.delivery_time_max, restaurant.is_featured
      ]);

      const restaurantId = result.rows[0].id;
      console.log(`✅ Created restaurant: ${restaurant.name} (${restaurantId})`);

      // Add sample menu categories and items for each restaurant
      await seedMenuForRestaurant(pool, restaurantId, restaurant.name, restaurant.cuisine_type);
    }

    console.log('✅ Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

async function seedMenuForRestaurant(pool, restaurantId, restaurantName, cuisineType) {
  // Create menu categories based on cuisine type
  let categories = [];
  let menuItems = [];

  switch (cuisineType) {
    case 'Italian':
      categories = [
        { name: 'Pizzas', description: 'Our signature wood-fired pizzas' },
        { name: 'Pasta', description: 'Fresh homemade pasta dishes' },
        { name: 'Appetizers', description: 'Start your meal right' }
      ];
      menuItems = [
        { category: 'Pizzas', name: 'Margherita Pizza', description: 'Fresh mozzarella, tomato sauce, basil', price: 18.99, is_vegetarian: true },
        { category: 'Pizzas', name: 'Pepperoni Pizza', description: 'Classic pepperoni with mozzarella cheese', price: 21.99 },
        { category: 'Pasta', name: 'Spaghetti Carbonara', description: 'Creamy pasta with pancetta and parmesan', price: 16.99 },
        { category: 'Appetizers', name: 'Garlic Bread', description: 'Toasted bread with garlic butter', price: 6.99, is_vegetarian: true }
      ];
      break;

    case 'American':
      categories = [
        { name: 'Burgers', description: 'Gourmet burgers made fresh' },
        { name: 'Sides', description: 'Perfect accompaniments' },
        { name: 'Beverages', description: 'Refreshing drinks' }
      ];
      menuItems = [
        { category: 'Burgers', name: 'Classic Cheeseburger', description: 'Beef patty with cheese, lettuce, tomato', price: 14.99 },
        { category: 'Burgers', name: 'Bacon BBQ Burger', description: 'Beef patty with bacon and BBQ sauce', price: 17.99 },
        { category: 'Sides', name: 'French Fries', description: 'Crispy golden fries', price: 4.99, is_vegetarian: true },
        { category: 'Beverages', name: 'Soft Drink', description: 'Choice of cola, sprite, or orange', price: 2.99, is_vegetarian: true }
      ];
      break;

    case 'Chinese':
      categories = [
        { name: 'Main Dishes', description: 'Traditional Chinese entrees' },
        { name: 'Appetizers', description: 'Start with these favorites' },
        { name: 'Rice & Noodles', description: 'Staple dishes' }
      ];
      menuItems = [
        { category: 'Main Dishes', name: 'Sweet & Sour Chicken', description: 'Crispy chicken with sweet and sour sauce', price: 15.99 },
        { category: 'Main Dishes', name: 'Kung Pao Chicken', description: 'Spicy chicken with peanuts', price: 16.99 },
        { category: 'Appetizers', name: 'Spring Rolls', description: 'Crispy vegetable spring rolls', price: 7.99, is_vegetarian: true },
        { category: 'Rice & Noodles', name: 'Fried Rice', description: 'Wok-fried rice with vegetables', price: 12.99, is_vegetarian: true }
      ];
      break;

    case 'Mexican':
      categories = [
        { name: 'Tacos', description: 'Authentic street tacos' },
        { name: 'Burritos', description: 'Hearty wrapped meals' },
        { name: 'Sides', description: 'Perfect additions' }
      ];
      menuItems = [
        { category: 'Tacos', name: 'Carnitas Tacos', description: 'Slow-cooked pork with onions and cilantro', price: 3.99 },
        { category: 'Tacos', name: 'Chicken Tacos', description: 'Grilled chicken with fresh salsa', price: 3.49 },
        { category: 'Burritos', name: 'Beef Burrito', description: 'Seasoned beef with rice, beans, and cheese', price: 11.99 },
        { category: 'Sides', name: 'Guacamole & Chips', description: 'Fresh guacamole with tortilla chips', price: 6.99, is_vegetarian: true }
      ];
      break;

    case 'Japanese':
      categories = [
        { name: 'Sushi Rolls', description: 'Fresh sushi rolls' },
        { name: 'Sashimi', description: 'Premium fresh fish' },
        { name: 'Hot Dishes', description: 'Cooked Japanese specialties' }
      ];
      menuItems = [
        { category: 'Sushi Rolls', name: 'California Roll', description: 'Crab, avocado, and cucumber', price: 12.99 },
        { category: 'Sushi Rolls', name: 'Spicy Tuna Roll', description: 'Spicy tuna with cucumber', price: 14.99 },
        { category: 'Sashimi', name: 'Salmon Sashimi', description: 'Fresh Atlantic salmon (6 pieces)', price: 18.99 },
        { category: 'Hot Dishes', name: 'Chicken Teriyaki', description: 'Grilled chicken with teriyaki sauce', price: 16.99 }
      ];
      break;
  }

  // Insert categories
  const categoryMap = {};
  for (const category of categories) {
    const result = await pool.query(`
      INSERT INTO menu_categories (restaurant_id, name, description, sort_order)
      VALUES ($1, $2, $3, $4)
      RETURNING id
    `, [restaurantId, category.name, category.description, categories.indexOf(category)]);

    categoryMap[category.name] = result.rows[0].id;
  }

  // Insert menu items
  for (const item of menuItems) {
    const categoryId = categoryMap[item.category];
    await pool.query(`
      INSERT INTO menu_items (
        restaurant_id, category_id, name, description, price,
        is_vegetarian, is_vegan, is_gluten_free, is_available, sort_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
    `, [
      restaurantId, categoryId, item.name, item.description, item.price,
      item.is_vegetarian || false, item.is_vegan || false, item.is_gluten_free || false,
      true, menuItems.indexOf(item)
    ]);
  }

  console.log(`  📋 Added menu for ${restaurantName}`);
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('🎉 Seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedDatabase };
