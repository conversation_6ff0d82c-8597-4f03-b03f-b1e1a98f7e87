// Cache Service for Performance Optimization
const redisManager = require('../config/redis');
const config = require('../config/app');

class CacheService {
  constructor() {
    this.defaultTTL = 300; // 5 minutes default
    this.isEnabled = !config.isTest; // Disable caching in tests
  }

  // Generate cache key with prefix
  generateKey(prefix, ...parts) {
    return `foodway:${prefix}:${parts.join(':')}`;
  }

  // Get cached data
  async get(key) {
    if (!this.isEnabled || !redisManager.isReady()) {
      return null;
    }

    try {
      const cached = await redisManager.get(key);
      if (cached) {
        return JSON.parse(cached);
      }
      return null;
    } catch (error) {
      console.warn('Cache get error:', error.message);
      return null;
    }
  }

  // Set cached data
  async set(key, data, ttl = this.defaultTTL) {
    if (!this.isEnabled || !redisManager.isReady()) {
      return false;
    }

    try {
      await redisManager.set(key, JSON.stringify(data), { EX: ttl });
      return true;
    } catch (error) {
      console.warn('Cache set error:', error.message);
      return false;
    }
  }

  // Delete cached data
  async del(key) {
    if (!this.isEnabled || !redisManager.isReady()) {
      return false;
    }

    try {
      await redisManager.del(key);
      return true;
    } catch (error) {
      console.warn('Cache delete error:', error.message);
      return false;
    }
  }

  // Delete multiple keys by pattern
  async delPattern(pattern) {
    if (!this.isEnabled || !redisManager.isReady()) {
      return false;
    }

    try {
      const keys = await redisManager.keys(pattern);
      if (keys.length > 0) {
        await redisManager.del(keys);
      }
      return true;
    } catch (error) {
      console.warn('Cache delete pattern error:', error.message);
      return false;
    }
  }

  // Cache wrapper for database queries
  async cacheQuery(key, queryFunction, ttl = this.defaultTTL) {
    // Try to get from cache first
    const cached = await this.get(key);
    if (cached !== null) {
      return cached;
    }

    // Execute query and cache result
    try {
      const result = await queryFunction();
      await this.set(key, result, ttl);
      return result;
    } catch (error) {
      console.error('Cache query error:', error.message);
      throw error;
    }
  }

  // User-specific cache methods
  async getUserCache(userId, type) {
    const key = this.generateKey('user', userId, type);
    return await this.get(key);
  }

  async setUserCache(userId, type, data, ttl = 600) { // 10 minutes for user data
    const key = this.generateKey('user', userId, type);
    return await this.set(key, data, ttl);
  }

  async clearUserCache(userId, type = '*') {
    const pattern = this.generateKey('user', userId, type);
    return await this.delPattern(pattern);
  }

  // Restaurant-specific cache methods
  async getRestaurantCache(restaurantId, type) {
    const key = this.generateKey('restaurant', restaurantId, type);
    return await this.get(key);
  }

  async setRestaurantCache(restaurantId, type, data, ttl = 900) { // 15 minutes for restaurant data
    const key = this.generateKey('restaurant', restaurantId, type);
    return await this.set(key, data, ttl);
  }

  async clearRestaurantCache(restaurantId, type = '*') {
    const pattern = this.generateKey('restaurant', restaurantId, type);
    return await this.delPattern(pattern);
  }

  // Menu-specific cache methods
  async getMenuCache(restaurantId, categoryId = 'all') {
    const key = this.generateKey('menu', restaurantId, categoryId);
    return await this.get(key);
  }

  async setMenuCache(restaurantId, categoryId = 'all', data, ttl = 1800) { // 30 minutes for menu data
    const key = this.generateKey('menu', restaurantId, categoryId);
    return await this.set(key, data, ttl);
  }

  async clearMenuCache(restaurantId, categoryId = '*') {
    const pattern = this.generateKey('menu', restaurantId, categoryId);
    return await this.delPattern(pattern);
  }

  // Search results cache
  async getSearchCache(query, filters = {}) {
    const filterKey = Object.keys(filters).sort().map(k => `${k}:${filters[k]}`).join('|');
    const key = this.generateKey('search', Buffer.from(query).toString('base64'), filterKey);
    return await this.get(key);
  }

  async setSearchCache(query, filters = {}, data, ttl = 300) { // 5 minutes for search results
    const filterKey = Object.keys(filters).sort().map(k => `${k}:${filters[k]}`).join('|');
    const key = this.generateKey('search', Buffer.from(query).toString('base64'), filterKey);
    return await this.set(key, data, ttl);
  }

  // Popular items cache
  async getPopularItemsCache(type = 'restaurants') {
    const key = this.generateKey('popular', type);
    return await this.get(key);
  }

  async setPopularItemsCache(type = 'restaurants', data, ttl = 3600) { // 1 hour for popular items
    const key = this.generateKey('popular', type);
    return await this.set(key, data, ttl);
  }

  // Categories cache
  async getCategoriesCache() {
    const key = this.generateKey('categories', 'all');
    return await this.get(key);
  }

  async setCategoriesCache(data, ttl = 3600) { // 1 hour for categories
    const key = this.generateKey('categories', 'all');
    return await this.set(key, data, ttl);
  }

  // Analytics cache (for expensive aggregation queries)
  async getAnalyticsCache(type, period, restaurantId = null) {
    const key = restaurantId 
      ? this.generateKey('analytics', type, period, restaurantId)
      : this.generateKey('analytics', type, period);
    return await this.get(key);
  }

  async setAnalyticsCache(type, period, data, restaurantId = null, ttl = 1800) { // 30 minutes for analytics
    const key = restaurantId 
      ? this.generateKey('analytics', type, period, restaurantId)
      : this.generateKey('analytics', type, period);
    return await this.set(key, data, ttl);
  }

  // Cache invalidation helpers
  async invalidateUserRelatedCache(userId) {
    await Promise.all([
      this.clearUserCache(userId),
      this.delPattern(this.generateKey('user_favorites', userId, '*')),
      this.delPattern(this.generateKey('user_orders', userId, '*'))
    ]);
  }

  async invalidateRestaurantRelatedCache(restaurantId) {
    await Promise.all([
      this.clearRestaurantCache(restaurantId),
      this.clearMenuCache(restaurantId),
      this.delPattern(this.generateKey('search', '*')), // Clear search cache as restaurant data changed
      this.delPattern(this.generateKey('popular', '*')) // Clear popular items cache
    ]);
  }

  // Health check
  async healthCheck() {
    if (!redisManager.isReady()) {
      return { status: 'disconnected', message: 'Redis not connected' };
    }

    try {
      const testKey = 'health:test';
      const testValue = Date.now().toString();
      
      await this.set(testKey, testValue, 10);
      const retrieved = await this.get(testKey);
      await this.del(testKey);
      
      const isWorking = retrieved === testValue;
      
      return {
        status: isWorking ? 'healthy' : 'error',
        message: isWorking ? 'Cache service working correctly' : 'Cache service not working',
        enabled: this.isEnabled
      };
    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        enabled: this.isEnabled
      };
    }
  }

  // Get cache statistics
  async getStats() {
    if (!redisManager.isReady()) {
      return null;
    }

    try {
      const info = await redisManager.info('memory');
      const keyCount = await redisManager.dbSize();
      
      return {
        keyCount,
        memoryInfo: info,
        enabled: this.isEnabled
      };
    } catch (error) {
      console.warn('Cache stats error:', error.message);
      return null;
    }
  }
}

// Export singleton instance
module.exports = new CacheService();
