// Payment Routes
const express = require('express');
const PaymentController = require('../controllers/paymentController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting to all routes
router.use(apiLimiter);

// POST /api/v1/payments/create-intent - Create payment intent
router.post('/create-intent',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateCreatePaymentIntent(),
  PaymentController.createPaymentIntent
);

// POST /api/v1/payments/confirm - Confirm payment
router.post('/confirm',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateConfirmPayment(),
  PaymentController.confirmPayment
);

// POST /api/v1/payments/webhook - Stripe webhook endpoint (no auth required)
router.post('/webhook',
  express.raw({ type: 'application/json' }),
  PaymentController.handleWebhook
);

// GET /api/v1/payments/intent/:id - Get payment intent details
router.get('/intent/:id',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateUUID('id'),
  PaymentController.getPaymentIntent
);

// POST /api/v1/payments/refund - Process refund
router.post('/refund',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validateProcessRefund(),
  PaymentController.processRefund
);

module.exports = router;
