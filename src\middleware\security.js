// Security Middleware Configuration
const helmet = require('helmet');
const cors = require('cors');
const config = require('../config/app');

// CORS configuration - Permissive for development (Expo Go compatibility)
const corsOptions = {
  // Allow all origins for development (Expo Go compatibility)
  // In production, this should be restricted to specific domains
  origin: '*',
  credentials: false, // Set to false when using origin: '*'
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  optionsSuccessStatus: 200 // Some legacy browsers (IE11, various SmartTVs) choke on 204
};

// Helmet configuration
const helmetOptions = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https:"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'", "https:", "data:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
};

module.exports = {
  helmet: helmet(helmetOptions),
  cors: cors(corsOptions)
};
