# FoodWay Backend 🍕

Modern food delivery backend API built with Node.js, Express, PostgreSQL, and Redis. Deployed on Railway with full CI/CD integration.

## 🚀 Features

- **Modern Stack**: Node.js, Express.js, PostgreSQL, Redis
- **Railway Ready**: Optimized for Railway deployment with health checks
- **Database Management**: Migration and seeding scripts
- **Testing**: Jest testing framework with supertest
- **Security**: Helmet, CORS, input validation
- **Performance**: Compression, connection pooling
- **Development**: Hot reload with nodemon, comprehensive logging

## 📋 Prerequisites

- Node.js >= 18.0.0
- npm >= 9.0.0
- PostgreSQL database (Railway provides this)
- Redis instance (Railway provides this)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd foodway-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your Railway database credentials
   ```

4. **Database Setup**
   ```bash
   # Run migrations to create tables
   npm run db:migrate
   
   # Seed with sample data (optional)
   npm run db:seed
   ```

## 🏃‍♂️ Running the Application

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Testing
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch
```

## 📊 API Endpoints

### Health & Status
- `GET /` - Server information
- `GET /health` - Health check (used by Railway)
- `GET /test-db` - Database connection test
- `GET /test-redis` - Redis connection test

### Coming Soon
- Authentication endpoints
- User management
- Restaurant management
- Order management
- Menu management

## 🗄️ Database Schema

### Users Table
- `id` (UUID, Primary Key)
- `email` (VARCHAR, Unique)
- `password_hash` (VARCHAR)
- `first_name` (VARCHAR)
- `last_name` (VARCHAR)
- `phone` (VARCHAR)
- `avatar_url` (TEXT)
- `email_verified` (BOOLEAN)
- `created_at` (TIMESTAMPTZ)
- `updated_at` (TIMESTAMPTZ)

### Restaurants Table
- `id` (UUID, Primary Key)
- `name` (VARCHAR)
- `email` (VARCHAR, Unique)
- `password_hash` (VARCHAR)
- `phone` (VARCHAR)
- `address` (TEXT)
- `city` (VARCHAR)
- `cuisine_type` (VARCHAR)
- `description` (TEXT)
- `logo_url` (TEXT)
- `cover_image_url` (TEXT)
- `is_active` (BOOLEAN)
- `created_at` (TIMESTAMPTZ)
- `updated_at` (TIMESTAMPTZ)

## 🔧 Scripts

```bash
# Development
npm run dev          # Start with nodemon
npm start           # Start production server

# Database
npm run db:migrate  # Run database migrations
npm run db:seed     # Seed with sample data
npm run db:reset    # Reset database (development only)

# Testing
npm test           # Run tests
npm run test:watch # Run tests in watch mode
```

## 🌍 Environment Variables

### Required
- `DATABASE_URL` - PostgreSQL connection string
- `REDIS_URL` - Redis connection string
- `JWT_SECRET` - JWT signing secret

### Optional
- `NODE_ENV` - Environment (development/production)
- `PORT` - Server port (default: 5000)
- `JWT_EXPIRES_IN` - JWT expiration (default: 7d)
- `BCRYPT_SALT_ROUNDS` - Password hashing rounds (default: 12)

## 🚀 Railway Deployment

This project is configured for Railway deployment:

1. **Connect Repository**: Link your GitHub repository to Railway
2. **Environment Variables**: Set required environment variables in Railway dashboard
3. **Database**: Add PostgreSQL plugin in Railway
4. **Redis**: Add Redis plugin in Railway
5. **Deploy**: Railway will automatically deploy on git push

### Railway Configuration
- Health check endpoint: `/health`
- Start command: `npm start`
- Auto-restart on failure
- Environment detection

## 🧪 Testing

The project uses Jest for testing with the following structure:

```
tests/
├── setup.js          # Test configuration
├── server.test.js    # Server endpoint tests
└── ...               # Additional test files
```

### Running Tests
```bash
# All tests
npm test

# Watch mode
npm run test:watch

# With coverage
npm test -- --coverage
```

## 📁 Project Structure

```
foodway-backend/
├── scripts/          # Database scripts
│   ├── migrate.js    # Database migrations
│   ├── seed.js       # Sample data seeding
│   └── reset.js      # Database reset
├── tests/            # Test files
│   ├── setup.js      # Test configuration
│   └── server.test.js # Server tests
├── server.js         # Main application file
├── package.json      # Dependencies and scripts
├── railway.json      # Railway configuration
├── jest.config.js    # Jest configuration
├── .env              # Environment variables
├── .env.test         # Test environment variables
└── README.md         # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the Railway documentation for deployment issues

---

**FoodWay Backend** - Built with ❤️ for modern food delivery
