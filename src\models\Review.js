// Review Model
const BaseModel = require('./BaseModel');

class Review extends BaseModel {
  constructor() {
    super('reviews');
  }

  // Get reviews for a restaurant
  async getRestaurantReviews(restaurantId, filters = {}) {
    try {
      const { rating, page = 1, limit = 20, sortBy = 'created_at', sortOrder = 'DESC' } = filters;
      const offset = (page - 1) * limit;

      let whereConditions = ['r.restaurant_id = $1', 'r.is_active = true'];
      let params = [restaurantId];
      let paramIndex = 2;

      if (rating) {
        whereConditions.push(`r.rating = $${paramIndex}`);
        params.push(rating);
        paramIndex++;
      }

      const whereClause = whereConditions.join(' AND ');
      const orderClause = `ORDER BY r.${sortBy} ${sortOrder}`;

      const query = `
        SELECT 
          r.*,
          u.first_name,
          u.last_name,
          u.avatar,
          CASE WHEN r.order_id IS NOT NULL THEN true ELSE false END as is_verified
        FROM reviews r
        JOIN users u ON r.user_id = u.id
        WHERE ${whereClause}
        ${orderClause}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const countQuery = `
        SELECT COUNT(*) as count
        FROM reviews r
        WHERE ${whereClause}
      `;

      params.push(limit, offset);
      const countParams = params.slice(0, -2);

      const [results, countResult] = await Promise.all([
        this.query(query, params),
        this.query(countQuery, countParams)
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        reviews: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get reviews for a menu item
  async getMenuItemReviews(menuItemId, filters = {}) {
    try {
      const { rating, page = 1, limit = 20 } = filters;
      const offset = (page - 1) * limit;

      let whereConditions = ['r.menu_item_id = $1', 'r.is_active = true'];
      let params = [menuItemId];
      let paramIndex = 2;

      if (rating) {
        whereConditions.push(`r.rating = $${paramIndex}`);
        params.push(rating);
        paramIndex++;
      }

      const whereClause = whereConditions.join(' AND ');

      const query = `
        SELECT 
          r.*,
          u.first_name,
          u.last_name,
          u.avatar,
          CASE WHEN r.order_id IS NOT NULL THEN true ELSE false END as is_verified
        FROM reviews r
        JOIN users u ON r.user_id = u.id
        WHERE ${whereClause}
        ORDER BY r.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const countQuery = `
        SELECT COUNT(*) as count
        FROM reviews r
        WHERE ${whereClause}
      `;

      params.push(limit, offset);
      const countParams = params.slice(0, -2);

      const [results, countResult] = await Promise.all([
        this.query(query, params),
        this.query(countQuery, countParams)
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        reviews: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Create a review
  async createReview(reviewData) {
    try {
      const client = await this.beginTransaction();

      try {
        // Check if user has already reviewed this restaurant/item for this order
        if (reviewData.order_id) {
          const existingReview = await client.query(
            'SELECT id FROM reviews WHERE user_id = $1 AND order_id = $2',
            [reviewData.user_id, reviewData.order_id]
          );

          if (existingReview.rows.length > 0) {
            throw new Error('You have already reviewed this order');
          }

          // Verify the order belongs to the user and is delivered
          const orderCheck = await client.query(
            'SELECT id FROM orders WHERE id = $1 AND user_id = $2 AND status = $3',
            [reviewData.order_id, reviewData.user_id, 'delivered']
          );

          if (orderCheck.rows.length === 0) {
            throw new Error('Order not found or not eligible for review');
          }
        }

        // Create the review
        const result = await client.query(`
          INSERT INTO reviews (
            user_id, restaurant_id, order_id, menu_item_id, rating,
            title, comment, images, is_verified
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          RETURNING *
        `, [
          reviewData.user_id,
          reviewData.restaurant_id,
          reviewData.order_id || null,
          reviewData.menu_item_id || null,
          reviewData.rating,
          reviewData.title || null,
          reviewData.comment || null,
          reviewData.images || [],
          reviewData.order_id ? true : false // Verified if linked to an order
        ]);

        await this.commitTransaction(client);
        return result.rows[0];
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Update a review
  async updateReview(reviewId, userId, updateData) {
    try {
      // Verify review belongs to user
      const existingReview = await this.query(
        'SELECT * FROM reviews WHERE id = $1 AND user_id = $2',
        [reviewId, userId]
      );

      if (existingReview.rows.length === 0) {
        throw new Error('Review not found or access denied');
      }

      const allowedFields = ['rating', 'title', 'comment', 'images'];
      const updateFields = [];
      const updateValues = [];
      let paramIndex = 1;

      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          updateFields.push(`${field} = $${paramIndex}`);
          updateValues.push(updateData[field]);
          paramIndex++;
        }
      }

      if (updateFields.length === 0) {
        throw new Error('No valid fields to update');
      }

      updateFields.push(`updated_at = NOW()`);
      updateValues.push(reviewId);

      const query = `
        UPDATE reviews 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
      `;

      const result = await this.query(query, updateValues);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Delete a review
  async deleteReview(reviewId, userId) {
    try {
      // Verify review belongs to user
      const existingReview = await this.query(
        'SELECT * FROM reviews WHERE id = $1 AND user_id = $2',
        [reviewId, userId]
      );

      if (existingReview.rows.length === 0) {
        throw new Error('Review not found or access denied');
      }

      // Soft delete the review
      const result = await this.query(
        'UPDATE reviews SET is_active = false WHERE id = $1 RETURNING *',
        [reviewId]
      );

      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Add restaurant response to review
  async addRestaurantResponse(reviewId, response) {
    try {
      const result = await this.query(
        'UPDATE reviews SET response = $1, response_date = NOW() WHERE id = $2 RETURNING *',
        [response, reviewId]
      );

      if (result.rows.length === 0) {
        throw new Error('Review not found');
      }

      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Get review statistics for restaurant
  async getRestaurantReviewStats(restaurantId) {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_reviews,
          AVG(rating) as average_rating,
          COUNT(*) FILTER (WHERE rating = 5) as five_star,
          COUNT(*) FILTER (WHERE rating = 4) as four_star,
          COUNT(*) FILTER (WHERE rating = 3) as three_star,
          COUNT(*) FILTER (WHERE rating = 2) as two_star,
          COUNT(*) FILTER (WHERE rating = 1) as one_star,
          COUNT(*) FILTER (WHERE is_verified = true) as verified_reviews
        FROM reviews
        WHERE restaurant_id = $1 AND is_active = true
      `;

      const result = await this.query(query, [restaurantId]);
      const stats = result.rows[0];

      // Calculate rating distribution percentages
      const total = parseInt(stats.total_reviews);
      if (total > 0) {
        stats.rating_distribution = {
          5: Math.round((stats.five_star / total) * 100),
          4: Math.round((stats.four_star / total) * 100),
          3: Math.round((stats.three_star / total) * 100),
          2: Math.round((stats.two_star / total) * 100),
          1: Math.round((stats.one_star / total) * 100)
        };
      } else {
        stats.rating_distribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
      }

      stats.average_rating = parseFloat(stats.average_rating) || 0;

      return stats;
    } catch (error) {
      throw error;
    }
  }

  // Get user's reviews
  async getUserReviews(userId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      const query = `
        SELECT 
          r.*,
          rest.name as restaurant_name,
          rest.logo as restaurant_logo,
          mi.name as menu_item_name,
          mi.image as menu_item_image
        FROM reviews r
        JOIN restaurants rest ON r.restaurant_id = rest.id
        LEFT JOIN menu_items mi ON r.menu_item_id = mi.id
        WHERE r.user_id = $1 AND r.is_active = true
        ORDER BY r.created_at DESC
        LIMIT $2 OFFSET $3
      `;

      const countQuery = `
        SELECT COUNT(*) as count
        FROM reviews
        WHERE user_id = $1 AND is_active = true
      `;

      const [results, countResult] = await Promise.all([
        this.query(query, [userId, limit, offset]),
        this.query(countQuery, [userId])
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        reviews: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Mark review as helpful
  async markReviewHelpful(reviewId) {
    try {
      const result = await this.query(
        'UPDATE reviews SET helpful_count = helpful_count + 1 WHERE id = $1 RETURNING helpful_count',
        [reviewId]
      );

      if (result.rows.length === 0) {
        throw new Error('Review not found');
      }

      return result.rows[0].helpful_count;
    } catch (error) {
      throw error;
    }
  }

  // Validate review data
  validateReviewData(reviewData) {
    const errors = [];

    // Validate rating
    if (!reviewData.rating || reviewData.rating < 1 || reviewData.rating > 5) {
      errors.push('Rating must be between 1 and 5');
    }

    // Validate required fields
    if (!reviewData.user_id) {
      errors.push('User ID is required');
    }

    if (!reviewData.restaurant_id) {
      errors.push('Restaurant ID is required');
    }

    // Validate comment length if provided
    if (reviewData.comment && reviewData.comment.length > 1000) {
      errors.push('Comment must be less than 1000 characters');
    }

    // Validate title length if provided
    if (reviewData.title && reviewData.title.length > 255) {
      errors.push('Title must be less than 255 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

module.exports = Review;
