// Socket.IO Service for Real-time Order Tracking
const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const databaseManager = require('../config/database');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId
    this.userSockets = new Map(); // socketId -> userId
  }

  // Initialize Socket.IO server
  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      }
    });

    this.setupMiddleware();
    this.setupEventHandlers();

    console.log('🔌 Socket.IO server initialized');
  }

  // Setup authentication middleware
  setupMiddleware() {
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');

        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // Get user ID from token (support both old and new format)
        const userId = decoded.sub || decoded.userId;

        // Verify user exists in database
        const pool = databaseManager.getPool();
        const userResult = await pool.query('SELECT id, email FROM users WHERE id = $1', [userId]);

        if (userResult.rows.length === 0) {
          return next(new Error('User not found'));
        }

        socket.userId = userId;
        socket.user = userResult.rows[0];
        next();

      } catch (error) {
        console.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  // Setup event handlers
  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`👤 User ${socket.userId} connected via socket ${socket.id}`);

      // Store user connection
      this.connectedUsers.set(socket.userId, socket.id);
      this.userSockets.set(socket.id, socket.userId);

      // Join user to their personal room for order updates
      socket.join(`user:${socket.userId}`);

      // Handle order tracking subscription
      socket.on('track_order', async (data) => {
        await this.handleTrackOrder(socket, data);
      });

      // Handle order tracking unsubscription
      socket.on('untrack_order', (data) => {
        this.handleUntrackOrder(socket, data);
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        console.log(`👤 User ${socket.userId} disconnected`);
        this.connectedUsers.delete(socket.userId);
        this.userSockets.delete(socket.id);
      });

      // Send welcome message
      socket.emit('connected', {
        message: 'Connected to FoodWay real-time tracking',
        userId: socket.userId
      });
    });
  }

  // Handle order tracking subscription
  async handleTrackOrder(socket, data) {
    try {
      const { orderId } = data;

      if (!orderId) {
        socket.emit('error', { message: 'Order ID is required' });
        return;
      }

      // Verify order belongs to user
      const pool = databaseManager.getPool();
      const orderResult = await pool.query(
        'SELECT id, status FROM orders WHERE id = $1 AND user_id = $2',
        [orderId, socket.userId]
      );

      if (orderResult.rows.length === 0) {
        socket.emit('error', { message: 'Order not found or access denied' });
        return;
      }

      // Join order-specific room
      socket.join(`order:${orderId}`);

      // Send current order status
      const order = orderResult.rows[0];
      socket.emit('order_status', {
        orderId: order.id,
        status: order.status,
        timestamp: new Date().toISOString()
      });

      console.log(`📦 User ${socket.userId} subscribed to order ${orderId} tracking`);

    } catch (error) {
      console.error('Error handling track order:', error);
      socket.emit('error', { message: 'Failed to track order' });
    }
  }

  // Handle order tracking unsubscription
  handleUntrackOrder(socket, data) {
    const { orderId } = data;

    if (orderId) {
      socket.leave(`order:${orderId}`);
      console.log(`📦 User ${socket.userId} unsubscribed from order ${orderId} tracking`);
    }
  }

  // Broadcast order status update to all tracking users
  broadcastOrderUpdate(orderId, statusUpdate) {
    if (!this.io) return;

    this.io.to(`order:${orderId}`).emit('order_status_update', {
      orderId,
      ...statusUpdate,
      timestamp: new Date().toISOString()
    });

    console.log(`📡 Broadcasted order ${orderId} status update:`, statusUpdate);
  }

  // Send notification to specific user
  sendUserNotification(userId, notification) {
    if (!this.io) return;

    this.io.to(`user:${userId}`).emit('notification', {
      ...notification,
      timestamp: new Date().toISOString()
    });

    console.log(`🔔 Sent notification to user ${userId}:`, notification);
  }

  // Broadcast driver location update
  broadcastDriverLocation(orderId, location) {
    if (!this.io) return;

    this.io.to(`order:${orderId}`).emit('driver_location_update', {
      orderId,
      location,
      timestamp: new Date().toISOString()
    });

    console.log(`📍 Broadcasted driver location for order ${orderId}:`, location);
  }

  // Get connected users count
  getConnectedUsersCount() {
    return this.connectedUsers.size;
  }

  // Check if user is connected
  isUserConnected(userId) {
    return this.connectedUsers.has(userId);
  }

  // Get socket instance for external use
  getIO() {
    return this.io;
  }

  // Broadcast system announcement
  broadcastAnnouncement(announcement) {
    if (!this.io) return;

    this.io.emit('announcement', {
      ...announcement,
      timestamp: new Date().toISOString()
    });

    console.log('📢 Broadcasted system announcement:', announcement);
  }
}

// Create singleton instance
const socketService = new SocketService();

module.exports = socketService;
