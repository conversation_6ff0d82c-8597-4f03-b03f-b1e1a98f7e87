# FoodWay Customer App - Backend API Documentation

## 🌐 Base Configuration

**Production Backend URL:** `https://backend-production-f106.up.railway.app`  
**API Base URL:** `https://backend-production-f106.up.railway.app/api/v1`  
**Environment:** Production (Railway Deployment)  
**Database:** PostgreSQL  
**Cache:** Redis  

## 🔐 Authentication System

### User Registration

**Endpoint:** `POST /api/v1/auth/register`

#### Request Body:
```json
{
  "firstName": "<PERSON>",
  "lastName": "Doe", 
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "phone": "+1234567890",
  "dateOfBirth": "1990-01-15"
}
```

#### Validation Rules:
- `firstName`: Required, 2-50 characters
- `lastName`: Required, 2-50 characters  
- `email`: Required, valid email format, unique
- `password`: Required, min 8 chars, 1 uppercase, 1 lowercase, 1 number, 1 special char
- `phone`: Required, valid phone number format
- `dateOfBirth`: Optional, valid date, user must be 13+ years old

#### Success Response (201):
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-string",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "emailVerified": false,
      "createdAt": "2025-01-15T10:30:00.000Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 3600
    }
  },
  "message": "User registered successfully",
  "timestamp": "2025-01-15T10:30:00.000Z"
}
```

#### Error Response (400):
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": [
      {
        "field": "email",
        "message": "Email already exists"
      }
    ]
  },
  "timestamp": "2025-01-15T10:30:00.000Z"
}
```

### User Login

**Endpoint:** `POST /api/v1/auth/login`

#### Request Body:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

#### Validation Rules:
- `email`: Required, valid email format
- `password`: Required, minimum 1 character

#### Success Response (200):
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-string",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "emailVerified": true,
      "avatar": "https://example.com/avatar.jpg",
      "createdAt": "2025-01-15T10:30:00.000Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 3600
    }
  },
  "message": "Login successful",
  "timestamp": "2025-01-15T10:30:00.000Z"
}
```

#### Error Response (401):
```json
{
  "success": false,
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Invalid email or password",
    "details": null
  },
  "timestamp": "2025-01-15T10:30:00.000Z"
}
```

### Token Refresh

**Endpoint:** `POST /api/v1/auth/refresh`

#### Request Body:
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

#### Success Response (200):
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "expiresIn": 3600
  },
  "message": "Token refreshed successfully",
  "timestamp": "2025-01-15T10:30:00.000Z"
}
```

### Logout

**Endpoint:** `POST /api/v1/auth/logout`  
**Authentication:** Required (Bearer Token)

#### Request Headers:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

#### Success Response (200):
```json
{
  "success": true,
  "data": null,
  "message": "Logout successful",
  "timestamp": "2025-01-15T10:30:00.000Z"
}
```

## 📊 Database Schema

### Users Table Structure

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    avatar VARCHAR(500),
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    email_verification_expires TIMESTAMP,
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### User Sessions Table Structure

```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    refresh_token VARCHAR(500) NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔒 Authentication Headers

For all protected endpoints, include the Authorization header:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

## 📱 CORS Configuration

The backend is configured with permissive CORS for development (Expo Go compatibility):

```javascript
{
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin']
}
```

## 🚀 Quick Start Example

### 1. Register a New User
```bash
curl -X POST https://backend-production-f106.up.railway.app/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "phone": "+1234567890"
  }'
```

### 2. Login User
```bash
curl -X POST https://backend-production-f106.up.railway.app/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

### 3. Use Access Token for Protected Routes
```bash
curl -X GET https://backend-production-f106.up.railway.app/api/v1/user/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN_HERE"
```

## ⚠️ Important Notes

1. **Password Security**: Passwords are hashed using bcrypt with salt rounds
2. **Token Expiry**: Access tokens expire in 1 hour, refresh tokens in 7 days
3. **Rate Limiting**: Auth endpoints have stricter rate limits (5 requests/minute)
4. **Email Verification**: Users receive verification emails after registration
5. **HTTPS Only**: All API calls must use HTTPS in production

## 🔄 Token Management

- **Access Token**: Short-lived (1 hour), used for API requests
- **Refresh Token**: Long-lived (7 days), used to get new access tokens
- **Automatic Refresh**: Frontend should refresh tokens before expiry
- **Logout**: Invalidates both access and refresh tokens

## 📧 Email Verification

After registration, users receive an email with a verification link:
- **Endpoint**: `POST /api/v1/auth/verify-email`
- **Parameter**: `token` (from email link)
- **Resend**: `POST /api/v1/auth/resend-verification` (authenticated)

## 🏪 Core Customer App Endpoints

### Get User Profile
**Endpoint:** `GET /api/v1/user/profile`
**Authentication:** Required

#### Success Response (200):
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid-string",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "avatar": "https://example.com/avatar.jpg",
      "emailVerified": true,
      "createdAt": "2025-01-15T10:30:00.000Z"
    }
  },
  "message": "Profile retrieved successfully"
}
```

### Get Restaurants
**Endpoint:** `GET /api/v1/restaurants`
**Authentication:** Optional

#### Query Parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 50)
- `cuisine`: Filter by cuisine type
- `rating`: Minimum rating (1-5)
- `delivery_fee`: Maximum delivery fee

#### Success Response (200):
```json
{
  "success": true,
  "data": {
    "restaurants": [
      {
        "id": "uuid-string",
        "name": "Pizza Palace",
        "description": "Authentic Italian pizza",
        "cuisine": "Italian",
        "rating": 4.5,
        "reviewCount": 150,
        "deliveryFee": 2.99,
        "deliveryTime": "25-35 min",
        "image": "https://example.com/restaurant.jpg",
        "isOpen": true,
        "acceptingOrders": true
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3
    }
  },
  "message": "Restaurants retrieved successfully"
}
```

### Get Categories
**Endpoint:** `GET /api/v1/categories`
**Authentication:** Not Required

#### Success Response (200):
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": "uuid-string",
        "name": "Pizza",
        "description": "Delicious pizzas",
        "image": "https://example.com/pizza.jpg",
        "itemCount": 25
      }
    ]
  },
  "message": "Categories retrieved successfully"
}
```

### Create Order
**Endpoint:** `POST /api/v1/orders`
**Authentication:** Required

#### Request Body:
```json
{
  "restaurantId": "uuid-string",
  "items": [
    {
      "menuItemId": "uuid-string",
      "quantity": 2,
      "price": 12.99,
      "specialInstructions": "No onions"
    }
  ],
  "deliveryAddress": {
    "street": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zipCode": "10001"
  },
  "paymentMethodId": "uuid-string",
  "specialInstructions": "Ring doorbell"
}
```

#### Success Response (201):
```json
{
  "success": true,
  "data": {
    "id": "uuid-string",
    "orderNumber": "ORD-2025-001",
    "status": "pending",
    "subtotal": 25.98,
    "tax": 2.08,
    "deliveryFee": 2.99,
    "totalAmount": 31.05,
    "estimatedDeliveryTime": "2025-01-15T11:30:00.000Z",
    "restaurant": {
      "name": "Pizza Palace",
      "phone": "+1234567890"
    }
  },
  "message": "Order created successfully"
}
```

### Get User Orders
**Endpoint:** `GET /api/v1/orders`
**Authentication:** Required

#### Query Parameters:
- `status`: Filter by order status
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 50)

#### Success Response (200):
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "uuid-string",
        "orderNumber": "ORD-2025-001",
        "status": "delivered",
        "totalAmount": 31.05,
        "createdAt": "2025-01-15T10:30:00.000Z",
        "restaurant": {
          "name": "Pizza Palace",
          "image": "https://example.com/restaurant.jpg"
        },
        "itemCount": 2
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  },
  "message": "Orders retrieved successfully"
}
```

## 🔗 Additional Endpoints Available

- **Favorites**: `GET/POST/DELETE /api/v1/user/favorites`
- **Reviews**: `GET/POST /api/v1/reviews`
- **Addresses**: `GET/POST/PUT/DELETE /api/v1/addresses`
- **Payment Methods**: `GET/POST/DELETE /api/v1/payment-methods`
- **Notifications**: `GET /api/v1/notifications`
- **Promotions**: `GET /api/v1/promotions`
- **Restaurant Search**: `GET /api/v1/restaurants/search`
- **Nearby Restaurants**: `GET /api/v1/restaurants/nearby`

## 📱 Frontend Integration

### Environment Configuration
```javascript
// .env or app.config.js
EXPO_PUBLIC_API_URL=https://backend-production-f106.up.railway.app/api/v1
```

### API Client Example
```javascript
const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL;

const apiClient = {
  async register(userData) {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData)
    });
    return response.json();
  },

  async login(credentials) {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });
    return response.json();
  }
};
```

---

**Last Updated:** January 15, 2025
**API Version:** v1
**Backend Version:** 1.0.0
