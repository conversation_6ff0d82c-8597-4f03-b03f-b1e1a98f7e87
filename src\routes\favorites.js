// User Favorites Routes
const express = require('express');
const UserFavoriteController = require('../controllers/userFavoriteController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting to all routes
router.use(apiLimiter);

// Public routes (no authentication required)

// GET /api/v1/favorites/popular/restaurants - Get popular favorited restaurants
router.get('/popular/restaurants',
  UserFavoriteController.getPopularFavoritedRestaurants
);

// GET /api/v1/favorites/popular/menu-items - Get popular favorited menu items
router.get('/popular/menu-items',
  UserFavoriteController.getPopularFavoritedMenuItems
);

// Protected routes (authentication required)
router.use(AuthMiddleware.authenticate);

// GET /api/v1/favorites/restaurants - Get user's favorite restaurants
router.get('/restaurants',
  UserFavoriteController.getFavoriteRestaurants
);

// GET /api/v1/favorites/menu-items - Get user's favorite menu items
router.get('/menu-items',
  UserFavoriteController.getFavoriteMenuItems
);

// GET /api/v1/favorites/stats - Get user's favorite statistics
router.get('/stats',
  UserFavoriteController.getFavoriteStats
);

// GET /api/v1/favorites/recommendations - Get recommendations based on favorites
router.get('/recommendations',
  UserFavoriteController.getRecommendations
);

// POST /api/v1/favorites/restaurants/:restaurantId - Add restaurant to favorites
router.post('/restaurants/:restaurantId',
  ValidationMiddleware.validateUUID('restaurantId'),
  UserFavoriteController.addRestaurantToFavorites
);

// POST /api/v1/favorites/menu-items/:menuItemId - Add menu item to favorites
router.post('/menu-items/:menuItemId',
  ValidationMiddleware.validateUUID('menuItemId'),
  UserFavoriteController.addMenuItemToFavorites
);

// DELETE /api/v1/favorites/restaurants/:restaurantId - Remove restaurant from favorites
router.delete('/restaurants/:restaurantId',
  ValidationMiddleware.validateUUID('restaurantId'),
  UserFavoriteController.removeRestaurantFromFavorites
);

// DELETE /api/v1/favorites/menu-items/:menuItemId - Remove menu item from favorites
router.delete('/menu-items/:menuItemId',
  ValidationMiddleware.validateUUID('menuItemId'),
  UserFavoriteController.removeMenuItemFromFavorites
);

// GET /api/v1/favorites/restaurants/:restaurantId/check - Check if restaurant is favorited
router.get('/restaurants/:restaurantId/check',
  ValidationMiddleware.validateUUID('restaurantId'),
  UserFavoriteController.checkRestaurantFavorited
);

// GET /api/v1/favorites/menu-items/:menuItemId/check - Check if menu item is favorited
router.get('/menu-items/:menuItemId/check',
  ValidationMiddleware.validateUUID('menuItemId'),
  UserFavoriteController.checkMenuItemFavorited
);

// DELETE /api/v1/favorites/clear - Clear all favorites
router.delete('/clear',
  UserFavoriteController.clearAllFavorites
);

module.exports = router;
