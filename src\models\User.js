// User Model
const BaseModel = require('./BaseModel');
const bcrypt = require('bcryptjs');

class User extends BaseModel {
  constructor() {
    super('users');
  }

  // Find user by email
  async findByEmail(email) {
    try {
      return await this.findOne({ email });
    } catch (error) {
      throw error;
    }
  }

  // Create user with hashed password
  async createUser(userData) {
    try {
      const { password, ...otherData } = userData;
      
      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      const user = await this.create({
        ...otherData,
        password_hash: hashedPassword
      });

      // Remove password hash from returned user
      delete user.password_hash;
      return user;
    } catch (error) {
      throw error;
    }
  }

  // Verify user password
  async verifyPassword(userId, password) {
    try {
      const user = await this.findById(userId);
      if (!user) return false;

      return await bcrypt.compare(password, user.password_hash);
    } catch (error) {
      throw error;
    }
  }

  // Update user profile
  async updateProfile(userId, profileData) {
    try {
      // Remove sensitive fields that shouldn't be updated directly
      const { password, password_hash, email, ...safeData } = profileData;
      
      return await this.updateById(userId, safeData);
    } catch (error) {
      throw error;
    }
  }

  // Change user password
  async changePassword(userId, currentPassword, newPassword) {
    try {
      // Verify current password
      const isValid = await this.verifyPassword(userId, currentPassword);
      if (!isValid) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      return await this.updateById(userId, { password_hash: hashedPassword });
    } catch (error) {
      throw error;
    }
  }

  // Get user with addresses
  async getUserWithAddresses(userId) {
    try {
      const query = `
        SELECT 
          u.*,
          json_agg(
            json_build_object(
              'id', ua.id,
              'label', ua.label,
              'street', ua.street_address,
              'apartment', ua.apartment,
              'city', ua.city,
              'state', ua.state,
              'zipCode', ua.postal_code,
              'isDefault', ua.is_default,
              'deliveryInstructions', ua.delivery_instructions
            )
          ) FILTER (WHERE ua.id IS NOT NULL) as addresses
        FROM users u
        LEFT JOIN user_addresses ua ON u.id = ua.user_id
        WHERE u.id = $1
        GROUP BY u.id
      `;

      const result = await this.query(query, [userId]);
      const user = result.rows[0];

      if (user) {
        delete user.password_hash;
        user.addresses = user.addresses || [];
      }

      return user;
    } catch (error) {
      throw error;
    }
  }

  // Get user statistics
  async getUserStats(userId) {
    try {
      const query = `
        SELECT 
          COUNT(DISTINCT o.id) as total_orders,
          COUNT(DISTINCT uf.id) FILTER (WHERE uf.item_type = 'restaurant') as favorite_restaurants,
          COUNT(DISTINCT uf.id) FILTER (WHERE uf.item_type = 'menu_item') as favorite_menu_items,
          COALESCE(SUM(o.total_amount), 0) as total_spent
        FROM users u
        LEFT JOIN orders o ON u.id = o.user_id AND o.status = 'delivered'
        LEFT JOIN user_favorites uf ON u.id = uf.user_id
        WHERE u.id = $1
        GROUP BY u.id
      `;

      const result = await this.query(query, [userId]);
      return result.rows[0] || {
        total_orders: 0,
        favorite_restaurants: 0,
        favorite_menu_items: 0,
        total_spent: 0
      };
    } catch (error) {
      throw error;
    }
  }

  // Search users (admin function)
  async searchUsers(searchTerm, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      
      const query = `
        SELECT 
          id, email, first_name, last_name, phone, avatar_url,
          is_active, created_at, last_login_at
        FROM users
        WHERE 
          email ILIKE $1 OR 
          first_name ILIKE $1 OR 
          last_name ILIKE $1 OR
          phone ILIKE $1
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
      `;

      const countQuery = `
        SELECT COUNT(*) as count
        FROM users
        WHERE 
          email ILIKE $1 OR 
          first_name ILIKE $1 OR 
          last_name ILIKE $1 OR
          phone ILIKE $1
      `;

      const searchPattern = `%${searchTerm}%`;
      
      const [results, countResult] = await Promise.all([
        this.query(query, [searchPattern, limit, offset]),
        this.query(countQuery, [searchPattern])
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        data: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Update last login
  async updateLastLogin(userId) {
    try {
      return await this.updateById(userId, { last_login_at: new Date() });
    } catch (error) {
      throw error;
    }
  }

  // Deactivate user account
  async deactivateUser(userId) {
    try {
      return await this.updateById(userId, { is_active: false });
    } catch (error) {
      throw error;
    }
  }

  // Reactivate user account
  async reactivateUser(userId) {
    try {
      return await this.updateById(userId, { is_active: true });
    } catch (error) {
      throw error;
    }
  }
}

module.exports = User;
