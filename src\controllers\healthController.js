// Health Check Controller
const databaseManager = require('../config/database');
const redisManager = require('../config/redis');
const config = require('../config/app');
const PerformanceMiddleware = require('../middleware/performance');

class HealthController {
  // Root endpoint - Server information
  static getServerInfo(req, res) {
    res.json({
      success: true,
      message: 'FoodWay Backend API - Ready for Development',
      version: '1.0.0',
      environment: config.nodeEnv,
      timestamp: new Date().toISOString(),
      services: {
        database: databaseManager.getPool() ? 'Connected' : 'Disconnected',
        redis: redisManager.isReady() ? 'Connected' : 'Disconnected'
      }
    });
  }

  // Health check endpoint for Railway
  static async getHealthStatus(req, res) {
    const startTime = Date.now();

    const health = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.nodeEnv,
      version: '1.0.0',
      services: {
        database: 'disconnected',
        redis: 'disconnected'
      }
    };

    // Check database health with timing
    let dbResponseTime = null;
    const pool = databaseManager.getPool();
    if (pool) {
      try {
        const dbStartTime = Date.now();
        const client = await pool.connect();
        await client.query('SELECT 1');
        client.release();
        dbResponseTime = Date.now() - dbStartTime;
        health.services.database = 'connected';
        health.database_response_time = `${dbResponseTime}ms`;
      } catch (error) {
        health.services.database = 'error';
        health.database_error = error.message;
      }
    }

    // Check Redis health with timing
    let redisResponseTime = null;
    if (redisManager.isReady()) {
      try {
        const redisStartTime = Date.now();
        await redisManager.ping();
        redisResponseTime = Date.now() - redisStartTime;
        health.services.redis = 'connected';
        health.redis_response_time = `${redisResponseTime}ms`;
      } catch (error) {
        health.services.redis = 'error';
        health.redis_error = error.message;
      }
    }

    // Add performance metrics
    const performanceMetrics = PerformanceMiddleware.getPerformanceMetrics();
    health.performance = {
      memory_usage_mb: performanceMetrics.memory.heapUsed,
      total_response_time: `${Date.now() - startTime}ms`,
      connection_pool: PerformanceMiddleware.monitorConnectionPool(pool)
    };

    // Performance health indicators
    const dbHealthy = health.services.database === 'connected' && (!dbResponseTime || dbResponseTime < 1000);
    const redisHealthy = health.services.redis === 'connected' && (!redisResponseTime || redisResponseTime < 100);
    const memoryHealthy = performanceMetrics.memory.heapUsed < 500;

    const isHealthy = dbHealthy && memoryHealthy;

    if (!isHealthy) {
      health.status = 'DEGRADED';
      health.warnings = [];

      if (!dbHealthy) {
        health.warnings.push('Database response time is slow or disconnected');
      }
      if (!redisHealthy) {
        health.warnings.push('Redis response time is slow or disconnected');
      }
      if (!memoryHealthy) {
        health.warnings.push('High memory usage detected');
      }
    }

    res.status(isHealthy ? 200 : 503).json(health);
  }

  // Database connection test
  static async testDatabase(req, res) {
    const pool = databaseManager.getPool();
    if (!pool) {
      return res.status(503).json({
        success: false,
        error: 'Database not configured',
        message: 'DATABASE_URL environment variable not set'
      });
    }

    try {
      const result = await pool.query('SELECT NOW() as current_time, version() as db_version');
      res.json({
        success: true,
        message: 'Database connection successful',
        data: {
          current_time: result.rows[0].current_time,
          database_version: result.rows[0].db_version
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Database connection failed',
        details: error.message
      });
    }
  }

  // Redis connection test
  static async testRedis(req, res) {
    if (!redisManager.isReady()) {
      return res.status(503).json({
        success: false,
        error: 'Redis not configured or not connected',
        message: 'REDIS_URL environment variable not set or connection failed'
      });
    }

    try {
      const testKey = 'test:' + Date.now();
      const testValue = 'FoodWay Redis Test';

      // Set a test value
      await redisManager.set(testKey, testValue, { EX: 10 });

      // Get the test value
      const retrievedValue = await redisManager.get(testKey);

      res.json({
        success: true,
        message: 'Redis connection successful',
        data: {
          test_key: testKey,
          test_value: testValue,
          retrieved_value: retrievedValue,
          match: testValue === retrievedValue
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Redis operation failed',
        details: error.message
      });
    }
  }

  // Detailed performance metrics endpoint
  static async getPerformanceMetrics(req, res) {
    try {
      const pool = databaseManager.getPool();
      const performanceData = {
        timestamp: new Date().toISOString(),
        system: PerformanceMiddleware.getPerformanceMetrics(),
        database: {
          connectionPool: PerformanceMiddleware.monitorConnectionPool(pool),
          status: pool ? 'connected' : 'disconnected'
        },
        redis: {
          status: redisManager.isReady() ? 'connected' : 'disconnected'
        },
        process: {
          pid: process.pid,
          title: process.title,
          cwd: process.cwd(),
          execPath: process.execPath
        }
      };

      res.json({
        success: true,
        data: performanceData,
        message: 'Performance metrics retrieved successfully'
      });
    } catch (error) {
      console.error('Performance metrics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve performance metrics',
        details: error.message
      });
    }
  }
}

module.exports = HealthController;
