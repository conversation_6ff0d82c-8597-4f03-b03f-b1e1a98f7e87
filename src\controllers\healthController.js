// Health Check Controller
const databaseManager = require('../config/database');
const redisManager = require('../config/redis');
const config = require('../config/app');

class HealthController {
  // Root endpoint - Server information
  static getServerInfo(req, res) {
    res.json({
      success: true,
      message: 'FoodWay Backend API - Ready for Development',
      version: '1.0.0',
      environment: config.nodeEnv,
      timestamp: new Date().toISOString(),
      services: {
        database: databaseManager.getPool() ? 'Connected' : 'Disconnected',
        redis: redisManager.isReady() ? 'Connected' : 'Disconnected'
      }
    });
  }

  // Health check endpoint for Railway
  static async getHealthStatus(req, res) {
    const health = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.nodeEnv,
      version: '1.0.0',
      services: {
        database: 'disconnected',
        redis: 'disconnected'
      }
    };

    // Check database health
    const pool = databaseManager.getPool();
    if (pool) {
      try {
        const client = await pool.connect();
        await client.query('SELECT 1');
        client.release();
        health.services.database = 'connected';
      } catch (error) {
        health.services.database = 'error';
        health.database_error = error.message;
      }
    }

    // Check Redis health
    if (redisManager.isReady()) {
      try {
        await redisManager.ping();
        health.services.redis = 'connected';
      } catch (error) {
        health.services.redis = 'error';
        health.redis_error = error.message;
      }
    }

    const isHealthy = health.services.database === 'connected';
    res.status(isHealthy ? 200 : 503).json(health);
  }

  // Database connection test
  static async testDatabase(req, res) {
    const pool = databaseManager.getPool();
    if (!pool) {
      return res.status(503).json({
        success: false,
        error: 'Database not configured',
        message: 'DATABASE_URL environment variable not set'
      });
    }

    try {
      const result = await pool.query('SELECT NOW() as current_time, version() as db_version');
      res.json({
        success: true,
        message: 'Database connection successful',
        data: {
          current_time: result.rows[0].current_time,
          database_version: result.rows[0].db_version
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Database connection failed',
        details: error.message
      });
    }
  }

  // Redis connection test
  static async testRedis(req, res) {
    if (!redisManager.isReady()) {
      return res.status(503).json({
        success: false,
        error: 'Redis not configured or not connected',
        message: 'REDIS_URL environment variable not set or connection failed'
      });
    }

    try {
      const testKey = 'test:' + Date.now();
      const testValue = 'FoodWay Redis Test';

      // Set a test value
      await redisManager.set(testKey, testValue, { EX: 10 });

      // Get the test value
      const retrievedValue = await redisManager.get(testKey);

      res.json({
        success: true,
        message: 'Redis connection successful',
        data: {
          test_key: testKey,
          test_value: testValue,
          retrieved_value: retrievedValue,
          match: testValue === retrievedValue
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Redis operation failed',
        details: error.message
      });
    }
  }
}

module.exports = HealthController;
