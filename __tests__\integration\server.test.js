// FoodWay Backend Server Tests
const request = require('supertest');
const { app } = require('../../server');

describe('FoodWay Backend Server', () => {
  describe('GET /', () => {
    it('should return server information', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('version');
      expect(response.body).toHaveProperty('environment');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('services');
    });
  });

  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect((res) => {
          // Accept both 200 (healthy) and 503 (unhealthy) status codes
          expect([200, 503]).toContain(res.status);
        });

      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
      expect(response.body).toHaveProperty('environment');
      expect(response.body).toHaveProperty('version');
      expect(response.body).toHaveProperty('services');
      expect(response.body.services).toHaveProperty('database');
      expect(response.body.services).toHaveProperty('redis');
    });
  });

  describe('GET /test-db', () => {
    it('should test database connection', async () => {
      const response = await request(app)
        .get('/test-db')
        .expect((res) => {
          // Accept both 200 (connected) and 503 (not configured) status codes
          expect([200, 503]).toContain(res.status);
        });

      expect(response.body).toHaveProperty('success');

      if (response.body.success) {
        expect(response.body).toHaveProperty('message');
        expect(response.body).toHaveProperty('data');
        expect(response.body.data).toHaveProperty('current_time');
        expect(response.body.data).toHaveProperty('database_version');
      } else {
        expect(response.body).toHaveProperty('error');
        expect(response.body).toHaveProperty('message');
      }
    });
  });

  describe('GET /test-redis', () => {
    it('should test Redis connection', async () => {
      const response = await request(app)
        .get('/test-redis')
        .expect((res) => {
          // Accept both 200 (connected) and 503 (not configured) status codes
          expect([200, 503]).toContain(res.status);
        });

      expect(response.body).toHaveProperty('success');

      if (response.body.success) {
        expect(response.body).toHaveProperty('message');
        expect(response.body).toHaveProperty('data');
        expect(response.body.data).toHaveProperty('test_key');
        expect(response.body.data).toHaveProperty('test_value');
        expect(response.body.data).toHaveProperty('retrieved_value');
        expect(response.body.data).toHaveProperty('match');
      } else {
        expect(response.body).toHaveProperty('error');
        expect(response.body).toHaveProperty('message');
      }
    });
  });

  describe('GET /nonexistent', () => {
    it('should return 404 for non-existent routes', async () => {
      const response = await request(app)
        .get('/nonexistent')
        .expect(404);

      expect(response.body).toHaveProperty('error', 'Route not found');
      expect(response.body).toHaveProperty('path', '/nonexistent');
      expect(response.body).toHaveProperty('method', 'GET');
    });
  });
});
