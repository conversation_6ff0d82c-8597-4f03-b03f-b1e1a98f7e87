# FoodWay Backend - Development Guide 👩‍💻

This guide covers local development setup, coding standards, and best practices for the FoodWay backend.

## 🚀 Quick Start

### Prerequisites
- Node.js >= 18.0.0
- npm >= 9.0.0
- Git
- Code editor (VS Code recommended)

### Setup
```bash
# Clone repository
git clone <your-repo-url>
cd foodway-backend

# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env with your database credentials

# Run database migrations
npm run db:migrate

# Seed with sample data (optional)
npm run db:seed

# Start development server
npm run dev
```

## 🏗️ Project Architecture

### Directory Structure
```
foodway-backend/
├── scripts/           # Database and utility scripts
│   ├── migrate.js    # Database migrations
│   ├── seed.js       # Sample data seeding
│   └── reset.js      # Database reset
├── tests/            # Test files
│   ├── setup.js      # Test configuration
│   └── *.test.js     # Test files
├── routes/           # API route handlers (to be created)
├── middleware/       # Custom middleware (to be created)
├── services/         # Business logic services (to be created)
├── utils/            # Utility functions (to be created)
├── server.js         # Main application entry point
└── package.json      # Dependencies and scripts
```

### Technology Stack
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Database**: PostgreSQL (via Railway)
- **Cache**: Redis (via Railway)
- **Testing**: Jest + Supertest
- **Security**: Helmet, CORS, bcrypt
- **Validation**: express-validator
- **Authentication**: JWT

## 🔧 Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/user-authentication

# Make changes
# Write tests
# Run tests
npm test

# Run development server
npm run dev

# Commit changes
git add .
git commit -m "feat: add user authentication"

# Push and create PR
git push origin feature/user-authentication
```

### 2. Database Changes
```bash
# Create migration (modify scripts/migrate.js)
# Test migration
npm run db:reset
npm run db:migrate

# Update seed data if needed
npm run db:seed
```

### 3. Testing
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- tests/server.test.js

# Run with coverage
npm test -- --coverage
```

## 📝 Coding Standards

### JavaScript Style
- Use ES6+ features
- Prefer `const` over `let`, avoid `var`
- Use async/await over Promises
- Use template literals for string interpolation
- Follow consistent naming conventions

### Example Code Style
```javascript
// Good
const getUserById = async (id) => {
  try {
    const user = await pool.query('SELECT * FROM users WHERE id = $1', [id]);
    return user.rows[0];
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
};

// API Route Example
app.get('/api/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const user = await getUserById(id);
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json({ success: true, data: user });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});
```

### Error Handling
```javascript
// Consistent error responses
const handleError = (res, error, statusCode = 500) => {
  console.error('Error:', error);
  res.status(statusCode).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
};

// Usage
try {
  // ... operation
} catch (error) {
  handleError(res, error);
}
```

## 🗄️ Database Guidelines

### Query Patterns
```javascript
// Use parameterized queries
const result = await pool.query(
  'SELECT * FROM users WHERE email = $1 AND active = $2',
  [email, true]
);

// Handle transactions
const client = await pool.connect();
try {
  await client.query('BEGIN');
  await client.query('INSERT INTO users ...');
  await client.query('INSERT INTO profiles ...');
  await client.query('COMMIT');
} catch (error) {
  await client.query('ROLLBACK');
  throw error;
} finally {
  client.release();
}
```

### Migration Best Practices
- Always test migrations on sample data
- Include rollback procedures
- Use transactions for complex migrations
- Document schema changes

## 🧪 Testing Guidelines

### Test Structure
```javascript
describe('User API', () => {
  beforeEach(async () => {
    // Setup test data
  });

  afterEach(async () => {
    // Cleanup test data
  });

  describe('POST /api/users', () => {
    it('should create a new user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Test',
        last_name: 'User'
      };

      const response = await request(app)
        .post('/api/users')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.email).toBe(userData.email);
    });
  });
});
```

### Test Categories
- **Unit Tests**: Individual functions and modules
- **Integration Tests**: API endpoints and database operations
- **End-to-End Tests**: Complete user workflows

## 🔐 Security Best Practices

### Authentication
```javascript
// Password hashing
const bcrypt = require('bcrypt');
const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;

const hashPassword = async (password) => {
  return await bcrypt.hash(password, saltRounds);
};

const verifyPassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};
```

### Input Validation
```javascript
const { body, validationResult } = require('express-validator');

// Validation middleware
const validateUser = [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }),
  body('first_name').trim().isLength({ min: 1 }),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  }
];

// Usage
app.post('/api/users', validateUser, createUser);
```

## 🚀 Performance Guidelines

### Database Optimization
- Use connection pooling (already configured)
- Add indexes for frequently queried columns
- Limit query results with LIMIT/OFFSET
- Use EXPLAIN ANALYZE for query optimization

### Caching with Redis
```javascript
// Cache pattern
const getCachedUser = async (userId) => {
  const cacheKey = `user:${userId}`;
  
  // Try cache first
  const cached = await redisClient.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Fetch from database
  const user = await getUserFromDB(userId);
  
  // Cache for 1 hour
  await redisClient.setEx(cacheKey, 3600, JSON.stringify(user));
  
  return user;
};
```

## 🔍 Debugging

### Logging
```javascript
// Use consistent logging
console.log('ℹ️  Info message');
console.warn('⚠️  Warning message');
console.error('❌ Error message');

// Development debugging
if (process.env.NODE_ENV === 'development') {
  console.log('🐛 Debug info:', debugData);
}
```

### Development Tools
- Use nodemon for auto-restart
- Enable source maps for debugging
- Use VS Code debugger configuration
- Monitor database queries in development

## 📦 Dependency Management

### Adding Dependencies
```bash
# Production dependencies
npm install package-name

# Development dependencies
npm install --save-dev package-name

# Update package.json description and keywords
```

### Security Updates
```bash
# Check for vulnerabilities
npm audit

# Fix vulnerabilities
npm audit fix

# Update dependencies
npm update
```

## 🌍 Environment Management

### Environment Files
- `.env` - Development environment
- `.env.test` - Testing environment
- `.env.example` - Template for new developers

### Environment Variables
```javascript
// Always provide defaults
const PORT = process.env.PORT || 5000;
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret';

// Validate required variables
if (!process.env.DATABASE_URL) {
  console.error('DATABASE_URL is required');
  process.exit(1);
}
```

## 🤝 Collaboration

### Git Workflow
- Use feature branches
- Write descriptive commit messages
- Keep commits atomic and focused
- Use conventional commit format

### Code Review
- Review for security issues
- Check test coverage
- Verify error handling
- Ensure consistent style

### Documentation
- Update README for new features
- Document API endpoints
- Add inline comments for complex logic
- Keep deployment guide updated

---

**Happy Coding!** 🎉
