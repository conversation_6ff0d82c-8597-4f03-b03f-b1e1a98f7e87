// FoodWay Database Migration Script
// Creates the basic database schema for fresh development

require('dotenv').config();
const { Pool } = require('pg');

async function migrate() {
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not found in environment variables');
    process.exit(1);
  }

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    console.log('🚀 Starting FoodWay database migration...\n');

    // Enable UUID extension
    console.log('📦 Enabling UUID extension...');
    await pool.query('CREATE EXTENSION IF NOT EXISTS "pgcrypto"');
    console.log('✅ UUID extension enabled');

    // Create migrations table to track schema versions
    console.log('📋 Creating migrations table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      )
    `);
    console.log('✅ Migrations table created');

    // Check if initial migration has been run
    const migrationCheck = await pool.query(
      "SELECT * FROM migrations WHERE name = 'initial_schema'"
    );

    if (migrationCheck.rows.length === 0) {
      console.log('🏗️  Running comprehensive FoodWay schema migration...');

      // Enable UUID extension
      console.log('🔧 Enabling UUID extension...');
      await pool.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);

      // Users table (customers)
      console.log('👥 Creating users table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS users (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          email VARCHAR(255) NOT NULL UNIQUE,
          password_hash VARCHAR(255) NOT NULL,
          first_name VARCHAR(100) NOT NULL,
          last_name VARCHAR(100) NOT NULL,
          phone VARCHAR(20),
          avatar TEXT,
          date_of_birth DATE,
          email_verified BOOLEAN NOT NULL DEFAULT false,
          phone_verified BOOLEAN NOT NULL DEFAULT false,
          email_verification_token VARCHAR(255),
          email_verification_expires TIMESTAMPTZ,
          password_reset_token VARCHAR(255),
          password_reset_expires TIMESTAMPTZ,
          refresh_token TEXT,
          last_login TIMESTAMPTZ,
          is_active BOOLEAN NOT NULL DEFAULT true,
          preferences JSONB DEFAULT '{}',
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Users table created');

      // User addresses table
      console.log('🏠 Creating user_addresses table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS user_addresses (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          label VARCHAR(100) NOT NULL,
          street VARCHAR(255) NOT NULL,
          apartment VARCHAR(100),
          city VARCHAR(100) NOT NULL,
          state VARCHAR(100) NOT NULL,
          zip_code VARCHAR(20) NOT NULL,
          country VARCHAR(100) NOT NULL DEFAULT 'USA',
          latitude DECIMAL(10, 8),
          longitude DECIMAL(11, 8),
          delivery_instructions TEXT,
          is_default BOOLEAN NOT NULL DEFAULT false,
          is_active BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ User addresses table created');

      // Payment methods table
      console.log('💳 Creating payment_methods table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS payment_methods (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          type VARCHAR(50) NOT NULL,
          provider VARCHAR(100),
          external_id VARCHAR(255),
          last_four VARCHAR(4),
          brand VARCHAR(50),
          expiry_month INTEGER,
          expiry_year INTEGER,
          cardholder_name VARCHAR(255),
          is_default BOOLEAN NOT NULL DEFAULT false,
          is_active BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Payment methods table created');

      // Restaurants table
      console.log('🍕 Creating restaurants table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS restaurants (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(255) NOT NULL,
          description TEXT,
          image TEXT,
          logo TEXT,
          slug VARCHAR(255) UNIQUE NOT NULL,
          phone VARCHAR(20),
          email VARCHAR(255),
          website TEXT,
          rating DECIMAL(3,2) DEFAULT 0.00,
          review_count INTEGER DEFAULT 0,
          cuisine_types TEXT[] DEFAULT '{}',
          delivery_fee DECIMAL(10,2) DEFAULT 0.00,
          minimum_order DECIMAL(10,2) DEFAULT 0.00,
          max_delivery_distance INTEGER DEFAULT 10,
          estimated_delivery_time VARCHAR(50),
          accepting_orders BOOLEAN DEFAULT TRUE,
          features TEXT[] DEFAULT '{}',
          opening_hours JSONB DEFAULT '{}',
          address JSONB NOT NULL,
          social_media JSONB DEFAULT '{}',
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Restaurants table created');

      // Menu categories table
      console.log('📋 Creating menu_categories table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS menu_categories (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          image TEXT,
          sort_order INTEGER DEFAULT 0,
          is_active BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Menu categories table created');

      // Menu items table
      console.log('🍽️ Creating menu_items table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS menu_items (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
          category_id UUID NOT NULL REFERENCES menu_categories(id) ON DELETE CASCADE,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          price DECIMAL(10, 2) NOT NULL,
          image TEXT,
          ingredients TEXT[] DEFAULT '{}',
          allergens TEXT[] DEFAULT '{}',
          preparation_time INTEGER DEFAULT 0,
          nutrition_info JSONB DEFAULT '{}',
          is_available BOOLEAN DEFAULT TRUE,
          is_popular BOOLEAN DEFAULT FALSE,
          is_vegetarian BOOLEAN DEFAULT FALSE,
          is_vegan BOOLEAN DEFAULT FALSE,
          is_gluten_free BOOLEAN DEFAULT FALSE,
          sort_order INTEGER DEFAULT 0,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Menu items table created');

      // Menu item customizations table
      console.log('⚙️ Creating menu_item_customizations table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS menu_item_customizations (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          menu_item_id UUID NOT NULL REFERENCES menu_items(id) ON DELETE CASCADE,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          is_required BOOLEAN DEFAULT FALSE,
          allow_multiple BOOLEAN DEFAULT FALSE,
          min_selections INTEGER DEFAULT 0,
          max_selections INTEGER,
          sort_order INTEGER DEFAULT 0,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Menu item customizations table created');

      // Customization options table
      console.log('🔧 Creating customization_options table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS customization_options (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          customization_id UUID NOT NULL REFERENCES menu_item_customizations(id) ON DELETE CASCADE,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          price_modifier DECIMAL(10, 2) DEFAULT 0.00,
          is_available BOOLEAN DEFAULT TRUE,
          sort_order INTEGER DEFAULT 0,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Customization options table created');



      // Orders table
      console.log('📦 Creating orders table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS orders (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          order_number VARCHAR(50) UNIQUE NOT NULL,
          user_id UUID NOT NULL REFERENCES users(id),
          restaurant_id UUID NOT NULL REFERENCES restaurants(id),
          status VARCHAR(50) NOT NULL DEFAULT 'pending',
          subtotal DECIMAL(10,2) NOT NULL,
          tax DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          delivery_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          tip DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          discount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          total_amount DECIMAL(10,2) NOT NULL,
          delivery_address JSONB NOT NULL,
          delivery_instructions TEXT,
          estimated_delivery_time TIMESTAMPTZ,
          actual_delivery_time TIMESTAMPTZ,
          payment_method_id UUID REFERENCES payment_methods(id),
          payment_status VARCHAR(50) DEFAULT 'pending',
          payment_intent_id VARCHAR(255),
          special_instructions TEXT,
          confirmed_at TIMESTAMPTZ,
          prepared_at TIMESTAMPTZ,
          ready_at TIMESTAMPTZ,
          picked_up_at TIMESTAMPTZ,
          delivered_at TIMESTAMPTZ,
          cancelled_at TIMESTAMPTZ,
          cancellation_reason TEXT,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Orders table created');

      // Order items table
      console.log('🛒 Creating order_items table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS order_items (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
          menu_item_id UUID NOT NULL REFERENCES menu_items(id),
          quantity INTEGER NOT NULL DEFAULT 1,
          unit_price DECIMAL(10, 2) NOT NULL,
          total_price DECIMAL(10, 2) NOT NULL,
          special_instructions TEXT,
          customizations JSONB DEFAULT '[]',
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Order items table created');

      // Order status history table
      console.log('📊 Creating order_status_history table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS order_status_history (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
          status VARCHAR(50) NOT NULL,
          note TEXT,
          changed_by UUID REFERENCES users(id),
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Order status history table created');

      // Order tracking table
      console.log('📍 Creating order_tracking table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS order_tracking (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
          status VARCHAR(50) NOT NULL,
          message TEXT,
          latitude DECIMAL(10, 8),
          longitude DECIMAL(11, 8),
          driver_name VARCHAR(255),
          driver_phone VARCHAR(20),
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Order tracking table created');

      // Reviews table
      console.log('⭐ Creating reviews table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS reviews (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID NOT NULL REFERENCES users(id),
          restaurant_id UUID NOT NULL REFERENCES restaurants(id),
          order_id UUID REFERENCES orders(id),
          menu_item_id UUID REFERENCES menu_items(id),
          rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
          title VARCHAR(255),
          comment TEXT,
          images TEXT[] DEFAULT '{}',
          is_verified BOOLEAN DEFAULT FALSE,
          helpful_count INTEGER DEFAULT 0,
          response TEXT,
          response_date TIMESTAMPTZ,
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Reviews table created');

      // User favorites table
      console.log('🎯 Creating user_favorites table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS user_favorites (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          restaurant_id UUID REFERENCES restaurants(id) ON DELETE CASCADE,
          menu_item_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          CONSTRAINT check_favorite_type CHECK (
            (restaurant_id IS NOT NULL AND menu_item_id IS NULL) OR
            (restaurant_id IS NULL AND menu_item_id IS NOT NULL)
          )
        )
      `);
      console.log('✅ User favorites table created');

      // User sessions table
      console.log('🔐 Creating user_sessions table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS user_sessions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          refresh_token VARCHAR(255) UNIQUE NOT NULL,
          device_info JSONB DEFAULT '{}',
          ip_address INET,
          user_agent TEXT,
          is_active BOOLEAN DEFAULT TRUE,
          expires_at TIMESTAMPTZ NOT NULL,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          last_used_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ User sessions table created');

      // Promo codes table
      console.log('🎫 Creating promo_codes table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS promo_codes (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          type VARCHAR(50) NOT NULL,
          value DECIMAL(10,2) NOT NULL,
          minimum_order DECIMAL(10,2) DEFAULT 0.00,
          maximum_discount DECIMAL(10,2),
          usage_limit INTEGER,
          usage_count INTEGER DEFAULT 0,
          user_limit INTEGER DEFAULT 1,
          is_active BOOLEAN DEFAULT TRUE,
          starts_at TIMESTAMPTZ,
          expires_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Promo codes table created');

      // Promo code usage table
      console.log('🎫 Creating promo_code_usage table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS promo_code_usage (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          promo_code_id UUID NOT NULL REFERENCES promo_codes(id) ON DELETE CASCADE,
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
          discount_amount DECIMAL(10,2) NOT NULL,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Promo code usage table created');

      // Notifications table
      console.log('🔔 Creating notifications table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS notifications (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          type VARCHAR(50) NOT NULL,
          title VARCHAR(255) NOT NULL,
          message TEXT NOT NULL,
          data JSONB,
          is_read BOOLEAN NOT NULL DEFAULT false,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
      `);
      console.log('✅ Notifications table created');

      // Push notification tokens table
      console.log('📱 Creating push_notification_tokens table...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS push_notification_tokens (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          token TEXT NOT NULL,
          platform VARCHAR(20) NOT NULL,
          is_active BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          UNIQUE(user_id, token)
        )
      `);
      console.log('✅ Push notification tokens table created');

      // Add missing columns to existing tables
      console.log('🔧 Adding missing columns to existing tables...');

      // Add missing columns to restaurants table
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS slug VARCHAR(255)');
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS image TEXT');
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS logo TEXT');
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS website TEXT');
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS cuisine_types TEXT[] DEFAULT \'{}\'');
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS max_delivery_distance INTEGER DEFAULT 10');
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS estimated_delivery_time VARCHAR(50)');
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS accepting_orders BOOLEAN DEFAULT TRUE');
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS features TEXT[] DEFAULT \'{}\'');
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS opening_hours JSONB DEFAULT \'{}\'');
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS address JSONB');
      await pool.query('ALTER TABLE restaurants ADD COLUMN IF NOT EXISTS social_media JSONB DEFAULT \'{}\'');

      // Update slug for existing restaurants without one
      await pool.query(`
        UPDATE restaurants
        SET slug = LOWER(REGEXP_REPLACE(name, '[^a-zA-Z0-9]+', '-', 'g'))
        WHERE slug IS NULL OR slug = ''
      `);

      // Make slug unique (after populating it) - only if constraint doesn't exist
      try {
        await pool.query('ALTER TABLE restaurants ADD CONSTRAINT restaurants_slug_unique UNIQUE (slug)');
      } catch (error) {
        if (!error.message.includes('already exists')) {
          throw error;
        }
      }

      // Add missing columns to menu_categories table
      await pool.query('ALTER TABLE menu_categories ADD COLUMN IF NOT EXISTS image TEXT');

      // Add missing columns to menu_items table
      await pool.query('ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS image TEXT');
      await pool.query('ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS ingredients TEXT[] DEFAULT \'{}\'');
      await pool.query('ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS preparation_time INTEGER DEFAULT 0');
      await pool.query('ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS nutrition_info JSONB DEFAULT \'{}\'');
      await pool.query('ALTER TABLE menu_items ADD COLUMN IF NOT EXISTS is_popular BOOLEAN DEFAULT FALSE');

      // Add missing columns to users table
      await pool.query('ALTER TABLE users ADD COLUMN IF NOT EXISTS date_of_birth DATE');
      await pool.query('ALTER TABLE users ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT \'{}\'');
      await pool.query('ALTER TABLE users ADD COLUMN IF NOT EXISTS avatar TEXT');

      // Add missing columns to user_addresses table
      await pool.query('ALTER TABLE user_addresses ADD COLUMN IF NOT EXISTS apartment VARCHAR(100)');
      await pool.query('ALTER TABLE user_addresses ADD COLUMN IF NOT EXISTS delivery_instructions TEXT');
      await pool.query('ALTER TABLE user_addresses ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE');

      // Add missing columns to payment_methods table
      await pool.query('ALTER TABLE payment_methods ADD COLUMN IF NOT EXISTS provider VARCHAR(100)');
      await pool.query('ALTER TABLE payment_methods ADD COLUMN IF NOT EXISTS external_id VARCHAR(255)');
      await pool.query('ALTER TABLE payment_methods ADD COLUMN IF NOT EXISTS cardholder_name VARCHAR(255)');
      await pool.query('ALTER TABLE payment_methods ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE');

      // Add missing columns to orders table
      await pool.query('ALTER TABLE orders ADD COLUMN IF NOT EXISTS discount DECIMAL(10,2) DEFAULT 0.00');
      await pool.query('ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_status VARCHAR(50) DEFAULT \'pending\'');
      await pool.query('ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_intent_id VARCHAR(255)');
      await pool.query('ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_instructions TEXT');
      await pool.query('ALTER TABLE orders ADD COLUMN IF NOT EXISTS confirmed_at TIMESTAMPTZ');
      await pool.query('ALTER TABLE orders ADD COLUMN IF NOT EXISTS prepared_at TIMESTAMPTZ');
      await pool.query('ALTER TABLE orders ADD COLUMN IF NOT EXISTS ready_at TIMESTAMPTZ');
      await pool.query('ALTER TABLE orders ADD COLUMN IF NOT EXISTS picked_up_at TIMESTAMPTZ');
      await pool.query('ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivered_at TIMESTAMPTZ');
      await pool.query('ALTER TABLE orders ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMPTZ');
      await pool.query('ALTER TABLE orders ADD COLUMN IF NOT EXISTS cancellation_reason TEXT');

      // Add missing columns to reviews table
      await pool.query('ALTER TABLE reviews ADD COLUMN IF NOT EXISTS menu_item_id UUID REFERENCES menu_items(id)');
      await pool.query('ALTER TABLE reviews ADD COLUMN IF NOT EXISTS title VARCHAR(255)');
      await pool.query('ALTER TABLE reviews ADD COLUMN IF NOT EXISTS helpful_count INTEGER DEFAULT 0');
      await pool.query('ALTER TABLE reviews ADD COLUMN IF NOT EXISTS response TEXT');
      await pool.query('ALTER TABLE reviews ADD COLUMN IF NOT EXISTS response_date TIMESTAMPTZ');
      await pool.query('ALTER TABLE reviews ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE');

      console.log('✅ Missing columns added to existing tables');

      // Create indexes for better performance
      console.log('🔍 Creating database indexes...');

      // Helper function to safely create indexes
      const safeCreateIndex = async (indexQuery, indexName) => {
        try {
          await pool.query(indexQuery);
        } catch (error) {
          if (error.message.includes('does not exist')) {
            console.log(`⚠️  Skipping ${indexName} - column does not exist`);
          } else if (!error.message.includes('already exists')) {
            throw error;
          }
        }
      };

      // User indexes
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)', 'users email index');
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone)', 'users phone index');
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)', 'users created_at index');

      // Address indexes
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_user_addresses_user_id ON user_addresses(user_id)', 'user_addresses user_id index');
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_user_addresses_default ON user_addresses(is_default)', 'user_addresses is_default index');
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_user_addresses_location ON user_addresses(latitude, longitude)', 'user_addresses location index');

      // Payment method indexes
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_payment_methods_user_id ON payment_methods(user_id)', 'payment_methods user_id index');
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_payment_methods_default ON payment_methods(is_default)', 'payment_methods is_default index');
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_payment_methods_type ON payment_methods(type)', 'payment_methods type index');

      // Restaurant indexes
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_restaurants_slug ON restaurants(slug)', 'restaurants slug index');
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_restaurants_cuisine_types ON restaurants USING GIN(cuisine_types)', 'restaurants cuisine_types index');
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_restaurants_rating ON restaurants(rating)', 'restaurants rating index');
      await safeCreateIndex('CREATE INDEX IF NOT EXISTS idx_restaurants_accepting_orders ON restaurants(accepting_orders)', 'restaurants accepting_orders index');

      // Menu indexes
      await pool.query('CREATE INDEX IF NOT EXISTS idx_menu_categories_restaurant_id ON menu_categories(restaurant_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_menu_categories_sort_order ON menu_categories(sort_order)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_menu_categories_active ON menu_categories(is_active)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_menu_items_restaurant_id ON menu_items(restaurant_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_menu_items_category_id ON menu_items(category_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_menu_items_available ON menu_items(is_available)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_menu_items_popular ON menu_items(is_popular)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_menu_items_price ON menu_items(price)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_menu_items_dietary ON menu_items(is_vegetarian, is_vegan, is_gluten_free)');

      // Customization indexes
      await pool.query('CREATE INDEX IF NOT EXISTS idx_customizations_menu_item_id ON menu_item_customizations(menu_item_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_customizations_sort_order ON menu_item_customizations(sort_order)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_customization_options_customization_id ON customization_options(customization_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_customization_options_available ON customization_options(is_available)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_customization_options_sort_order ON customization_options(sort_order)');

      // Order indexes
      await pool.query('CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_orders_restaurant_id ON orders(restaurant_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_order_items_menu_item_id ON order_items(menu_item_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_order_status_history_order_id ON order_status_history(order_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_order_status_history_status ON order_status_history(status)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_order_status_history_created_at ON order_status_history(created_at)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_order_tracking_order ON order_tracking(order_id)');

      // Review indexes
      await pool.query('CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON reviews(user_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_reviews_restaurant_id ON reviews(restaurant_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_reviews_menu_item_id ON reviews(menu_item_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_reviews_rating ON reviews(rating)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_reviews_created_at ON reviews(created_at)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_reviews_verified ON reviews(is_verified)');

      // Favorites indexes
      await pool.query('CREATE INDEX IF NOT EXISTS idx_user_favorites_user_id ON user_favorites(user_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_user_favorites_restaurant_id ON user_favorites(restaurant_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_user_favorites_menu_item_id ON user_favorites(menu_item_id)');

      // Session indexes
      await pool.query('CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_user_sessions_refresh_token ON user_sessions(refresh_token)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active)');

      // Promo code indexes
      await pool.query('CREATE INDEX IF NOT EXISTS idx_promo_codes_code ON promo_codes(code)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_promo_codes_active ON promo_codes(is_active)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_promo_codes_expires_at ON promo_codes(expires_at)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_promo_code_usage_promo_code_id ON promo_code_usage(promo_code_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_promo_code_usage_user_id ON promo_code_usage(user_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_promo_code_usage_order_id ON promo_code_usage(order_id)');

      // Notification indexes
      await pool.query('CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications(user_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(is_read)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_notifications_created ON notifications(created_at)');

      // Push notification token indexes
      await pool.query('CREATE INDEX IF NOT EXISTS idx_push_tokens_user_id ON push_notification_tokens(user_id)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_push_tokens_token ON push_notification_tokens(token)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_push_tokens_platform ON push_notification_tokens(platform)');
      await pool.query('CREATE INDEX IF NOT EXISTS idx_push_tokens_active ON push_notification_tokens(is_active)');

      console.log('✅ Database indexes created');

      // Create unique constraints
      console.log('🔒 Creating unique constraints...');
      await pool.query('CREATE UNIQUE INDEX IF NOT EXISTS idx_reviews_user_order ON reviews(user_id, order_id) WHERE order_id IS NOT NULL');
      await pool.query('CREATE UNIQUE INDEX IF NOT EXISTS idx_user_favorites_user_restaurant ON user_favorites(user_id, restaurant_id) WHERE restaurant_id IS NOT NULL');
      await pool.query('CREATE UNIQUE INDEX IF NOT EXISTS idx_user_favorites_user_menu_item ON user_favorites(user_id, menu_item_id) WHERE menu_item_id IS NOT NULL');
      await pool.query('CREATE UNIQUE INDEX IF NOT EXISTS idx_promo_code_usage_unique ON promo_code_usage(promo_code_id, user_id, order_id)');
      await pool.query('CREATE UNIQUE INDEX IF NOT EXISTS idx_push_tokens_unique ON push_notification_tokens(token, user_id)');
      console.log('✅ Unique constraints created');

      // Create triggers for updated_at timestamps
      console.log('⚡ Creating timestamp triggers...');
      await pool.query(`
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
        END;
        $$ language 'plpgsql';
      `);

      const tablesWithUpdatedAt = [
        'users', 'user_addresses', 'payment_methods', 'restaurants',
        'menu_categories', 'menu_items', 'menu_item_customizations', 'customization_options',
        'orders', 'order_items', 'reviews', 'promo_codes', 'push_notification_tokens'
      ];

      for (const table of tablesWithUpdatedAt) {
        await pool.query(`
          DROP TRIGGER IF EXISTS update_${table}_updated_at ON ${table};
          CREATE TRIGGER update_${table}_updated_at
          BEFORE UPDATE ON ${table}
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        `);
      }
      console.log('✅ Timestamp triggers created');

      // Create distance calculation function
      console.log('📐 Creating distance calculation function...');
      await pool.query(`
        CREATE OR REPLACE FUNCTION calculate_distance(
          lat1 DECIMAL, lon1 DECIMAL,
          lat2 DECIMAL, lon2 DECIMAL
        ) RETURNS DECIMAL AS $$
        BEGIN
          RETURN (
            6371 * acos(
              cos(radians(lat1)) * cos(radians(lat2)) *
              cos(radians(lon2) - radians(lon1)) +
              sin(radians(lat1)) * sin(radians(lat2))
            )
          );
        END;
        $$ LANGUAGE plpgsql;
      `);
      console.log('✅ Distance calculation function created');

      // Create order number generation function and sequence
      console.log('🔢 Creating order number generation...');
      await pool.query(`CREATE SEQUENCE IF NOT EXISTS order_number_seq START 1`);
      await pool.query(`
        CREATE OR REPLACE FUNCTION generate_order_number()
        RETURNS TRIGGER AS $$
        BEGIN
          NEW.order_number := 'ORD-' || LPAD(nextval('order_number_seq')::TEXT, 6, '0');
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `);
      await pool.query(`
        DROP TRIGGER IF EXISTS set_order_number ON orders;
        CREATE TRIGGER set_order_number
          BEFORE INSERT ON orders
          FOR EACH ROW
          EXECUTE FUNCTION generate_order_number();
      `);
      console.log('✅ Order number generation created');

      // Create restaurant rating update function
      console.log('⭐ Creating restaurant rating update function...');
      await pool.query(`
        CREATE OR REPLACE FUNCTION update_restaurant_rating()
        RETURNS TRIGGER AS $$
        BEGIN
          UPDATE restaurants
          SET
            rating = (
              SELECT COALESCE(AVG(rating), 0)
              FROM reviews
              WHERE restaurant_id = NEW.restaurant_id
              AND is_active = true
            ),
            review_count = (
              SELECT COUNT(*)
              FROM reviews
              WHERE restaurant_id = NEW.restaurant_id
              AND is_active = true
            )
          WHERE id = NEW.restaurant_id;
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `);
      await pool.query(`
        DROP TRIGGER IF EXISTS update_restaurant_rating_trigger ON reviews;
        CREATE TRIGGER update_restaurant_rating_trigger
          AFTER INSERT OR UPDATE OR DELETE ON reviews
          FOR EACH ROW
          EXECUTE FUNCTION update_restaurant_rating();
      `);
      console.log('✅ Restaurant rating update function created');

      // Create order status history function
      console.log('📊 Creating order status history function...');
      await pool.query(`
        CREATE OR REPLACE FUNCTION update_order_status_history()
        RETURNS TRIGGER AS $$
        BEGIN
          IF OLD.status IS DISTINCT FROM NEW.status THEN
            INSERT INTO order_status_history (order_id, status, note, created_at)
            VALUES (NEW.id, NEW.status, 'Status updated automatically', NOW());

            CASE NEW.status
              WHEN 'confirmed' THEN
                NEW.confirmed_at = NOW();
              WHEN 'preparing' THEN
                NEW.prepared_at = NOW();
              WHEN 'ready' THEN
                NEW.ready_at = NOW();
              WHEN 'out_for_delivery' THEN
                NEW.picked_up_at = NOW();
              WHEN 'delivered' THEN
                NEW.delivered_at = NOW();
              WHEN 'cancelled' THEN
                NEW.cancelled_at = NOW();
              ELSE
                -- Do nothing for other statuses
            END CASE;
          END IF;
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `);
      await pool.query(`
        DROP TRIGGER IF EXISTS update_order_status_history_trigger ON orders;
        CREATE TRIGGER update_order_status_history_trigger
          BEFORE UPDATE ON orders
          FOR EACH ROW
          EXECUTE FUNCTION update_order_status_history();
      `);
      console.log('✅ Order status history function created');

      // Create analytics views
      console.log('📊 Creating analytics views...');
      await pool.query(`
        CREATE OR REPLACE VIEW order_analytics AS
        SELECT
          DATE_TRUNC('day', created_at) as date,
          COUNT(*) as total_orders,
          COUNT(*) FILTER (WHERE status = 'delivered') as completed_orders,
          COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_orders,
          AVG(total_amount) FILTER (WHERE status = 'delivered') as avg_order_value,
          SUM(total_amount) FILTER (WHERE status = 'delivered') as total_revenue
        FROM orders
        GROUP BY DATE_TRUNC('day', created_at)
        ORDER BY date DESC;
      `);

      await pool.query(`
        CREATE OR REPLACE VIEW popular_menu_items AS
        SELECT
          mi.id,
          mi.name,
          mi.price,
          mi.image,
          COUNT(oi.id) as order_count,
          SUM(oi.quantity) as total_quantity,
          AVG(r.rating) as avg_rating,
          COUNT(r.id) as review_count
        FROM menu_items mi
        LEFT JOIN order_items oi ON mi.id = oi.menu_item_id
        LEFT JOIN orders o ON oi.order_id = o.id AND o.status = 'delivered'
        LEFT JOIN reviews r ON mi.id = r.menu_item_id
        WHERE mi.is_available = true
        GROUP BY mi.id, mi.name, mi.price, mi.image
        ORDER BY order_count DESC, total_quantity DESC;
      `);
      console.log('✅ Analytics views created');

      // Insert sample restaurant data
      console.log('🏪 Inserting sample restaurant data...');
      await pool.query(`
        INSERT INTO restaurants (
          name, description, image, logo, slug, phone, email, website,
          cuisine_types, delivery_fee, minimum_order, max_delivery_distance,
          estimated_delivery_time, accepting_orders, features, opening_hours, address
        ) VALUES (
          'FoodWay Restaurant',
          'Authentic Italian cuisine with a modern twist. Fresh ingredients, traditional recipes, and exceptional service.',
          'https://example.com/restaurant-hero.jpg',
          'https://example.com/restaurant-logo.png',
          'foodway-restaurant',
          '******-123-4567',
          '<EMAIL>',
          'https://foodway.com',
          ARRAY['Italian', 'Mediterranean', 'Pizza'],
          3.99,
          15.00,
          10,
          '25-35 min',
          true,
          ARRAY['delivery', 'pickup', 'dine_in'],
          '{
            "monday": {"open": "11:00", "close": "22:00"},
            "tuesday": {"open": "11:00", "close": "22:00"},
            "wednesday": {"open": "11:00", "close": "22:00"},
            "thursday": {"open": "11:00", "close": "22:00"},
            "friday": {"open": "11:00", "close": "23:00"},
            "saturday": {"open": "10:00", "close": "23:00"},
            "sunday": {"open": "10:00", "close": "21:00"}
          }',
          '{
            "street": "123 Main Street",
            "city": "New York",
            "state": "NY",
            "zipCode": "10001",
            "country": "USA",
            "latitude": 40.7128,
            "longitude": -74.0060
          }'
        ) ON CONFLICT (slug) DO NOTHING;
      `);
      console.log('✅ Sample restaurant data inserted');

      // Record the migration
      await pool.query(
        "INSERT INTO migrations (name) VALUES ('comprehensive_foodway_schema')"
      );
      console.log('✅ Comprehensive FoodWay schema migration completed');
    } else {
      console.log('ℹ️  Schema migration already exists, skipping...');
    }

    console.log('\n🎉 Database migration completed successfully!');
    console.log('================================');
    console.log('📊 Database is ready for development');
    console.log('🚀 You can now start building your FoodWay application');
    console.log('================================\n');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run migration if called directly
if (require.main === module) {
  migrate();
}

module.exports = migrate;
