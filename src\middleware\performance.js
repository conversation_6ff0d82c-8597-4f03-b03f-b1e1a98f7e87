// Performance Monitoring Middleware
const config = require('../config/app');

class PerformanceMiddleware {
  // Request timing middleware
  static requestTimer(req, res, next) {
    const startTime = Date.now();
    
    // Add timing info to request
    req.startTime = startTime;
    
    // Override res.end to capture response time
    const originalEnd = res.end;
    res.end = function(...args) {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // Add response time header
      res.set('X-Response-Time', `${responseTime}ms`);
      
      // Log slow requests (>5 seconds)
      if (responseTime > 5000) {
        console.warn(`🐌 SLOW REQUEST: ${req.method} ${req.originalUrl} - ${responseTime}ms`);
        console.warn(`   User-Agent: ${req.get('User-Agent')}`);
        console.warn(`   IP: ${req.ip}`);
        if (req.user) {
          console.warn(`   User ID: ${req.user.id}`);
        }
      }
      
      // Log very slow requests (>10 seconds)
      if (responseTime > 10000) {
        console.error(`🚨 VERY SLOW REQUEST: ${req.method} ${req.originalUrl} - ${responseTime}ms`);
        // In production, you might want to send this to monitoring service
      }
      
      originalEnd.apply(this, args);
    };
    
    next();
  }

  // Database query performance monitoring
  static monitorDatabaseQueries(pool) {
    if (!pool || config.isProduction) {
      return; // Skip in production to avoid overhead
    }

    const originalQuery = pool.query;
    pool.query = async function(text, params) {
      const startTime = Date.now();
      
      try {
        const result = await originalQuery.call(this, text, params);
        const queryTime = Date.now() - startTime;
        
        // Log slow queries (>1 second)
        if (queryTime > 1000) {
          console.warn(`🐌 SLOW QUERY (${queryTime}ms):`, text.substring(0, 100) + '...');
        }
        
        // Log very slow queries (>3 seconds)
        if (queryTime > 3000) {
          console.error(`🚨 VERY SLOW QUERY (${queryTime}ms):`, text);
          console.error('   Params:', params);
        }
        
        return result;
      } catch (error) {
        const queryTime = Date.now() - startTime;
        console.error(`❌ QUERY ERROR (${queryTime}ms):`, error.message);
        console.error('   Query:', text.substring(0, 200));
        throw error;
      }
    };
  }

  // Memory usage monitoring
  static memoryMonitor(req, res, next) {
    if (config.isDevelopment) {
      const memUsage = process.memoryUsage();
      const memMB = {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024)
      };
      
      // Log high memory usage (>500MB)
      if (memMB.heapUsed > 500) {
        console.warn(`🧠 HIGH MEMORY USAGE: ${memMB.heapUsed}MB heap used`);
      }
      
      // Add memory info to response headers in development
      res.set('X-Memory-Usage', JSON.stringify(memMB));
    }
    
    next();
  }

  // Request size monitoring
  static requestSizeMonitor(req, res, next) {
    const contentLength = req.get('content-length');
    
    if (contentLength) {
      const sizeMB = parseInt(contentLength) / 1024 / 1024;
      
      // Log large requests (>10MB)
      if (sizeMB > 10) {
        console.warn(`📦 LARGE REQUEST: ${req.method} ${req.originalUrl} - ${sizeMB.toFixed(2)}MB`);
      }
      
      // Reject very large requests (>50MB)
      if (sizeMB > 50) {
        return res.status(413).json({
          success: false,
          message: 'Request too large',
          error: {
            code: 'REQUEST_TOO_LARGE',
            maxSize: '50MB'
          }
        });
      }
    }
    
    next();
  }

  // Health check with performance metrics
  static getPerformanceMetrics() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        external: Math.round(memUsage.external / 1024 / 1024) // MB
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      uptime: Math.round(process.uptime()),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    };
  }

  // Connection pool monitoring
  static monitorConnectionPool(pool) {
    if (!pool) return null;
    
    return {
      totalCount: pool.totalCount,
      idleCount: pool.idleCount,
      waitingCount: pool.waitingCount
    };
  }
}

module.exports = PerformanceMiddleware;
