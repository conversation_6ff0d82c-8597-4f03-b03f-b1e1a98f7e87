# FoodWay Restaurant App - Database Schema

## Overview
This document outlines the PostgreSQL database schema required for the FoodWay Restaurant food delivery application.

## Database Configuration
- **Database**: PostgreSQL 14+
- **Encoding**: UTF-8
- **Timezone**: UTC
- **Extensions**: uuid-ossp, postgis (for location features)

---

## 👤 Users Table

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    avatar TEXT,
    date_of_birth DATE,
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_created_at ON users(created_at);
```

**Preferences JSONB Structure:**
```json
{
  "notifications": true,
  "marketing": false,
  "dietary": ["vegetarian", "gluten_free"],
  "language": "en",
  "currency": "USD"
}
```

---

## 🏪 Restaurants Table

```sql
CREATE TABLE restaurants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image TEXT,
    logo TEXT,
    slug VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    website TEXT,
    rating DECIMAL(3,2) DEFAULT 0.00,
    review_count INTEGER DEFAULT 0,
    cuisine_types TEXT[] DEFAULT '{}',
    delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    minimum_order DECIMAL(10,2) DEFAULT 0.00,
    max_delivery_distance INTEGER DEFAULT 10, -- in kilometers
    estimated_delivery_time VARCHAR(50),
    accepting_orders BOOLEAN DEFAULT TRUE,
    features TEXT[] DEFAULT '{}', -- ["delivery", "pickup", "dine_in"]
    opening_hours JSONB DEFAULT '{}',
    address JSONB NOT NULL,
    social_media JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_restaurants_slug ON restaurants(slug);
CREATE INDEX idx_restaurants_cuisine_types ON restaurants USING GIN(cuisine_types);
CREATE INDEX idx_restaurants_rating ON restaurants(rating);
CREATE INDEX idx_restaurants_accepting_orders ON restaurants(accepting_orders);
```

**Address JSONB Structure:**
```json
{
  "street": "123 Main St",
  "apartment": "Suite 100",
  "city": "New York",
  "state": "NY",
  "zipCode": "10001",
  "country": "USA",
  "latitude": 40.7128,
  "longitude": -74.0060
}
```

**Opening Hours JSONB Structure:**
```json
{
  "monday": {"open": "11:00", "close": "22:00"},
  "tuesday": {"open": "11:00", "close": "22:00"},
  "wednesday": {"open": "11:00", "close": "22:00"},
  "thursday": {"open": "11:00", "close": "22:00"},
  "friday": {"open": "11:00", "close": "23:00"},
  "saturday": {"open": "10:00", "close": "23:00"},
  "sunday": {"open": "10:00", "close": "21:00", "closed": false}
}
```

---

## 🍽️ Menu Categories Table

```sql
CREATE TABLE menu_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_menu_categories_restaurant_id ON menu_categories(restaurant_id);
CREATE INDEX idx_menu_categories_sort_order ON menu_categories(sort_order);
CREATE INDEX idx_menu_categories_active ON menu_categories(is_active);
```

---

## 🍕 Menu Items Table

```sql
CREATE TABLE menu_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES menu_categories(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    image TEXT,
    ingredients TEXT[] DEFAULT '{}',
    allergens TEXT[] DEFAULT '{}',
    preparation_time INTEGER DEFAULT 0, -- in minutes
    nutrition_info JSONB DEFAULT '{}',
    is_available BOOLEAN DEFAULT TRUE,
    is_popular BOOLEAN DEFAULT FALSE,
    is_vegetarian BOOLEAN DEFAULT FALSE,
    is_vegan BOOLEAN DEFAULT FALSE,
    is_gluten_free BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_menu_items_restaurant_id ON menu_items(restaurant_id);
CREATE INDEX idx_menu_items_category_id ON menu_items(category_id);
CREATE INDEX idx_menu_items_available ON menu_items(is_available);
CREATE INDEX idx_menu_items_popular ON menu_items(is_popular);
CREATE INDEX idx_menu_items_price ON menu_items(price);
CREATE INDEX idx_menu_items_dietary ON menu_items(is_vegetarian, is_vegan, is_gluten_free);
```

**Nutrition Info JSONB Structure:**
```json
{
  "calories": 280,
  "protein": 12,
  "carbs": 35,
  "fat": 10,
  "fiber": 3,
  "sugar": 5,
  "sodium": 650
}
```

---

## ⚙️ Menu Item Customizations Table

```sql
CREATE TABLE menu_item_customizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    menu_item_id UUID NOT NULL REFERENCES menu_items(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_required BOOLEAN DEFAULT FALSE,
    allow_multiple BOOLEAN DEFAULT FALSE,
    min_selections INTEGER DEFAULT 0,
    max_selections INTEGER,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_customizations_menu_item_id ON menu_item_customizations(menu_item_id);
CREATE INDEX idx_customizations_sort_order ON menu_item_customizations(sort_order);
```

---

## 🔧 Customization Options Table

```sql
CREATE TABLE customization_options (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customization_id UUID NOT NULL REFERENCES menu_item_customizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price_modifier DECIMAL(10,2) DEFAULT 0.00,
    is_available BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_customization_options_customization_id ON customization_options(customization_id);
CREATE INDEX idx_customization_options_available ON customization_options(is_available);
CREATE INDEX idx_customization_options_sort_order ON customization_options(sort_order);
```

---

## 📍 User Addresses Table

```sql
CREATE TABLE user_addresses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    label VARCHAR(100) NOT NULL, -- "Home", "Work", etc.
    street VARCHAR(255) NOT NULL,
    apartment VARCHAR(100),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    zip_code VARCHAR(20) NOT NULL,
    country VARCHAR(100) NOT NULL DEFAULT 'USA',
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    delivery_instructions TEXT,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_user_addresses_user_id ON user_addresses(user_id);
CREATE INDEX idx_user_addresses_default ON user_addresses(is_default);
CREATE INDEX idx_user_addresses_location ON user_addresses(latitude, longitude);
```

---

## 💳 Payment Methods Table

```sql
CREATE TABLE payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- "card", "paypal", "apple_pay", "google_pay"
    provider VARCHAR(100), -- "stripe", "paypal", etc.
    external_id VARCHAR(255), -- Provider's payment method ID
    last_four VARCHAR(4),
    brand VARCHAR(50), -- "visa", "mastercard", etc.
    expiry_month INTEGER,
    expiry_year INTEGER,
    cardholder_name VARCHAR(255),
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_payment_methods_user_id ON payment_methods(user_id);
CREATE INDEX idx_payment_methods_default ON payment_methods(is_default);
CREATE INDEX idx_payment_methods_type ON payment_methods(type);
```

---

## 📋 Orders Table

```sql
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id),
    restaurant_id UUID NOT NULL REFERENCES restaurants(id),
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    -- Status: pending, confirmed, preparing, ready, out_for_delivery, delivered, cancelled

    -- Pricing
    subtotal DECIMAL(10,2) NOT NULL,
    tax DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    delivery_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    tip DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    discount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,

    -- Delivery Information
    delivery_address JSONB NOT NULL,
    delivery_instructions TEXT,
    estimated_delivery_time TIMESTAMP WITH TIME ZONE,
    actual_delivery_time TIMESTAMP WITH TIME ZONE,

    -- Payment Information
    payment_method_id UUID REFERENCES payment_methods(id),
    payment_status VARCHAR(50) DEFAULT 'pending',
    -- Payment Status: pending, paid, failed, refunded
    payment_intent_id VARCHAR(255), -- Stripe payment intent ID

    -- Special Instructions
    special_instructions TEXT,

    -- Timestamps
    confirmed_at TIMESTAMP WITH TIME ZONE,
    prepared_at TIMESTAMP WITH TIME ZONE,
    ready_at TIMESTAMP WITH TIME ZONE,
    picked_up_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_restaurant_id ON orders(restaurant_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_payment_status ON orders(payment_status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_order_number ON orders(order_number);

-- Generate order number function
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.order_number := 'ORD-' || LPAD(nextval('order_number_seq')::TEXT, 6, '0');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create sequence for order numbers
CREATE SEQUENCE order_number_seq START 1;

-- Create trigger
CREATE TRIGGER set_order_number
    BEFORE INSERT ON orders
    FOR EACH ROW
    EXECUTE FUNCTION generate_order_number();
```

---

## 🛒 Order Items Table

```sql
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    menu_item_id UUID NOT NULL REFERENCES menu_items(id),
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    special_instructions TEXT,
    customizations JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_menu_item_id ON order_items(menu_item_id);
```

**Customizations JSONB Structure:**
```json
[
  {
    "customizationId": "uuid",
    "customizationName": "Size",
    "optionId": "uuid",
    "optionName": "Large",
    "priceModifier": 3.00
  },
  {
    "customizationId": "uuid",
    "customizationName": "Toppings",
    "optionId": "uuid",
    "optionName": "Extra Cheese",
    "priceModifier": 2.50
  }
]
```

---

## 📊 Order Status History Table

```sql
CREATE TABLE order_status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    status VARCHAR(50) NOT NULL,
    note TEXT,
    changed_by UUID REFERENCES users(id), -- NULL for system changes
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_order_status_history_order_id ON order_status_history(order_id);
CREATE INDEX idx_order_status_history_status ON order_status_history(status);
CREATE INDEX idx_order_status_history_created_at ON order_status_history(created_at);
```

---

## ⭐ Reviews Table

```sql
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    restaurant_id UUID NOT NULL REFERENCES restaurants(id),
    order_id UUID REFERENCES orders(id),
    menu_item_id UUID REFERENCES menu_items(id), -- NULL for restaurant reviews
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    comment TEXT,
    images TEXT[] DEFAULT '{}',
    is_verified BOOLEAN DEFAULT FALSE, -- Verified purchase
    helpful_count INTEGER DEFAULT 0,
    response TEXT, -- Restaurant response
    response_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_reviews_user_id ON reviews(user_id);
CREATE INDEX idx_reviews_restaurant_id ON reviews(restaurant_id);
CREATE INDEX idx_reviews_menu_item_id ON reviews(menu_item_id);
CREATE INDEX idx_reviews_rating ON reviews(rating);
CREATE INDEX idx_reviews_created_at ON reviews(created_at);
CREATE INDEX idx_reviews_verified ON reviews(is_verified);

-- Unique constraint: one review per user per order
CREATE UNIQUE INDEX idx_reviews_user_order ON reviews(user_id, order_id) WHERE order_id IS NOT NULL;
```

---

## 🎯 User Favorites Table

```sql
CREATE TABLE user_favorites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    restaurant_id UUID REFERENCES restaurants(id) ON DELETE CASCADE,
    menu_item_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure either restaurant_id or menu_item_id is set, but not both
    CONSTRAINT check_favorite_type CHECK (
        (restaurant_id IS NOT NULL AND menu_item_id IS NULL) OR
        (restaurant_id IS NULL AND menu_item_id IS NOT NULL)
    )
);

-- Indexes
CREATE INDEX idx_user_favorites_user_id ON user_favorites(user_id);
CREATE INDEX idx_user_favorites_restaurant_id ON user_favorites(restaurant_id);
CREATE INDEX idx_user_favorites_menu_item_id ON user_favorites(menu_item_id);

-- Unique constraints
CREATE UNIQUE INDEX idx_user_favorites_user_restaurant ON user_favorites(user_id, restaurant_id) WHERE restaurant_id IS NOT NULL;
CREATE UNIQUE INDEX idx_user_favorites_user_menu_item ON user_favorites(user_id, menu_item_id) WHERE menu_item_id IS NOT NULL;
```

---

## 🔐 User Sessions Table (Redis Alternative)

```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_refresh_token ON user_sessions(refresh_token);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX idx_user_sessions_active ON user_sessions(is_active);
```

---

## 🎫 Promo Codes Table

```sql
CREATE TABLE promo_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL, -- "percentage", "fixed_amount", "free_delivery"
    value DECIMAL(10,2) NOT NULL,
    minimum_order DECIMAL(10,2) DEFAULT 0.00,
    maximum_discount DECIMAL(10,2),
    usage_limit INTEGER,
    usage_count INTEGER DEFAULT 0,
    user_limit INTEGER DEFAULT 1, -- Per user usage limit
    is_active BOOLEAN DEFAULT TRUE,
    starts_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_promo_codes_code ON promo_codes(code);
CREATE INDEX idx_promo_codes_active ON promo_codes(is_active);
CREATE INDEX idx_promo_codes_expires_at ON promo_codes(expires_at);
```

---

## 🎫 Promo Code Usage Table

```sql
CREATE TABLE promo_code_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    promo_code_id UUID NOT NULL REFERENCES promo_codes(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    discount_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_promo_code_usage_promo_code_id ON promo_code_usage(promo_code_id);
CREATE INDEX idx_promo_code_usage_user_id ON promo_code_usage(user_id);
CREATE INDEX idx_promo_code_usage_order_id ON promo_code_usage(order_id);

-- Unique constraint: one usage per user per promo code per order
CREATE UNIQUE INDEX idx_promo_code_usage_unique ON promo_code_usage(promo_code_id, user_id, order_id);
```

---

## 📱 Push Notification Tokens Table

```sql
CREATE TABLE push_notification_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL,
    platform VARCHAR(50) NOT NULL, -- "ios", "android", "web"
    device_id VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_push_tokens_user_id ON push_notification_tokens(user_id);
CREATE INDEX idx_push_tokens_token ON push_notification_tokens(token);
CREATE INDEX idx_push_tokens_platform ON push_notification_tokens(platform);
CREATE INDEX idx_push_tokens_active ON push_notification_tokens(is_active);

-- Unique constraint: one token per device
CREATE UNIQUE INDEX idx_push_tokens_unique ON push_notification_tokens(token, device_id);
```

---

## 📊 Analytics Tables

### Order Analytics View
```sql
CREATE VIEW order_analytics AS
SELECT
    DATE_TRUNC('day', created_at) as date,
    COUNT(*) as total_orders,
    COUNT(*) FILTER (WHERE status = 'delivered') as completed_orders,
    COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_orders,
    AVG(total_amount) FILTER (WHERE status = 'delivered') as avg_order_value,
    SUM(total_amount) FILTER (WHERE status = 'delivered') as total_revenue
FROM orders
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY date DESC;
```

### Popular Menu Items View
```sql
CREATE VIEW popular_menu_items AS
SELECT
    mi.id,
    mi.name,
    mi.price,
    mi.image,
    COUNT(oi.id) as order_count,
    SUM(oi.quantity) as total_quantity,
    AVG(r.rating) as avg_rating,
    COUNT(r.id) as review_count
FROM menu_items mi
LEFT JOIN order_items oi ON mi.id = oi.menu_item_id
LEFT JOIN orders o ON oi.order_id = o.id AND o.status = 'delivered'
LEFT JOIN reviews r ON mi.id = r.menu_item_id
WHERE mi.is_available = true
GROUP BY mi.id, mi.name, mi.price, mi.image
ORDER BY order_count DESC, total_quantity DESC;
```

---

## 🔧 Database Functions

### Update Restaurant Rating Function
```sql
CREATE OR REPLACE FUNCTION update_restaurant_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE restaurants
    SET
        rating = (
            SELECT COALESCE(AVG(rating), 0)
            FROM reviews
            WHERE restaurant_id = NEW.restaurant_id
            AND is_active = true
        ),
        review_count = (
            SELECT COUNT(*)
            FROM reviews
            WHERE restaurant_id = NEW.restaurant_id
            AND is_active = true
        )
    WHERE id = NEW.restaurant_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER update_restaurant_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_restaurant_rating();
```

### Update Order Status History Function
```sql
CREATE OR REPLACE FUNCTION update_order_status_history()
RETURNS TRIGGER AS $$
BEGIN
    -- Only insert if status actually changed
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO order_status_history (order_id, status, note, created_at)
        VALUES (NEW.id, NEW.status, 'Status updated automatically', NOW());

        -- Update timestamp fields based on status
        CASE NEW.status
            WHEN 'confirmed' THEN
                NEW.confirmed_at = NOW();
            WHEN 'preparing' THEN
                NEW.prepared_at = NOW();
            WHEN 'ready' THEN
                NEW.ready_at = NOW();
            WHEN 'out_for_delivery' THEN
                NEW.picked_up_at = NOW();
            WHEN 'delivered' THEN
                NEW.delivered_at = NOW();
            WHEN 'cancelled' THEN
                NEW.cancelled_at = NOW();
            ELSE
                -- Do nothing for other statuses
        END CASE;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER update_order_status_history_trigger
    BEFORE UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_order_status_history();
```

---

## 📋 Initial Data Seeds

### Default Restaurant Data
```sql
INSERT INTO restaurants (
    id, name, description, image, logo, slug, phone, email, website,
    cuisine_types, delivery_fee, minimum_order, max_delivery_distance,
    estimated_delivery_time, accepting_orders, features, opening_hours, address
) VALUES (
    gen_random_uuid(),
    'FoodWay Restaurant',
    'Authentic Italian cuisine with a modern twist. Fresh ingredients, traditional recipes, and exceptional service.',
    'https://example.com/restaurant-hero.jpg',
    'https://example.com/restaurant-logo.png',
    'foodway-restaurant',
    '******-123-4567',
    '<EMAIL>',
    'https://foodway.com',
    ARRAY['Italian', 'Mediterranean', 'Pizza'],
    3.99,
    15.00,
    10,
    '25-35 min',
    true,
    ARRAY['delivery', 'pickup', 'dine_in'],
    '{
        "monday": {"open": "11:00", "close": "22:00"},
        "tuesday": {"open": "11:00", "close": "22:00"},
        "wednesday": {"open": "11:00", "close": "22:00"},
        "thursday": {"open": "11:00", "close": "22:00"},
        "friday": {"open": "11:00", "close": "23:00"},
        "saturday": {"open": "10:00", "close": "23:00"},
        "sunday": {"open": "10:00", "close": "21:00"}
    }',
    '{
        "street": "123 Main Street",
        "city": "New York",
        "state": "NY",
        "zipCode": "10001",
        "country": "USA",
        "latitude": 40.7128,
        "longitude": -74.0060
    }'
);
```

---

## 🔍 Useful Queries

### Get Order Statistics
```sql
SELECT
    COUNT(*) as total_orders,
    COUNT(*) FILTER (WHERE status = 'delivered') as delivered_orders,
    COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_orders,
    AVG(total_amount) FILTER (WHERE status = 'delivered') as avg_order_value,
    SUM(total_amount) FILTER (WHERE status = 'delivered') as total_revenue
FROM orders
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days';
```

### Get Top Customers
```sql
SELECT
    u.id,
    u.first_name,
    u.last_name,
    u.email,
    COUNT(o.id) as order_count,
    SUM(o.total_amount) as total_spent,
    AVG(o.total_amount) as avg_order_value
FROM users u
JOIN orders o ON u.id = o.user_id
WHERE o.status = 'delivered'
GROUP BY u.id, u.first_name, u.last_name, u.email
ORDER BY total_spent DESC
LIMIT 10;
```

### Get Menu Item Performance
```sql
SELECT
    mi.name,
    COUNT(oi.id) as times_ordered,
    SUM(oi.quantity) as total_quantity,
    SUM(oi.total_price) as total_revenue,
    AVG(r.rating) as avg_rating
FROM menu_items mi
LEFT JOIN order_items oi ON mi.id = oi.menu_item_id
LEFT JOIN orders o ON oi.order_id = o.id AND o.status = 'delivered'
LEFT JOIN reviews r ON mi.id = r.menu_item_id
GROUP BY mi.id, mi.name
ORDER BY times_ordered DESC;
```
