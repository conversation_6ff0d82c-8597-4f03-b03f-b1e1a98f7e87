// Order Service
const databaseManager = require('../config/database');
const PaymentService = require('./paymentService');
const socketService = require('./socketService');
const { v4: uuidv4 } = require('uuid');

class OrderService {
  // Create a new order
  static async createOrder(userId, orderData) {
    const pool = databaseManager.getPool();
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      const { items, deliveryAddressId, paymentMethodId, tip, specialInstructions } = orderData;

      // Get delivery address
      const addressResult = await client.query(
        'SELECT * FROM user_addresses WHERE id = $1 AND user_id = $2',
        [deliveryAddressId, userId]
      );

      if (addressResult.rows.length === 0) {
        throw new Error('Delivery address not found');
      }

      const deliveryAddress = addressResult.rows[0];

      // Get restaurant info (assuming single restaurant app)
      const restaurantResult = await client.query(
        'SELECT id, delivery_fee, minimum_order FROM restaurants WHERE is_active = true LIMIT 1'
      );

      if (restaurantResult.rows.length === 0) {
        throw new Error('Restaurant not available');
      }

      const restaurant = restaurantResult.rows[0];

      // Calculate order totals
      let subtotal = 0;
      const orderItems = [];

      for (const item of items) {
        const menuItemResult = await client.query(
          'SELECT id, name, price, is_available FROM menu_items WHERE id = $1',
          [item.menuItemId]
        );

        if (menuItemResult.rows.length === 0 || !menuItemResult.rows[0].is_available) {
          throw new Error(`Menu item ${item.menuItemId} not available`);
        }

        const menuItem = menuItemResult.rows[0];
        const itemTotal = parseFloat(menuItem.price) * item.quantity;
        subtotal += itemTotal;

        orderItems.push({
          menuItemId: menuItem.id,
          menuItemName: menuItem.name,
          quantity: item.quantity,
          unitPrice: parseFloat(menuItem.price),
          totalPrice: itemTotal,
          customizations: item.customizations || [],
          specialInstructions: item.specialInstructions || null
        });
      }

      // Check minimum order
      if (subtotal < parseFloat(restaurant.minimum_order)) {
        throw new Error(`Minimum order amount is $${restaurant.minimum_order}`);
      }

      // Calculate totals
      const deliveryFee = parseFloat(restaurant.delivery_fee);
      const taxRate = 0.08; // 8% tax
      const taxAmount = subtotal * taxRate;
      const tipAmount = tip || 0;
      const totalAmount = subtotal + taxAmount + deliveryFee + tipAmount;

      // Generate order number
      const orderNumber = `ORD-${Date.now().toString().slice(-6)}`;

      // Create order
      const orderResult = await client.query(`
        INSERT INTO orders (
          order_number, user_id, restaurant_id, status, subtotal, tax_amount,
          delivery_fee, tip_amount, total_amount, payment_method_id,
          delivery_address, special_instructions, estimated_delivery_time
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        RETURNING *
      `, [
        orderNumber, userId, restaurant.id, 'pending', subtotal, taxAmount,
        deliveryFee, tipAmount, totalAmount, paymentMethodId,
        JSON.stringify({
          street: deliveryAddress.street_address,
          apartment: deliveryAddress.apartment || null,
          city: deliveryAddress.city,
          state: deliveryAddress.state,
          zipCode: deliveryAddress.postal_code,
          deliveryInstructions: specialInstructions
        }),
        specialInstructions,
        new Date(Date.now() + 35 * 60 * 1000) // 35 minutes from now
      ]);

      const order = orderResult.rows[0];

      // Create order items
      for (const item of orderItems) {
        await client.query(`
          INSERT INTO order_items (
            order_id, menu_item_id, quantity, unit_price, total_price,
            customizations, special_instructions
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
          order.id, item.menuItemId, item.quantity, item.unitPrice,
          item.totalPrice, JSON.stringify(item.customizations), item.specialInstructions
        ]);
      }

      // Create initial order tracking entry
      await client.query(`
        INSERT INTO order_tracking (order_id, status, message)
        VALUES ($1, $2, $3)
      `, [order.id, 'pending', 'Order received']);

      await client.query('COMMIT');

      // Return formatted order response
      return {
        id: order.id,
        orderNumber: order.order_number,
        status: order.status,
        userId: order.user_id,
        items: orderItems.map(item => ({
          id: uuidv4(), // Generate temp ID for response
          menuItemId: item.menuItemId,
          menuItem: {
            name: item.menuItemName,
            image: null // TODO: Get from menu_items table
          },
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          customizations: item.customizations.map(c => ({
            name: c.name || 'Customization',
            option: c.option || 'Option',
            price: c.price || 0
          }))
        })),
        subtotal: parseFloat(order.subtotal),
        tax: parseFloat(order.tax_amount),
        deliveryFee: parseFloat(order.delivery_fee),
        tip: parseFloat(order.tip_amount),
        totalAmount: parseFloat(order.total_amount),
        deliveryAddress: JSON.parse(order.delivery_address),
        paymentMethod: 'card', // TODO: Get from payment_methods table
        paymentStatus: 'pending',
        estimatedDeliveryTime: order.estimated_delivery_time,
        createdAt: order.created_at
      };

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Get user's orders with pagination and filtering
  static async getUserOrders(userId, filters) {
    const pool = databaseManager.getPool();

    try {
      const { status, page, limit, startDate, endDate } = filters;
      const offset = (page - 1) * limit;

      let whereConditions = ['o.user_id = $1'];
      let queryParams = [userId];
      let paramIndex = 2;

      if (status) {
        whereConditions.push(`o.status = $${paramIndex}`);
        queryParams.push(status);
        paramIndex++;
      }

      if (startDate) {
        whereConditions.push(`o.created_at >= $${paramIndex}`);
        queryParams.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        whereConditions.push(`o.created_at <= $${paramIndex}`);
        queryParams.push(endDate);
        paramIndex++;
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM orders o
        WHERE ${whereClause}
      `;

      const countResult = await pool.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].total);

      // Get orders
      const ordersQuery = `
        SELECT 
          o.id, o.order_number, o.status, o.total_amount, o.created_at,
          o.actual_delivery_time,
          COUNT(oi.id) as item_count,
          r.name as restaurant_name, r.cover_image_url as restaurant_image
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN restaurants r ON o.restaurant_id = r.id
        WHERE ${whereClause}
        GROUP BY o.id, r.name, r.cover_image_url
        ORDER BY o.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      queryParams.push(limit, offset);
      const ordersResult = await pool.query(ordersQuery, queryParams);

      const orders = ordersResult.rows.map(order => ({
        id: order.id,
        orderNumber: order.order_number,
        status: order.status,
        totalAmount: parseFloat(order.total_amount),
        itemCount: parseInt(order.item_count),
        restaurant: {
          name: order.restaurant_name,
          image: order.restaurant_image
        },
        createdAt: order.created_at,
        deliveredAt: order.actual_delivery_time
      }));

      return {
        orders,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      throw error;
    }
  }

  // Get specific order details
  static async getOrderById(userId, orderId) {
    const pool = databaseManager.getPool();

    try {
      const orderQuery = `
        SELECT
          o.*, r.name as restaurant_name, r.cover_image_url as restaurant_image
        FROM orders o
        LEFT JOIN restaurants r ON o.restaurant_id = r.id
        WHERE o.id = $1 AND o.user_id = $2
      `;

      const orderResult = await pool.query(orderQuery, [orderId, userId]);

      if (orderResult.rows.length === 0) {
        return null;
      }

      const order = orderResult.rows[0];

      // Get order items
      const itemsQuery = `
        SELECT
          oi.*, mi.name as menu_item_name, mi.image_url as menu_item_image
        FROM order_items oi
        LEFT JOIN menu_items mi ON oi.menu_item_id = mi.id
        WHERE oi.order_id = $1
      `;

      const itemsResult = await pool.query(itemsQuery, [orderId]);

      const items = itemsResult.rows.map(item => ({
        id: item.id,
        menuItemId: item.menu_item_id,
        menuItem: {
          name: item.menu_item_name,
          image: item.menu_item_image
        },
        quantity: item.quantity,
        unitPrice: parseFloat(item.unit_price),
        totalPrice: parseFloat(item.total_price),
        specialInstructions: item.special_instructions,
        customizations: item.customizations ? JSON.parse(item.customizations) : []
      }));

      return {
        id: order.id,
        orderNumber: order.order_number,
        status: order.status,
        userId: order.user_id,
        items,
        subtotal: parseFloat(order.subtotal),
        tax: parseFloat(order.tax_amount),
        deliveryFee: parseFloat(order.delivery_fee),
        tip: parseFloat(order.tip_amount),
        totalAmount: parseFloat(order.total_amount),
        deliveryAddress: JSON.parse(order.delivery_address),
        paymentMethod: 'card', // TODO: Get from payment_methods table
        paymentStatus: order.stripe_payment_intent_id ? 'paid' : 'pending',
        specialInstructions: order.special_instructions,
        estimatedDeliveryTime: order.estimated_delivery_time,
        actualDeliveryTime: order.actual_delivery_time,
        createdAt: order.created_at,
        updatedAt: order.updated_at
      };

    } catch (error) {
      throw error;
    }
  }

  // Get order tracking information
  static async trackOrder(userId, orderId) {
    const pool = databaseManager.getPool();

    try {
      // First verify the order belongs to the user
      const orderResult = await pool.query(
        'SELECT id, order_number, status, estimated_delivery_time FROM orders WHERE id = $1 AND user_id = $2',
        [orderId, userId]
      );

      if (orderResult.rows.length === 0) {
        return null;
      }

      const order = orderResult.rows[0];

      // Get tracking history
      const trackingQuery = `
        SELECT status, message, latitude, longitude, driver_name, driver_phone, created_at
        FROM order_tracking
        WHERE order_id = $1
        ORDER BY created_at ASC
      `;

      const trackingResult = await pool.query(trackingQuery, [orderId]);

      const statusHistory = trackingResult.rows.map(track => ({
        status: track.status,
        timestamp: track.created_at,
        note: track.message
      }));

      return {
        order: {
          id: order.id,
          orderNumber: order.order_number,
          status: order.status,
          estimatedDeliveryTime: order.estimated_delivery_time
        },
        tracking: {
          currentStatus: order.status,
          estimatedDeliveryTime: order.estimated_delivery_time,
          driverLocation: order.status === 'out_for_delivery' ? {
            latitude: 40.7128,
            longitude: -74.0060
          } : null,
          statusHistory
        }
      };

    } catch (error) {
      throw error;
    }
  }

  // Cancel an order
  static async cancelOrder(userId, orderId, reason) {
    const pool = databaseManager.getPool();
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      // Check if order can be cancelled
      const orderResult = await client.query(
        'SELECT id, status FROM orders WHERE id = $1 AND user_id = $2',
        [orderId, userId]
      );

      if (orderResult.rows.length === 0) {
        throw new Error('Order not found');
      }

      const order = orderResult.rows[0];

      if (!['pending', 'confirmed'].includes(order.status)) {
        throw new Error('Order cannot be cancelled at this stage');
      }

      // Update order status
      await client.query(
        'UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2',
        ['cancelled', orderId]
      );

      // Add tracking entry
      await client.query(`
        INSERT INTO order_tracking (order_id, status, message)
        VALUES ($1, $2, $3)
      `, [orderId, 'cancelled', reason || 'Order cancelled by customer']);

      await client.query('COMMIT');

      return true;

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Update order status with real-time notifications
  static async updateOrderStatus(orderId, newStatus, message = null, driverInfo = null) {
    const pool = databaseManager.getPool();
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      // Update order status
      const orderResult = await client.query(
        'UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2 RETURNING user_id, order_number',
        [newStatus, orderId]
      );

      if (orderResult.rows.length === 0) {
        throw new Error('Order not found');
      }

      const order = orderResult.rows[0];

      // Add tracking entry
      const trackingMessage = message || this.getDefaultStatusMessage(newStatus);
      await client.query(
        'INSERT INTO order_tracking (order_id, status, message, driver_name, driver_phone) VALUES ($1, $2, $3, $4, $5)',
        [orderId, newStatus, trackingMessage, driverInfo?.name || null, driverInfo?.phone || null]
      );

      await client.query('COMMIT');

      // Emit real-time update
      socketService.broadcastOrderUpdate(orderId, {
        status: newStatus,
        message: trackingMessage,
        orderNumber: order.order_number,
        driverInfo
      });

      // Send user notification
      socketService.sendUserNotification(order.user_id, {
        type: 'order_update',
        title: 'Order Update',
        message: `Your order ${order.order_number} is now ${newStatus.replace('_', ' ')}`,
        orderId,
        status: newStatus
      });

      console.log(`Order ${orderId} status updated to ${newStatus}`);
      return true;

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Get default status message
  static getDefaultStatusMessage(status) {
    const messages = {
      'pending': 'Order received and waiting for confirmation',
      'confirmed': 'Order confirmed by restaurant',
      'preparing': 'Kitchen started preparing your order',
      'ready': 'Order ready for pickup',
      'out_for_delivery': 'Driver picked up your order',
      'delivered': 'Order delivered successfully',
      'cancelled': 'Order has been cancelled'
    };

    return messages[status] || `Order status updated to ${status}`;
  }

  // Update driver location (for real-time tracking)
  static async updateDriverLocation(orderId, latitude, longitude) {
    try {
      // Emit driver location update
      socketService.broadcastDriverLocation(orderId, {
        latitude,
        longitude
      });

      console.log(`Driver location updated for order ${orderId}: ${latitude}, ${longitude}`);
      return true;

    } catch (error) {
      console.error('Error updating driver location:', error);
      throw error;
    }
  }
}

module.exports = OrderService;
