// Payment Method Routes
const express = require('express');
const PaymentMethodController = require('../controllers/paymentMethodController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting and authentication to all routes
router.use(apiLimiter);
router.use(AuthMiddleware.authenticate);

// GET /api/v1/payment-methods - Get user's payment methods
router.get('/',
  PaymentMethodController.getUserPaymentMethods
);

// GET /api/v1/payment-methods/default - Get user's default payment method
router.get('/default',
  PaymentMethodController.getDefaultPaymentMethod
);

// GET /api/v1/payment-methods/stats - Get payment method statistics
router.get('/stats',
  PaymentMethodController.getPaymentMethodStats
);

// GET /api/v1/payment-methods/expired - Get expired payment methods
router.get('/expired',
  PaymentMethodController.getExpiredPaymentMethods
);

// POST /api/v1/payment-methods - Create new payment method
router.post('/',
  ValidationMiddleware.validateCreatePaymentMethod(),
  PaymentMethodController.createPaymentMethod
);

// PUT /api/v1/payment-methods/:id - Update payment method
router.put('/:id',
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateUpdatePaymentMethod(),
  PaymentMethodController.updatePaymentMethod
);

// PUT /api/v1/payment-methods/:id/default - Set default payment method
router.put('/:id/default',
  ValidationMiddleware.validateUUID('id'),
  PaymentMethodController.setDefaultPaymentMethod
);

// DELETE /api/v1/payment-methods/:id - Delete payment method
router.delete('/:id',
  ValidationMiddleware.validateUUID('id'),
  PaymentMethodController.deletePaymentMethod
);

module.exports = router;
