// Add Business Hours Table Migration
require('dotenv').config();
const { Pool } = require('pg');

async function addBusinessHoursTable() {
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not found in environment variables');
    process.exit(1);
  }

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    console.log('🚀 Adding business hours table...\n');

    // Check if business_hours table already exists
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'restaurant_business_hours'
      );
    `);

    if (tableCheck.rows[0].exists) {
      console.log('ℹ️  Business hours table already exists, skipping...');
      return;
    }

    // Create restaurant_business_hours table
    console.log('📋 Creating restaurant_business_hours table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS restaurant_business_hours (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
        day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
        open_time TIME,
        close_time TIME,
        is_closed BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        UNIQUE(restaurant_id, day_of_week)
      )
    `);
    console.log('✅ Restaurant business hours table created');

    // Create special hours table for holidays/exceptions
    console.log('📋 Creating restaurant_special_hours table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS restaurant_special_hours (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
        date DATE NOT NULL,
        open_time TIME,
        close_time TIME,
        is_closed BOOLEAN NOT NULL DEFAULT false,
        reason VARCHAR(255),
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        UNIQUE(restaurant_id, date)
      )
    `);
    console.log('✅ Restaurant special hours table created');

    // Create indexes for better performance
    console.log('🔍 Creating business hours indexes...');
    await pool.query('CREATE INDEX IF NOT EXISTS idx_business_hours_restaurant ON restaurant_business_hours(restaurant_id)');
    await pool.query('CREATE INDEX IF NOT EXISTS idx_business_hours_day ON restaurant_business_hours(day_of_week)');
    await pool.query('CREATE INDEX IF NOT EXISTS idx_special_hours_restaurant ON restaurant_special_hours(restaurant_id)');
    await pool.query('CREATE INDEX IF NOT EXISTS idx_special_hours_date ON restaurant_special_hours(date)');
    console.log('✅ Business hours indexes created');

    // Add trigger for updated_at
    await pool.query(`
      DROP TRIGGER IF EXISTS update_restaurant_business_hours_updated_at ON restaurant_business_hours;
      CREATE TRIGGER update_restaurant_business_hours_updated_at
      BEFORE UPDATE ON restaurant_business_hours
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `);

    // Insert default business hours for existing restaurants
    console.log('📊 Adding default business hours for existing restaurants...');
    await pool.query(`
      INSERT INTO restaurant_business_hours (restaurant_id, day_of_week, open_time, close_time)
      SELECT
        r.id,
        days.day_of_week,
        CASE
          WHEN days.day_of_week IN (5, 6) THEN '10:00'::TIME  -- Friday, Saturday
          WHEN days.day_of_week = 0 THEN '10:00'::TIME        -- Sunday
          ELSE '11:00'::TIME                                  -- Monday-Thursday
        END as open_time,
        CASE
          WHEN days.day_of_week IN (5, 6) THEN '23:00'::TIME  -- Friday, Saturday
          WHEN days.day_of_week = 0 THEN '21:00'::TIME        -- Sunday
          ELSE '22:00'::TIME                                  -- Monday-Thursday
        END as close_time
      FROM restaurants r
      CROSS JOIN (SELECT generate_series(0, 6) as day_of_week) days
      WHERE r.is_active = true
      ON CONFLICT (restaurant_id, day_of_week) DO NOTHING
    `);
    console.log('✅ Default business hours added');

    // Record the migration
    await pool.query(
      "INSERT INTO migrations (name) VALUES ('add_business_hours_tables')"
    );

    console.log('\n🎉 Business hours tables migration completed successfully!');
    console.log('================================');
    console.log('📊 Restaurant availability system is enhanced');
    console.log('🕐 Business hours management is ready');
    console.log('================================\n');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run migration if called directly
if (require.main === module) {
  addBusinessHoursTable();
}

module.exports = addBusinessHoursTable;
