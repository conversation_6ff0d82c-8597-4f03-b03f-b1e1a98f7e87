// UserFavorite Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const UserFavorite = require('../models/UserFavorite');

class UserFavoriteController {
  // Get user's favorite restaurants
  static async getFavoriteRestaurants(req, res) {
    try {
      const userId = req.user.id;
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);
      
      const userFavoriteModel = new UserFavorite();
      const result = await userFavoriteModel.getFavoriteRestaurants(userId, page, limit);

      return ResponseHelper.success(res, result, 'Favorite restaurants retrieved successfully');
    } catch (error) {
      console.error('Get favorite restaurants error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve favorite restaurants', 500);
    }
  }

  // Get user's favorite menu items
  static async getFavoriteMenuItems(req, res) {
    try {
      const userId = req.user.id;
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);
      
      const userFavoriteModel = new UserFavorite();
      const result = await userFavoriteModel.getFavoriteMenuItems(userId, page, limit);

      return ResponseHelper.success(res, result, 'Favorite menu items retrieved successfully');
    } catch (error) {
      console.error('Get favorite menu items error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve favorite menu items', 500);
    }
  }

  // Add restaurant to favorites
  static async addRestaurantToFavorites(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const restaurantId = req.params.restaurantId;
      const userFavoriteModel = new UserFavorite();
      
      const favorite = await userFavoriteModel.addRestaurantToFavorites(userId, restaurantId);

      return ResponseHelper.success(res, favorite, 'Restaurant added to favorites successfully', 201);
    } catch (error) {
      console.error('Add restaurant to favorites error:', error);
      
      if (error.message.includes('already in favorites')) {
        return ResponseHelper.error(res, error.message, 409);
      }
      
      if (error.message.includes('not found')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to add restaurant to favorites', 500);
    }
  }

  // Add menu item to favorites
  static async addMenuItemToFavorites(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const menuItemId = req.params.menuItemId;
      const userFavoriteModel = new UserFavorite();
      
      const favorite = await userFavoriteModel.addMenuItemToFavorites(userId, menuItemId);

      return ResponseHelper.success(res, favorite, 'Menu item added to favorites successfully', 201);
    } catch (error) {
      console.error('Add menu item to favorites error:', error);
      
      if (error.message.includes('already in favorites')) {
        return ResponseHelper.error(res, error.message, 409);
      }
      
      if (error.message.includes('not found')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to add menu item to favorites', 500);
    }
  }

  // Remove restaurant from favorites
  static async removeRestaurantFromFavorites(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const restaurantId = req.params.restaurantId;
      const userFavoriteModel = new UserFavorite();
      
      const result = await userFavoriteModel.removeRestaurantFromFavorites(userId, restaurantId);

      return ResponseHelper.success(res, { removed: true }, 'Restaurant removed from favorites successfully');
    } catch (error) {
      console.error('Remove restaurant from favorites error:', error);
      
      if (error.message.includes('not found in favorites')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to remove restaurant from favorites', 500);
    }
  }

  // Remove menu item from favorites
  static async removeMenuItemFromFavorites(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const menuItemId = req.params.menuItemId;
      const userFavoriteModel = new UserFavorite();
      
      const result = await userFavoriteModel.removeMenuItemFromFavorites(userId, menuItemId);

      return ResponseHelper.success(res, { removed: true }, 'Menu item removed from favorites successfully');
    } catch (error) {
      console.error('Remove menu item from favorites error:', error);
      
      if (error.message.includes('not found in favorites')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to remove menu item from favorites', 500);
    }
  }

  // Check if restaurant is favorited
  static async checkRestaurantFavorited(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const restaurantId = req.params.restaurantId;
      const userFavoriteModel = new UserFavorite();
      
      const isFavorited = await userFavoriteModel.isRestaurantFavorited(userId, restaurantId);

      return ResponseHelper.success(res, { isFavorited }, 'Restaurant favorite status retrieved successfully');
    } catch (error) {
      console.error('Check restaurant favorited error:', error);
      return ResponseHelper.error(res, 'Failed to check restaurant favorite status', 500);
    }
  }

  // Check if menu item is favorited
  static async checkMenuItemFavorited(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const menuItemId = req.params.menuItemId;
      const userFavoriteModel = new UserFavorite();
      
      const isFavorited = await userFavoriteModel.isMenuItemFavorited(userId, menuItemId);

      return ResponseHelper.success(res, { isFavorited }, 'Menu item favorite status retrieved successfully');
    } catch (error) {
      console.error('Check menu item favorited error:', error);
      return ResponseHelper.error(res, 'Failed to check menu item favorite status', 500);
    }
  }

  // Get user's favorite statistics
  static async getFavoriteStats(req, res) {
    try {
      const userId = req.user.id;
      const userFavoriteModel = new UserFavorite();
      
      const stats = await userFavoriteModel.getUserFavoriteStats(userId);

      return ResponseHelper.success(res, stats, 'Favorite statistics retrieved successfully');
    } catch (error) {
      console.error('Get favorite stats error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve favorite statistics', 500);
    }
  }

  // Get recommendations based on favorites
  static async getRecommendations(req, res) {
    try {
      const userId = req.user.id;
      const limit = Math.min(parseInt(req.query.limit) || 10, 50);
      const userFavoriteModel = new UserFavorite();
      
      const recommendations = await userFavoriteModel.getRecommendationsBasedOnFavorites(userId, limit);

      return ResponseHelper.success(res, { recommendations }, 'Recommendations retrieved successfully');
    } catch (error) {
      console.error('Get recommendations error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve recommendations', 500);
    }
  }

  // Clear all favorites
  static async clearAllFavorites(req, res) {
    try {
      const userId = req.user.id;
      const userFavoriteModel = new UserFavorite();
      
      const result = await userFavoriteModel.clearAllFavorites(userId);

      return ResponseHelper.success(res, result, 'All favorites cleared successfully');
    } catch (error) {
      console.error('Clear all favorites error:', error);
      return ResponseHelper.error(res, 'Failed to clear all favorites', 500);
    }
  }

  // Get popular favorited restaurants (public endpoint)
  static async getPopularFavoritedRestaurants(req, res) {
    try {
      const limit = Math.min(parseInt(req.query.limit) || 10, 50);
      const days = parseInt(req.query.days) || 30;
      const userFavoriteModel = new UserFavorite();
      
      const restaurants = await userFavoriteModel.getPopularFavoritedRestaurants(limit, days);

      return ResponseHelper.success(res, { restaurants }, 'Popular favorited restaurants retrieved successfully');
    } catch (error) {
      console.error('Get popular favorited restaurants error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve popular favorited restaurants', 500);
    }
  }

  // Get popular favorited menu items (public endpoint)
  static async getPopularFavoritedMenuItems(req, res) {
    try {
      const limit = Math.min(parseInt(req.query.limit) || 10, 50);
      const days = parseInt(req.query.days) || 30;
      const userFavoriteModel = new UserFavorite();
      
      const menuItems = await userFavoriteModel.getPopularFavoritedMenuItems(limit, days);

      return ResponseHelper.success(res, { menuItems }, 'Popular favorited menu items retrieved successfully');
    } catch (error) {
      console.error('Get popular favorited menu items error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve popular favorited menu items', 500);
    }
  }
}

module.exports = UserFavoriteController;
