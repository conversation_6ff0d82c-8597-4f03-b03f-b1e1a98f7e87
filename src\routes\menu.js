// Menu Routes
const express = require('express');
const MenuController = require('../controllers/menuController');
const MenuCategoryController = require('../controllers/menuCategoryController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting to all routes
router.use(apiLimiter);

// Public routes (no authentication required)

// GET /api/v1/menu/items - Get menu items with filtering
router.get('/items',
  ValidationMiddleware.validateMenuItemsQuery(),
  MenuController.getMenuItems
);

// GET /api/v1/menu/items/popular - Get popular menu items
router.get('/items/popular',
  MenuController.getPopularItems
);

// GET /api/v1/menu/items/:id - Get specific menu item details with customizations
router.get('/items/:id',
  ValidationMiddleware.validateUUID('id'),
  MenuController.getMenuItemById
);

// GET /api/v1/menu/items/:id/stats - Get menu item statistics
router.get('/items/:id/stats',
  ValidationMiddleware.validateUUID('id'),
  MenuController.getItemStats
);

// GET /api/v1/menu/search - Search menu items
router.get('/search',
  ValidationMiddleware.validateMenuSearch(),
  MenuController.searchMenuItems
);

// GET /api/v1/menu/categories/:categoryId/items - Get menu items by category
router.get('/categories/:categoryId/items',
  ValidationMiddleware.validateUUID('categoryId'),
  MenuController.getItemsByCategory
);

// Menu Category Routes

// GET /api/v1/menu/restaurants/:restaurantId/categories - Get categories by restaurant
router.get('/restaurants/:restaurantId/categories',
  ValidationMiddleware.validateUUID('restaurantId'),
  MenuCategoryController.getCategoriesByRestaurant
);

// GET /api/v1/menu/restaurants/:restaurantId/categories/popular - Get popular categories
router.get('/restaurants/:restaurantId/categories/popular',
  ValidationMiddleware.validateUUID('restaurantId'),
  MenuCategoryController.getPopularCategories
);

// GET /api/v1/menu/categories/:id - Get category with menu items
router.get('/categories/:id',
  ValidationMiddleware.validateUUID('id'),
  MenuCategoryController.getCategoryWithItems
);

// GET /api/v1/menu/categories/:id/stats - Get category statistics
router.get('/categories/:id/stats',
  ValidationMiddleware.validateUUID('id'),
  MenuCategoryController.getCategoryStats
);

// Protected routes (authentication required)

// PUT /api/v1/menu/items/:id/availability - Update menu item availability (admin only)
router.put('/items/:id/availability',
  AuthMiddleware.authenticate,
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateItemAvailabilityUpdate(),
  MenuController.updateItemAvailability
);

// PUT /api/v1/menu/items/:id/price - Update menu item price (admin only)
router.put('/items/:id/price',
  AuthMiddleware.authenticate,
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateItemPriceUpdate(),
  MenuController.updateItemPrice
);

// POST /api/v1/menu/categories - Create menu category (admin only)
router.post('/categories',
  AuthMiddleware.authenticate,
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateCreateCategory(),
  MenuCategoryController.createCategory
);

// PUT /api/v1/menu/categories/:id - Update menu category (admin only)
router.put('/categories/:id',
  AuthMiddleware.authenticate,
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateUpdateCategory(),
  MenuCategoryController.updateCategory
);

// DELETE /api/v1/menu/categories/:id - Delete menu category (admin only)
router.delete('/categories/:id',
  AuthMiddleware.authenticate,
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateDeleteCategory(),
  MenuCategoryController.deleteCategory
);

// PUT /api/v1/menu/categories/:id/toggle - Toggle category active status (admin only)
router.put('/categories/:id/toggle',
  AuthMiddleware.authenticate,
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  MenuCategoryController.toggleCategoryActive
);

// PUT /api/v1/menu/restaurants/:restaurantId/categories/reorder - Reorder categories (admin only)
router.put('/restaurants/:restaurantId/categories/reorder',
  AuthMiddleware.authenticate,
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('restaurantId'),
  ValidationMiddleware.validateReorderCategories(),
  MenuCategoryController.reorderCategories
);

module.exports = router;
