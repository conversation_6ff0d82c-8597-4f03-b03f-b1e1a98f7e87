// Health Check Routes
const express = require('express');
const HealthController = require('../controllers/healthController');

const router = express.Router();

// Root endpoint
router.get('/', HealthController.getServerInfo);

// Health check endpoint (used by Railway)
router.get('/health', HealthController.getHealthStatus);

// Database test endpoint
router.get('/test-db', HealthController.testDatabase);

// Redis test endpoint
router.get('/test-redis', HealthController.testRedis);

// Performance metrics endpoint
router.get('/performance', HealthController.getPerformanceMetrics);

module.exports = router;
