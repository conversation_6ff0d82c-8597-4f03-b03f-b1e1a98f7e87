// Authentication Routes
const express = require('express');
const AuthController = require('../controllers/authController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { authLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Public routes (no authentication required)

// POST /api/auth/register - Register new user
router.post('/register',
  authLimiter,
  ValidationMiddleware.validateRegistration(),
  AuthController.register
);

// POST /api/auth/login - Login user
router.post('/login',
  authLimiter,
  ValidationMiddleware.validateLogin(),
  AuthController.login
);

// POST /api/auth/refresh - Refresh access token
router.post('/refresh',
  authLimiter,
  ValidationMiddleware.validateRefreshToken(),
  AuthController.refreshToken
);

// POST /api/auth/forgot-password - Request password reset
router.post('/forgot-password',
  authLimiter,
  ValidationMiddleware.validateForgotPassword(),
  AuthController.forgotPassword
);

// POST /api/auth/reset-password - Reset password with token
router.post('/reset-password',
  authLimiter,
  ValidationMiddleware.validateResetPassword(),
  AuthController.resetPassword
);

// POST /api/auth/verify-email - Verify email with token
router.post('/verify-email',
  ValidationMiddleware.validateEmailVerification(),
  AuthController.verifyEmail
);

// POST /api/auth/verify-otp - Verify OTP
router.post('/verify-otp',
  authLimiter,
  ValidationMiddleware.validateOTPVerification(),
  AuthController.verifyOTP
);

// Protected routes (authentication required)

// POST /api/auth/logout - Logout user
router.post('/logout',
  AuthMiddleware.authenticate,
  AuthController.logout
);

// GET /api/v1/auth/me - Get current user profile
router.get('/me',
  AuthMiddleware.authenticate,
  AuthController.getProfile
);

// POST /api/auth/resend-verification - Resend email verification
router.post('/resend-verification',
  AuthMiddleware.authenticate,
  AuthController.resendVerificationEmail
);

module.exports = router;
