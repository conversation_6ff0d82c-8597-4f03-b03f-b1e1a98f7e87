// UserFavorite Model
const BaseModel = require('./BaseModel');

class UserFavorite extends BaseModel {
  constructor() {
    super('user_favorites');
  }

  // Get user's favorite restaurants
  async getFavoriteRestaurants(userId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      const query = `
        SELECT 
          uf.id as favorite_id,
          uf.created_at as favorited_at,
          r.id, r.name, r.description, r.image, r.logo, r.slug,
          r.rating, r.review_count, r.cuisine_types, r.delivery_fee,
          r.minimum_order, r.estimated_delivery_time, r.accepting_orders
        FROM user_favorites uf
        JOIN restaurants r ON uf.restaurant_id = r.id
        WHERE uf.user_id = $1 AND uf.restaurant_id IS NOT NULL
        AND r.is_active = true
        ORDER BY uf.created_at DESC
        LIMIT $2 OFFSET $3
      `;

      const countQuery = `
        SELECT COUNT(*) as count
        FROM user_favorites uf
        JOIN restaurants r ON uf.restaurant_id = r.id
        WHERE uf.user_id = $1 AND uf.restaurant_id IS NOT NULL
        AND r.is_active = true
      `;

      const [results, countResult] = await Promise.all([
        this.query(query, [userId, limit, offset]),
        this.query(countQuery, [userId])
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        restaurants: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get user's favorite menu items
  async getFavoriteMenuItems(userId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      const query = `
        SELECT 
          uf.id as favorite_id,
          uf.created_at as favorited_at,
          mi.id, mi.name, mi.description, mi.price, mi.image,
          mi.is_available, mi.is_popular, mi.is_vegetarian, mi.is_vegan, mi.is_gluten_free,
          mi.preparation_time, mi.ingredients, mi.allergens,
          r.id as restaurant_id, r.name as restaurant_name, r.logo as restaurant_logo,
          mc.name as category_name
        FROM user_favorites uf
        JOIN menu_items mi ON uf.menu_item_id = mi.id
        JOIN restaurants r ON mi.restaurant_id = r.id
        JOIN menu_categories mc ON mi.category_id = mc.id
        WHERE uf.user_id = $1 AND uf.menu_item_id IS NOT NULL
        AND mi.is_available = true AND r.is_active = true
        ORDER BY uf.created_at DESC
        LIMIT $2 OFFSET $3
      `;

      const countQuery = `
        SELECT COUNT(*) as count
        FROM user_favorites uf
        JOIN menu_items mi ON uf.menu_item_id = mi.id
        JOIN restaurants r ON mi.restaurant_id = r.id
        WHERE uf.user_id = $1 AND uf.menu_item_id IS NOT NULL
        AND mi.is_available = true AND r.is_active = true
      `;

      const [results, countResult] = await Promise.all([
        this.query(query, [userId, limit, offset]),
        this.query(countQuery, [userId])
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        menuItems: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Add restaurant to favorites
  async addRestaurantToFavorites(userId, restaurantId) {
    try {
      // Check if already favorited
      const existing = await this.query(
        'SELECT id FROM user_favorites WHERE user_id = $1 AND restaurant_id = $2',
        [userId, restaurantId]
      );

      if (existing.rows.length > 0) {
        throw new Error('Restaurant is already in favorites');
      }

      // Verify restaurant exists and is active
      const restaurant = await this.query(
        'SELECT id FROM restaurants WHERE id = $1 AND is_active = true',
        [restaurantId]
      );

      if (restaurant.rows.length === 0) {
        throw new Error('Restaurant not found or inactive');
      }

      // Add to favorites
      const result = await this.query(
        'INSERT INTO user_favorites (user_id, restaurant_id) VALUES ($1, $2) RETURNING *',
        [userId, restaurantId]
      );

      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Add menu item to favorites
  async addMenuItemToFavorites(userId, menuItemId) {
    try {
      // Check if already favorited
      const existing = await this.query(
        'SELECT id FROM user_favorites WHERE user_id = $1 AND menu_item_id = $2',
        [userId, menuItemId]
      );

      if (existing.rows.length > 0) {
        throw new Error('Menu item is already in favorites');
      }

      // Verify menu item exists and is available
      const menuItem = await this.query(
        'SELECT id FROM menu_items WHERE id = $1 AND is_available = true',
        [menuItemId]
      );

      if (menuItem.rows.length === 0) {
        throw new Error('Menu item not found or unavailable');
      }

      // Add to favorites
      const result = await this.query(
        'INSERT INTO user_favorites (user_id, menu_item_id) VALUES ($1, $2) RETURNING *',
        [userId, menuItemId]
      );

      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Remove restaurant from favorites
  async removeRestaurantFromFavorites(userId, restaurantId) {
    try {
      const result = await this.query(
        'DELETE FROM user_favorites WHERE user_id = $1 AND restaurant_id = $2 RETURNING *',
        [userId, restaurantId]
      );

      if (result.rows.length === 0) {
        throw new Error('Restaurant not found in favorites');
      }

      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Remove menu item from favorites
  async removeMenuItemFromFavorites(userId, menuItemId) {
    try {
      const result = await this.query(
        'DELETE FROM user_favorites WHERE user_id = $1 AND menu_item_id = $2 RETURNING *',
        [userId, menuItemId]
      );

      if (result.rows.length === 0) {
        throw new Error('Menu item not found in favorites');
      }

      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Check if restaurant is favorited by user
  async isRestaurantFavorited(userId, restaurantId) {
    try {
      const result = await this.query(
        'SELECT id FROM user_favorites WHERE user_id = $1 AND restaurant_id = $2',
        [userId, restaurantId]
      );

      return result.rows.length > 0;
    } catch (error) {
      throw error;
    }
  }

  // Check if menu item is favorited by user
  async isMenuItemFavorited(userId, menuItemId) {
    try {
      const result = await this.query(
        'SELECT id FROM user_favorites WHERE user_id = $1 AND menu_item_id = $2',
        [userId, menuItemId]
      );

      return result.rows.length > 0;
    } catch (error) {
      throw error;
    }
  }

  // Get user's favorite statistics
  async getUserFavoriteStats(userId) {
    try {
      const query = `
        SELECT 
          COUNT(*) FILTER (WHERE restaurant_id IS NOT NULL) as favorite_restaurants,
          COUNT(*) FILTER (WHERE menu_item_id IS NOT NULL) as favorite_menu_items,
          COUNT(*) as total_favorites
        FROM user_favorites
        WHERE user_id = $1
      `;

      const result = await this.query(query, [userId]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Get popular favorited restaurants
  async getPopularFavoritedRestaurants(limit = 10, days = 30) {
    try {
      const query = `
        SELECT 
          r.id, r.name, r.description, r.image, r.logo, r.slug,
          r.rating, r.review_count, r.cuisine_types,
          COUNT(uf.id) as favorite_count
        FROM restaurants r
        JOIN user_favorites uf ON r.id = uf.restaurant_id
        WHERE uf.created_at >= NOW() - INTERVAL '${days} days'
        AND r.is_active = true
        GROUP BY r.id, r.name, r.description, r.image, r.logo, r.slug,
                 r.rating, r.review_count, r.cuisine_types
        ORDER BY favorite_count DESC, r.rating DESC
        LIMIT $1
      `;

      const result = await this.query(query, [limit]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Get popular favorited menu items
  async getPopularFavoritedMenuItems(limit = 10, days = 30) {
    try {
      const query = `
        SELECT 
          mi.id, mi.name, mi.description, mi.price, mi.image,
          mi.is_vegetarian, mi.is_vegan, mi.is_gluten_free,
          r.id as restaurant_id, r.name as restaurant_name, r.logo as restaurant_logo,
          mc.name as category_name,
          COUNT(uf.id) as favorite_count
        FROM menu_items mi
        JOIN user_favorites uf ON mi.id = uf.menu_item_id
        JOIN restaurants r ON mi.restaurant_id = r.id
        JOIN menu_categories mc ON mi.category_id = mc.id
        WHERE uf.created_at >= NOW() - INTERVAL '${days} days'
        AND mi.is_available = true AND r.is_active = true
        GROUP BY mi.id, mi.name, mi.description, mi.price, mi.image,
                 mi.is_vegetarian, mi.is_vegan, mi.is_gluten_free,
                 r.id, r.name, r.logo, mc.name
        ORDER BY favorite_count DESC, mi.name ASC
        LIMIT $1
      `;

      const result = await this.query(query, [limit]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Get recommendations based on user's favorites
  async getRecommendationsBasedOnFavorites(userId, limit = 10) {
    try {
      const query = `
        WITH user_favorite_cuisines AS (
          SELECT UNNEST(r.cuisine_types) as cuisine_type
          FROM user_favorites uf
          JOIN restaurants r ON uf.restaurant_id = r.id
          WHERE uf.user_id = $1
          GROUP BY cuisine_type
          ORDER BY COUNT(*) DESC
          LIMIT 3
        ),
        user_favorite_restaurants AS (
          SELECT restaurant_id
          FROM user_favorites
          WHERE user_id = $1 AND restaurant_id IS NOT NULL
        )
        SELECT DISTINCT
          r.id, r.name, r.description, r.image, r.logo, r.slug,
          r.rating, r.review_count, r.cuisine_types, r.delivery_fee,
          r.minimum_order, r.estimated_delivery_time
        FROM restaurants r
        JOIN user_favorite_cuisines ufc ON ufc.cuisine_type = ANY(r.cuisine_types)
        WHERE r.is_active = true 
        AND r.accepting_orders = true
        AND r.id NOT IN (SELECT restaurant_id FROM user_favorite_restaurants)
        ORDER BY r.rating DESC, r.review_count DESC
        LIMIT $2
      `;

      const result = await this.query(query, [userId, limit]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Clear all favorites for user
  async clearAllFavorites(userId) {
    try {
      const result = await this.query(
        'DELETE FROM user_favorites WHERE user_id = $1 RETURNING COUNT(*)',
        [userId]
      );

      return { deletedCount: result.rowCount };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = UserFavorite;
