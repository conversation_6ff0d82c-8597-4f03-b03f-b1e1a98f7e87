// Email Service
const nodemailer = require('nodemailer');
const config = require('../config/app');

class EmailService {
  constructor() {
    this.transporter = null;
    this.initialize();
  }

  initialize() {
    // For development, we'll use a test account or console logging
    // In production, you would configure with your email provider (SendGrid, AWS SES, etc.)
    
    if (config.isProduction) {
      // Production email configuration
      this.transporter = nodemailer.createTransporter({
        // Configure with your email service provider
        // Example for SendGrid:
        // service: 'SendGrid',
        // auth: {
        //   user: 'apikey',
        //   pass: process.env.SENDGRID_API_KEY
        // }
      });
    } else {
      // Development - log emails to console
      this.transporter = {
        sendMail: async (mailOptions) => {
          console.log('\n📧 Email would be sent:');
          console.log('To:', mailOptions.to);
          console.log('Subject:', mailOptions.subject);
          console.log('Content:', mailOptions.html || mailOptions.text);
          console.log('---\n');
          return { messageId: 'dev-' + Date.now() };
        }
      };
    }
  }

  // Send email verification
  async sendVerificationEmail(email, firstName, token) {
    const verificationUrl = `${config.urls.frontend}/verify-email?token=${token}`;
    
    const mailOptions = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'Verify Your FoodWay Account',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #ff6b35;">Welcome to FoodWay, ${firstName}!</h2>
          
          <p>Thank you for signing up for FoodWay. To complete your registration, please verify your email address by clicking the button below:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" 
               style="background-color: #ff6b35; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Verify Email Address
            </a>
          </div>
          
          <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
          
          <p>This verification link will expire in 24 hours.</p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          
          <p style="color: #666; font-size: 12px;">
            If you didn't create a FoodWay account, you can safely ignore this email.
          </p>
          
          <p style="color: #666; font-size: 12px;">
            Best regards,<br>
            The FoodWay Team
          </p>
        </div>
      `
    };

    try {
      const result = await this.transporter.sendMail(mailOptions);
      console.log('Verification email sent:', result.messageId);
      return result;
    } catch (error) {
      console.error('Failed to send verification email:', error);
      throw error;
    }
  }

  // Send password reset email
  async sendPasswordResetEmail(email, firstName, token) {
    const resetUrl = `${config.urls.frontend}/reset-password?token=${token}`;
    
    const mailOptions = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'Reset Your FoodWay Password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #ff6b35;">Password Reset Request</h2>
          
          <p>Hi ${firstName},</p>
          
          <p>We received a request to reset your FoodWay account password. Click the button below to reset your password:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background-color: #ff6b35; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Reset Password
            </a>
          </div>
          
          <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${resetUrl}</p>
          
          <p>This password reset link will expire in 1 hour.</p>
          
          <p><strong>If you didn't request a password reset, please ignore this email.</strong> Your password will remain unchanged.</p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          
          <p style="color: #666; font-size: 12px;">
            For security reasons, this link will only work once and will expire in 1 hour.
          </p>
          
          <p style="color: #666; font-size: 12px;">
            Best regards,<br>
            The FoodWay Team
          </p>
        </div>
      `
    };

    try {
      const result = await this.transporter.sendMail(mailOptions);
      console.log('Password reset email sent:', result.messageId);
      return result;
    } catch (error) {
      console.error('Failed to send password reset email:', error);
      throw error;
    }
  }

  // Send order confirmation email
  async sendOrderConfirmationEmail(email, firstName, orderDetails) {
    const mailOptions = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: `Order Confirmation - ${orderDetails.orderNumber}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #ff6b35;">Order Confirmed!</h2>
          
          <p>Hi ${firstName},</p>
          
          <p>Thank you for your order! We've received your order and it's being prepared.</p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Order Details</h3>
            <p><strong>Order Number:</strong> ${orderDetails.orderNumber}</p>
            <p><strong>Restaurant:</strong> ${orderDetails.restaurantName}</p>
            <p><strong>Total:</strong> $${orderDetails.totalAmount}</p>
            <p><strong>Estimated Delivery:</strong> ${orderDetails.estimatedDeliveryTime}</p>
          </div>
          
          <p>You can track your order status in the FoodWay app.</p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          
          <p style="color: #666; font-size: 12px;">
            Best regards,<br>
            The FoodWay Team
          </p>
        </div>
      `
    };

    try {
      const result = await this.transporter.sendMail(mailOptions);
      console.log('Order confirmation email sent:', result.messageId);
      return result;
    } catch (error) {
      console.error('Failed to send order confirmation email:', error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new EmailService();
