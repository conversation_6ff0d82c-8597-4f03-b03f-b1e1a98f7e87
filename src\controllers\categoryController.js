// Category Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const MenuCategory = require('../models/MenuCategory');

class CategoryController {
  // Get all categories (for customer app)
  static async getCategories(req, res) {
    try {
      const menuCategoryModel = new MenuCategory();
      
      // Get all active categories across all restaurants
      const query = `
        SELECT DISTINCT
          mc.id,
          mc.name,
          mc.description,
          mc.image,
          COUNT(mi.id) as item_count
        FROM menu_categories mc
        LEFT JOIN menu_items mi ON mc.id = mi.category_id AND mi.is_available = true
        WHERE mc.is_active = true
        GROUP BY mc.id, mc.name, mc.description, mc.image
        HAVING COUNT(mi.id) > 0
        ORDER BY mc.name ASC
      `;

      const result = await menuCategoryModel.query(query);
      const categories = result.rows;

      return ResponseHelper.success(res, { categories }, 'Categories retrieved successfully');
    } catch (error) {
      console.error('Get categories error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve categories', 500);
    }
  }

  // Get category by ID with menu items
  static async getCategoryById(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const categoryId = req.params.id;
      const menuCategoryModel = new MenuCategory();
      
      const category = await menuCategoryModel.getCategoryWithItems(categoryId);

      if (!category) {
        return ResponseHelper.error(res, 'Category not found', 404, {
          code: 'NOT_FOUND',
          message: 'Category not found or is not active'
        });
      }

      return ResponseHelper.success(res, category, 'Category details retrieved successfully');
    } catch (error) {
      console.error('Get category by ID error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve category details', 500);
    }
  }

  // Get categories by restaurant
  static async getCategoriesByRestaurant(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.restaurantId;
      const activeOnly = req.query.activeOnly !== 'false';
      const menuCategoryModel = new MenuCategory();
      
      const categories = await menuCategoryModel.getCategoriesByRestaurant(restaurantId, activeOnly);

      return ResponseHelper.success(res, { categories }, 'Restaurant categories retrieved successfully');
    } catch (error) {
      console.error('Get categories by restaurant error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve restaurant categories', 500);
    }
  }

  // Get popular categories
  static async getPopularCategories(req, res) {
    try {
      const limit = Math.min(parseInt(req.query.limit) || 10, 50);
      const days = parseInt(req.query.days) || 30;
      const menuCategoryModel = new MenuCategory();

      // Get popular categories across all restaurants
      const query = `
        SELECT 
          mc.id,
          mc.name,
          mc.description,
          mc.image,
          COUNT(oi.id) as order_count,
          COUNT(DISTINCT mi.id) as item_count
        FROM menu_categories mc
        JOIN menu_items mi ON mc.id = mi.category_id
        JOIN order_items oi ON mi.id = oi.menu_item_id
        JOIN orders o ON oi.order_id = o.id
        WHERE mc.is_active = true
        AND mi.is_available = true
        AND o.created_at >= NOW() - INTERVAL '${days} days'
        AND o.status = 'delivered'
        GROUP BY mc.id, mc.name, mc.description, mc.image
        ORDER BY order_count DESC
        LIMIT $1
      `;

      const result = await menuCategoryModel.query(query, [limit]);
      const categories = result.rows;

      return ResponseHelper.success(res, { categories }, 'Popular categories retrieved successfully');
    } catch (error) {
      console.error('Get popular categories error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve popular categories', 500);
    }
  }
}

module.exports = CategoryController;
