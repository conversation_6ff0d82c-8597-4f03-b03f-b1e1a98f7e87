# FoodWay Backend - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# ================================
# SERVER CONFIGURATION
# ================================
NODE_ENV=development
PORT=5000

# ================================
# DATABASE CONFIGURATION
# ================================
# PostgreSQL connection string
# For Railway: Get this from your PostgreSQL service in Railway dashboard
DATABASE_URL=postgresql://username:password@host:port/database

# ================================
# REDIS CONFIGURATION
# ================================
# Redis connection string
# For Railway: Get this from your Redis service in Railway dashboard
REDIS_URL=redis://default:password@host:port

# ================================
# AUTHENTICATION & SECURITY
# ================================
# JWT secret key - Generate a strong random string
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=7d
BCRYPT_SALT_ROUNDS=12

# ================================
# APPLICATION URLS
# ================================
# Frontend application URL
FRONTEND_URL=http://localhost:3000

# Backend application URL (for Railway, use ${{RAILWAY_PUBLIC_DOMAIN}})
BACKEND_URL=http://localhost:5000

# ================================
# DEVELOPMENT/DEBUG
# ================================
DEBUG=false
LOG_LEVEL=info

# ================================
# OPTIONAL CONFIGURATIONS
# ================================
# Email service configuration (if needed)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# File upload configuration (if needed)
# MAX_FILE_SIZE=5242880
# UPLOAD_PATH=uploads/

# Rate limiting (if needed)
# RATE_LIMIT_WINDOW_MS=900000
# RATE_LIMIT_MAX_REQUESTS=100
