// Authentication Controller
const { validationResult } = require('express-validator');
const AuthService = require('../services/authService');
const ResponseHelper = require('../utils/response');

class AuthController {
  // Register new user
  static async register(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { email, password, firstName, lastName, phone } = req.body;

      // Register user
      const result = await AuthService.register({
        email,
        password,
        firstName,
        lastName,
        phone
      });

      return ResponseHelper.success(res, result, 'User registered successfully', 201);

    } catch (error) {
      console.error('Registration error:', error);

      if (error.message.includes('already exists')) {
        return ResponseHelper.error(res, error.message, 409, {
          code: 'CONFLICT',
          message: error.message
        });
      }

      return ResponseHelper.error(res, 'Registration failed', 500);
    }
  }

  // Login user
  static async login(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { email, password } = req.body;

      // Login user
      const result = await AuthService.login(email, password);

      return ResponseHelper.success(res, result, 'Login successful');

    } catch (error) {
      console.error('Login error:', error);

      if (error.message.includes('Invalid email or password') ||
        error.message.includes('deactivated')) {
        return ResponseHelper.error(res, error.message, 401, {
          code: 'UNAUTHORIZED',
          message: error.message
        });
      }

      return ResponseHelper.error(res, 'Login failed', 500);
    }
  }

  // Refresh access token
  static async refreshToken(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { refreshToken } = req.body;

      // Refresh token
      const result = await AuthService.refreshToken(refreshToken);

      return ResponseHelper.success(res, result, 'Token refreshed successfully');

    } catch (error) {
      console.error('Token refresh error:', error);

      return ResponseHelper.error(res, 'Invalid refresh token', 401, {
        code: 'UNAUTHORIZED',
        message: 'Invalid or expired refresh token'
      });
    }
  }

  // Logout user
  static async logout(req, res) {
    try {
      const userId = req.user.id;

      // Logout user
      await AuthService.logout(userId);

      return ResponseHelper.success(res, null, 'Logout successful');

    } catch (error) {
      console.error('Logout error:', error);
      return ResponseHelper.error(res, 'Logout failed', 500);
    }
  }

  // Request password reset
  static async forgotPassword(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { email } = req.body;

      // Request password reset
      await AuthService.requestPasswordReset(email);

      return ResponseHelper.success(res, null, 'Password reset email sent if account exists');

    } catch (error) {
      console.error('Forgot password error:', error);
      return ResponseHelper.error(res, 'Failed to process password reset request', 500);
    }
  }

  // Reset password
  static async resetPassword(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { token, newPassword } = req.body;

      // Reset password
      await AuthService.resetPassword(token, newPassword);

      return ResponseHelper.success(res, null, 'Password reset successful');

    } catch (error) {
      console.error('Reset password error:', error);

      if (error.message.includes('Invalid or expired')) {
        return ResponseHelper.error(res, error.message, 400, {
          code: 'INVALID_TOKEN',
          message: error.message
        });
      }

      return ResponseHelper.error(res, 'Password reset failed', 500);
    }
  }

  // Verify email
  static async verifyEmail(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { token } = req.body;

      // Verify email
      await AuthService.verifyEmail(token);

      return ResponseHelper.success(res, null, 'Email verified successfully');

    } catch (error) {
      console.error('Email verification error:', error);

      if (error.message.includes('Invalid or expired')) {
        return ResponseHelper.error(res, error.message, 400, {
          code: 'INVALID_TOKEN',
          message: error.message
        });
      }

      return ResponseHelper.error(res, 'Email verification failed', 500);
    }
  }

  // Get current user profile
  static async getProfile(req, res) {
    try {
      const user = req.user;

      return ResponseHelper.success(res, { user }, 'Profile retrieved successfully');

    } catch (error) {
      console.error('Get profile error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve profile', 500);
    }
  }

  // Resend verification email
  static async resendVerificationEmail(req, res) {
    try {
      const userId = req.user.id;
      const pool = require('../config/database').getPool();

      // Check if email is already verified
      const userResult = await pool.query(
        'SELECT email, first_name, email_verified FROM users WHERE id = $1',
        [userId]
      );

      if (userResult.rows.length === 0) {
        return ResponseHelper.error(res, 'User not found', 404);
      }

      const user = userResult.rows[0];

      if (user.email_verified) {
        return ResponseHelper.error(res, 'Email is already verified', 400, {
          code: 'ALREADY_VERIFIED',
          message: 'Your email address is already verified'
        });
      }

      // Generate new verification token
      const crypto = require('crypto');
      const emailVerificationToken = crypto.randomBytes(32).toString('hex');
      const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Update verification token
      await pool.query(
        'UPDATE users SET email_verification_token = $1, email_verification_expires = $2 WHERE id = $3',
        [emailVerificationToken, emailVerificationExpires, userId]
      );

      // Send verification email
      const EmailService = require('../services/emailService');
      await EmailService.sendVerificationEmail(user.email, user.first_name, emailVerificationToken);

      return ResponseHelper.success(res, null, 'Verification email sent successfully');

    } catch (error) {
      console.error('Resend verification email error:', error);
      return ResponseHelper.error(res, 'Failed to send verification email', 500);
    }
  }

  // Verify OTP (mock implementation)
  static async verifyOTP(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { phone, otp } = req.body;

      // Mock OTP verification - in a real implementation, you would:
      // 1. Check if the OTP exists in the database
      // 2. Verify it hasn't expired
      // 3. Check if it matches the phone number

      // For demo purposes, accept any 6-digit OTP
      if (!/^\d{6}$/.test(otp)) {
        return ResponseHelper.error(res, 'Invalid OTP format', 400, {
          code: 'INVALID_OTP',
          message: 'OTP must be 6 digits'
        });
      }

      // Mock verification success
      const verificationToken = require('crypto').randomBytes(32).toString('hex');

      return ResponseHelper.success(res, {
        verified: true,
        verificationToken,
        message: 'OTP verified successfully'
      }, 'OTP verification successful');

    } catch (error) {
      console.error('Verify OTP error:', error);
      return ResponseHelper.error(res, 'Failed to verify OTP', 500);
    }
  }
}

module.exports = AuthController;
