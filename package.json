{"name": "foodway-backend", "version": "1.0.0", "description": "FoodWay - Modern Food Delivery Backend API for Railway deployment", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:unit": "jest __tests__/unit", "test:integration": "jest __tests__/integration", "test:coverage": "jest --coverage", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js", "db:reset": "node scripts/reset.js", "db:optimize": "node scripts/optimize-database.js", "test:query-optimization": "node scripts/test-query-optimization.js", "lint": "echo '<PERSON><PERSON> not configured yet'", "format": "echo 'Formatting not configured yet'"}, "keywords": ["foodway", "food-delivery", "api", "backend", "railway", "postgresql", "redis", "express", "nodejs"], "author": "FoodWay Development Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"axios": "^1.10.0", "bcrypt": "^6.0.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemailer": "^7.0.4", "pg": "^8.16.0", "redis": "^4.6.12", "socket.io": "^4.8.1", "stripe": "^18.2.1", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.4"}}