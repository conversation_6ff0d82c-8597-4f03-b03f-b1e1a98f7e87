// Menu Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const MenuItem = require('../models/MenuItem');
const MenuCategory = require('../models/MenuCategory');

class MenuController {
  // Get menu items with filtering and pagination
  static async getMenuItems(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const filters = {
        categoryId: req.query.categoryId,
        available: req.query.available !== 'false',
        page: parseInt(req.query.page) || 1,
        limit: Math.min(parseInt(req.query.limit) || 20, 50)
      };

      const menuItemModel = new MenuItem();
      const result = await menuItemModel.getMenuItemsWithCategory(filters);

      return ResponseHelper.success(res, result, 'Menu items retrieved successfully');
    } catch (error) {
      console.error('Get menu items error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve menu items', 500);
    }
  }

  // Get specific menu item details with customizations
  static async getMenuItemById(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const menuItemId = req.params.id;
      const menuItemModel = new MenuItem();
      const menuItem = await menuItemModel.getMenuItemWithCustomizations(menuItemId);

      if (!menuItem) {
        return ResponseHelper.error(res, 'Menu item not found', 404, {
          code: 'NOT_FOUND',
          message: 'Menu item not found or is not available'
        });
      }

      return ResponseHelper.success(res, menuItem, 'Menu item details retrieved successfully');
    } catch (error) {
      console.error('Get menu item by ID error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve menu item details', 500);
    }
  }

  // Search menu items
  static async searchMenuItems(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const searchParams = {
        q: req.query.q,
        categoryId: req.query.categoryId,
        minPrice: req.query.minPrice ? parseFloat(req.query.minPrice) : null,
        maxPrice: req.query.maxPrice ? parseFloat(req.query.maxPrice) : null,
        dietary: req.query.dietary ? (Array.isArray(req.query.dietary) ? req.query.dietary : [req.query.dietary]) : []
      };

      const menuItemModel = new MenuItem();
      const results = await menuItemModel.searchMenuItems(searchParams);

      return ResponseHelper.success(res, { items: results }, 'Menu search completed successfully');
    } catch (error) {
      console.error('Search menu items error:', error);
      return ResponseHelper.error(res, 'Failed to search menu items', 500);
    }
  }

  // Get popular menu items
  static async getPopularItems(req, res) {
    try {
      const limit = Math.min(parseInt(req.query.limit) || 10, 50);
      const menuItemModel = new MenuItem();

      const items = await menuItemModel.getPopularItems(limit);

      return ResponseHelper.success(res, { items }, 'Popular menu items retrieved successfully');
    } catch (error) {
      console.error('Get popular items error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve popular menu items', 500);
    }
  }

  // Get menu items by category
  static async getItemsByCategory(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const categoryId = req.params.categoryId;
      const availableOnly = req.query.availableOnly !== 'false';
      const menuItemModel = new MenuItem();

      const items = await menuItemModel.getItemsByCategory(categoryId, availableOnly);

      return ResponseHelper.success(res, { items }, 'Menu items by category retrieved successfully');
    } catch (error) {
      console.error('Get items by category error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve menu items by category', 500);
    }
  }

  // Update menu item availability (admin only)
  static async updateItemAvailability(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const itemId = req.params.id;
      const { isAvailable } = req.body;
      const menuItemModel = new MenuItem();

      const item = await menuItemModel.updateAvailability(itemId, isAvailable);

      if (!item) {
        return ResponseHelper.error(res, 'Menu item not found', 404, {
          code: 'NOT_FOUND',
          message: 'Menu item not found'
        });
      }

      return ResponseHelper.success(res, item, 'Menu item availability updated successfully');
    } catch (error) {
      console.error('Update item availability error:', error);
      return ResponseHelper.error(res, 'Failed to update menu item availability', 500);
    }
  }

  // Update menu item price (admin only)
  static async updateItemPrice(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const itemId = req.params.id;
      const { price } = req.body;
      const menuItemModel = new MenuItem();

      const item = await menuItemModel.updatePrice(itemId, price);

      if (!item) {
        return ResponseHelper.error(res, 'Menu item not found', 404, {
          code: 'NOT_FOUND',
          message: 'Menu item not found'
        });
      }

      return ResponseHelper.success(res, item, 'Menu item price updated successfully');
    } catch (error) {
      console.error('Update item price error:', error);
      return ResponseHelper.error(res, 'Failed to update menu item price', 500);
    }
  }

  // Get menu item statistics
  static async getItemStats(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const itemId = req.params.id;
      const days = parseInt(req.query.days) || 30;
      const menuItemModel = new MenuItem();

      const stats = await menuItemModel.getItemStats(itemId, days);

      if (!stats) {
        return ResponseHelper.error(res, 'Menu item not found', 404, {
          code: 'NOT_FOUND',
          message: 'Menu item not found'
        });
      }

      return ResponseHelper.success(res, stats, 'Menu item statistics retrieved successfully');
    } catch (error) {
      console.error('Get item stats error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve menu item statistics', 500);
    }
  }
}

module.exports = MenuController;
