// Unit Tests for Utility Functions
const ResponseHelper = require('../../src/utils/response');
const Logger = require('../../src/utils/logger');

describe('ResponseHelper', () => {
  let mockRes;

  beforeEach(() => {
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };
  });

  describe('success', () => {
    it('should return success response with default values', () => {
      const data = { id: 1, name: 'Test' };
      ResponseHelper.success(mockRes, data);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: 'Success',
        data,
        timestamp: expect.any(String)
      });
    });

    it('should return success response with custom values', () => {
      const data = { id: 1 };
      const message = 'Created successfully';
      const statusCode = 201;

      ResponseHelper.success(mockRes, data, message, statusCode);

      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message,
        data,
        timestamp: expect.any(String)
      });
    });
  });

  describe('error', () => {
    it('should return error response with default values', () => {
      ResponseHelper.error(mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error',
        timestamp: expect.any(String)
      });
    });

    it('should return error response with custom values', () => {
      const message = 'Custom error';
      const statusCode = 400;
      const details = { field: 'email', issue: 'invalid format' };

      ResponseHelper.error(mockRes, message, statusCode, details);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: message,
        details,
        timestamp: expect.any(String)
      });
    });
  });

  describe('notFound', () => {
    it('should return 404 response', () => {
      ResponseHelper.notFound(mockRes, 'User');

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: 'User not found',
        timestamp: expect.any(String)
      });
    });
  });
});

describe('Logger', () => {
  let consoleSpy;

  beforeEach(() => {
    consoleSpy = {
      log: jest.spyOn(console, 'log').mockImplementation(),
      warn: jest.spyOn(console, 'warn').mockImplementation(),
      error: jest.spyOn(console, 'error').mockImplementation()
    };
  });

  afterEach(() => {
    Object.values(consoleSpy).forEach(spy => spy.mockRestore());
  });

  it('should have info method that may or may not log based on config', () => {
    Logger.info('Test info message');
    // Info logging depends on configuration, so we just test the method exists
    expect(typeof Logger.info).toBe('function');
  });

  it('should log warning messages', () => {
    Logger.warn('Test warning message');
    expect(consoleSpy.warn).toHaveBeenCalledWith('⚠️  Test warning message');
  });

  it('should log error messages', () => {
    Logger.error('Test error message');
    expect(consoleSpy.error).toHaveBeenCalledWith('❌ Test error message');
  });

  it('should log success messages', () => {
    Logger.success('Test success message');
    expect(consoleSpy.log).toHaveBeenCalledWith('✅ Test success message');
  });
});
