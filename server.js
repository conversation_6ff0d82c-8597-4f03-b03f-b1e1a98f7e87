// FoodWay Backend - Server Entry Point
// Railway Ready with PostgreSQL and Redis
require('dotenv').config();

// Import application and configurations
const app = require('./src/app');
const config = require('./src/config/app');
const databaseManager = require('./src/config/database');
const redisManager = require('./src/config/redis');
const socketService = require('./src/services/socketService');

// ================================
// SERVER STARTUP
// ================================
async function startServer() {
  console.log('🚀 Starting FoodWay Backend...\n');

  // Initialize services
  const dbConnected = await databaseManager.initialize();
  const redisConnected = await redisManager.initialize();

  // Start the server
  const server = app.listen(config.port, () => {
    console.log('\n🎉 FoodWay Backend Started Successfully!');
    console.log('================================');
    console.log(`🌐 Server: http://localhost:${config.port}`);
    console.log(`📊 Health: http://localhost:${config.port}/health`);
    console.log(`🗄️ DB Test: http://localhost:${config.port}/test-db`);
    console.log(`🔴 Redis Test: http://localhost:${config.port}/test-redis`);
    console.log('================================');
    console.log(`📱 Environment: ${config.nodeEnv}`);
    console.log(`💾 PostgreSQL: ${dbConnected ? '✅ Connected' : '❌ Failed'}`);
    console.log(`🔴 Redis: ${redisConnected ? '✅ Connected' : '❌ Failed'}`);
    console.log(`🔌 Socket.IO: ✅ Initialized`);
    console.log(`🚀 Ready for Railway deployment!`);
    console.log('================================\n');
  });

  // Initialize Socket.IO
  socketService.initialize(server);

  return server;
}

// ================================
// GRACEFUL SHUTDOWN
// ================================
const gracefulShutdown = async (signal) => {
  console.log(`🔄 ${signal} received, shutting down gracefully...`);

  try {
    await databaseManager.close();
    await redisManager.close();
    console.log('✅ All connections closed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// ================================
// START SERVER
// ================================
if (require.main === module) {
  startServer().catch(error => {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  });
}

// Export for testing
module.exports = {
  app,
  startServer,
  databaseManager,
  redisManager
};
