// Database Performance Optimization Script
// Creates indexes and optimizes database for better performance

require('dotenv').config();
const databaseManager = require('../src/config/database');

async function optimizeDatabase() {
  console.log('🚀 Starting database performance optimization...\n');

  try {
    // Initialize database connection
    const connected = await databaseManager.initialize();
    if (!connected) {
      console.error('❌ Failed to connect to database');
      process.exit(1);
    }

    const pool = databaseManager.getPool();

    // Performance indexes for better query speed
    const indexes = [
      // Users table indexes
      {
        name: 'idx_users_email',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);',
        description: 'Email lookup for authentication'
      },
      {
        name: 'idx_users_phone',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_phone ON users(phone);',
        description: 'Phone number lookup'
      },
      {
        name: 'idx_users_active',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active ON users(is_active) WHERE is_active = true;',
        description: 'Active users filter'
      },

      // Restaurants table indexes
      {
        name: 'idx_restaurants_location',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_restaurants_location ON restaurants(latitude, longitude);',
        description: 'Location-based restaurant search'
      },
      {
        name: 'idx_restaurants_cuisine',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_restaurants_cuisine ON restaurants USING GIN(cuisine_types);',
        description: 'Cuisine type filtering (array search)'
      },
      {
        name: 'idx_restaurants_rating',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_restaurants_rating ON restaurants(rating DESC, review_count DESC);',
        description: 'Rating-based sorting'
      },
      {
        name: 'idx_restaurants_featured',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_restaurants_featured ON restaurants(is_featured) WHERE is_featured = true;',
        description: 'Featured restaurants'
      },
      {
        name: 'idx_restaurants_active',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_restaurants_active ON restaurants(is_active) WHERE is_active = true;',
        description: 'Active restaurants filter'
      },

      // Menu items table indexes
      {
        name: 'idx_menu_items_restaurant',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_menu_items_restaurant ON menu_items(restaurant_id, is_available);',
        description: 'Restaurant menu items lookup'
      },
      {
        name: 'idx_menu_items_category',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_menu_items_category ON menu_items(category_id, is_available);',
        description: 'Category-based menu filtering'
      },
      {
        name: 'idx_menu_items_popular',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_menu_items_popular ON menu_items(is_popular) WHERE is_popular = true;',
        description: 'Popular items filter'
      },

      // Orders table indexes
      {
        name: 'idx_orders_user',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_user ON orders(user_id, created_at DESC);',
        description: 'User order history'
      },
      {
        name: 'idx_orders_restaurant',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_restaurant ON orders(restaurant_id, created_at DESC);',
        description: 'Restaurant order management'
      },
      {
        name: 'idx_orders_status',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_status ON orders(status, created_at DESC);',
        description: 'Order status filtering'
      },
      {
        name: 'idx_orders_date_range',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_date_range ON orders(created_at DESC);',
        description: 'Date-based order queries'
      },

      // Order items table indexes
      {
        name: 'idx_order_items_order',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_items_order ON order_items(order_id);',
        description: 'Order details lookup'
      },
      {
        name: 'idx_order_items_menu',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_items_menu ON order_items(menu_item_id);',
        description: 'Menu item popularity tracking'
      },

      // User favorites table indexes
      {
        name: 'idx_user_favorites_user',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_favorites_user ON user_favorites(user_id, created_at DESC);',
        description: 'User favorites lookup'
      },
      {
        name: 'idx_user_favorites_restaurant',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_favorites_restaurant ON user_favorites(restaurant_id) WHERE restaurant_id IS NOT NULL;',
        description: 'Restaurant favorites (separate columns approach)'
      },
      {
        name: 'idx_user_favorites_menu_item',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_favorites_menu_item ON user_favorites(menu_item_id) WHERE menu_item_id IS NOT NULL;',
        description: 'Menu item favorites (separate columns approach)'
      },
      {
        name: 'idx_user_favorites_generic',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_favorites_generic ON user_favorites(item_type, item_id);',
        description: 'Generic favorites (item_type/item_id approach)'
      },

      // Reviews table indexes
      {
        name: 'idx_reviews_restaurant',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reviews_restaurant ON reviews(restaurant_id, created_at DESC);',
        description: 'Restaurant reviews'
      },
      {
        name: 'idx_reviews_user',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reviews_user ON reviews(user_id, created_at DESC);',
        description: 'User reviews history'
      },
      {
        name: 'idx_reviews_rating',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reviews_rating ON reviews(rating, created_at DESC);',
        description: 'Rating-based review filtering'
      },

      // Notifications table indexes
      {
        name: 'idx_notifications_user',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user ON notifications(user_id, created_at DESC);',
        description: 'User notifications'
      },
      {
        name: 'idx_notifications_read',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_read ON notifications(is_read, created_at DESC);',
        description: 'Unread notifications filter'
      },

      // Promo codes table indexes
      {
        name: 'idx_promo_codes_code',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_promo_codes_code ON promo_codes(code);',
        description: 'Promo code lookup'
      },
      {
        name: 'idx_promo_codes_active',
        query: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_promo_codes_active ON promo_codes(is_active, expires_at) WHERE is_active = true;',
        description: 'Active promo codes'
      }
    ];

    console.log(`📊 Creating ${indexes.length} performance indexes...\n`);

    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;

    for (const index of indexes) {
      try {
        console.log(`⚡ Creating: ${index.name} - ${index.description}`);
        await pool.query(index.query);
        successCount++;
        console.log(`   ✅ Success\n`);
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log(`   ⏭️  Already exists\n`);
          skipCount++;
        } else {
          console.error(`   ❌ Error: ${error.message}\n`);
          errorCount++;
        }
      }
    }

    // Database maintenance queries
    console.log('🧹 Running database maintenance...\n');

    const maintenanceQueries = [
      {
        name: 'Update table statistics',
        query: 'ANALYZE;',
        description: 'Update query planner statistics'
      },
      {
        name: 'Vacuum analyze',
        query: 'VACUUM ANALYZE;',
        description: 'Clean up and analyze all tables'
      }
    ];

    for (const maintenance of maintenanceQueries) {
      try {
        console.log(`🔧 ${maintenance.name} - ${maintenance.description}`);
        await pool.query(maintenance.query);
        console.log(`   ✅ Success\n`);
      } catch (error) {
        console.error(`   ❌ Error: ${error.message}\n`);
      }
    }

    // Summary
    console.log('📋 OPTIMIZATION SUMMARY:');
    console.log(`   ✅ Indexes created: ${successCount}`);
    console.log(`   ⏭️  Indexes skipped: ${skipCount}`);
    console.log(`   ❌ Indexes failed: ${errorCount}`);
    console.log(`   📊 Total indexes: ${indexes.length}\n`);

    if (errorCount === 0) {
      console.log('🎉 Database optimization completed successfully!');
      console.log('💡 Your database queries should now be significantly faster.\n');
    } else {
      console.log('⚠️  Database optimization completed with some errors.');
      console.log('   Check the error messages above for details.\n');
    }

  } catch (error) {
    console.error('❌ Database optimization failed:', error.message);
    process.exit(1);
  } finally {
    await databaseManager.close();
    process.exit(0);
  }
}

// Run optimization if called directly
if (require.main === module) {
  optimizeDatabase();
}

module.exports = { optimizeDatabase };
