// Notifications Routes
const express = require('express');
const NotificationController = require('../controllers/notificationController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting and authentication to all routes
router.use(apiLimiter);
router.use(AuthMiddleware.authenticate);

// GET /api/v1/notifications - Get user notifications
router.get('/',
  NotificationController.getNotifications
);

// GET /api/v1/notifications/unread-count - Get unread notification count
router.get('/unread-count',
  NotificationController.getUnreadCount
);

// PATCH /api/v1/notifications/read-all - Mark all notifications as read
router.patch('/read-all',
  NotificationController.markAllAsRead
);

// PATCH /api/v1/notifications/:id/read - Mark specific notification as read
router.patch('/:id/read',
  ValidationMiddleware.validateUUID('id'),
  NotificationController.markAsRead
);

module.exports = router;
