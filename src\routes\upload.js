// Upload Routes
const express = require('express');
const multer = require('multer');
const UploadController = require('../controllers/uploadController');
const AuthMiddleware = require('../middleware/auth');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

// Apply rate limiting and authentication to all routes
router.use(apiLimiter);
router.use(AuthMiddleware.authenticate);

// POST /api/v1/upload/menu-item - Upload menu item image
router.post('/menu-item',
  upload.single('image'),
  UploadController.uploadMenuItemImage
);

// POST /api/v1/upload/avatar - Upload user avatar
router.post('/avatar',
  upload.single('avatar'),
  UploadController.uploadUserAvatar
);

module.exports = router;
