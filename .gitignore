# ================================
# NODE.JS DEPENDENCIES
# ================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ================================
# ENVIRONMENT VARIABLES
# ================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ================================
# LOGS
# ================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ================================
# RUNTIME DATA
# ================================
pids/
*.pid
*.seed
*.pid.lock

# ================================
# COVERAGE DIRECTORY
# ================================
lib-cov/
coverage/
*.lcov
.nyc_output/

# ================================
# DEPENDENCY DIRECTORIES
# ================================
node_modules/
jspm_packages/

# ================================
# TYPESCRIPT
# ================================
*.tsbuildinfo

# ================================
# OPTIONAL NPM CACHE
# ================================
.npm
.eslintcache

# ================================
# MICROBUNDLE CACHE
# ================================
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ================================
# OPTIONAL ESLINT CACHE
# ================================
.eslintcache

# ================================
# OPTIONAL STYLELINT CACHE
# ================================
.stylelintcache

# ================================
# MICROBUNDLE CACHE
# ================================
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ================================
# OPTIONAL REPL HISTORY
# ================================
.node_repl_history

# ================================
# OUTPUT OF 'NPM PACK'
# ================================
*.tgz

# ================================
# YARN INTEGRITY FILE
# ================================
.yarn-integrity

# ================================
# PARCEL-BUNDLER CACHE
# ================================
.cache
.parcel-cache

# ================================
# NEXT.JS BUILD OUTPUT
# ================================
.next
out

# ================================
# NUXT.JS BUILD / GENERATE OUTPUT
# ================================
.nuxt
dist

# ================================
# GATSBY FILES
# ================================
.cache/
public

# ================================
# VUEPRESS BUILD OUTPUT
# ================================
.vuepress/dist

# ================================
# SERVERLESS DIRECTORIES
# ================================
.serverless/

# ================================
# FUSEBOX CACHE
# ================================
.fusebox/

# ================================
# DYNAMODB LOCAL FILES
# ================================
.dynamodb/

# ================================
# TERNJS PORT FILE
# ================================
.tern-port

# ================================
# STORES VSCode VERSIONS
# ================================
.vscode-test

# ================================
# YARN V2
# ================================
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ================================
# UPLOADS & TEMP FILES
# ================================
uploads/*
!uploads/.gitkeep
temp/
tmp/

# ================================
# DATABASE FILES
# ================================
*.sqlite
*.sqlite3
*.db

# ================================
# OS GENERATED FILES
# ================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ================================
# IDE FILES
# ================================
.vscode/
.idea/
*.swp
*.swo
*~

# ================================
# BACKUP FILES
# ================================
*.bak
*.backup
*.old

# ================================
# DOCKER
# ================================
Dockerfile.dev
docker-compose.override.yml
