// Favorites Service
const databaseManager = require('../config/database');

class FavoritesService {
  // Get user's favorite restaurants and menu items
  static async getFavorites(userId, type = null) {
    const pool = databaseManager.getPool();

    try {
      let whereConditions = ['uf.user_id = $1'];
      let queryParams = [userId];
      let paramIndex = 2;

      if (type) {
        whereConditions.push(`uf.item_type = $${paramIndex}`);
        queryParams.push(type === 'restaurants' ? 'restaurant' : 'menu_item');
        paramIndex++;
      }

      const whereClause = whereConditions.join(' AND ');

      // Get restaurants
      const restaurantsQuery = `
        SELECT 
          uf.id as favorite_id, uf.created_at as added_at,
          r.id, r.name, r.cover_image_url as image, r.rating,
          CONCAT(r.delivery_time_min, '-', r.delivery_time_max, ' min') as delivery_time,
          r.delivery_fee
        FROM user_favorites uf
        JOIN restaurants r ON uf.item_id = r.id
        WHERE ${whereClause} AND uf.item_type = 'restaurant'
        ORDER BY uf.created_at DESC
      `;

      const restaurantsResult = await pool.query(restaurantsQuery, queryParams);

      // Get menu items
      const menuItemsQuery = `
        SELECT 
          uf.id as favorite_id, uf.created_at as added_at,
          mi.id, mi.name, mi.image_url as image, mi.price,
          r.id as restaurant_id, r.name as restaurant_name
        FROM user_favorites uf
        JOIN menu_items mi ON uf.item_id = mi.id
        JOIN restaurants r ON mi.restaurant_id = r.id
        WHERE ${whereClause} AND uf.item_type = 'menu_item'
        ORDER BY uf.created_at DESC
      `;

      const menuItemsResult = await pool.query(menuItemsQuery, queryParams);

      const restaurants = restaurantsResult.rows.map(item => ({
        id: item.id,
        name: item.name,
        image: item.image,
        rating: parseFloat(item.rating || 0),
        deliveryTime: item.delivery_time,
        deliveryFee: parseFloat(item.delivery_fee || 0),
        addedAt: item.added_at
      }));

      const menuItems = menuItemsResult.rows.map(item => ({
        id: item.id,
        name: item.name,
        image: item.image,
        price: parseFloat(item.price),
        restaurant: {
          id: item.restaurant_id,
          name: item.restaurant_name
        },
        addedAt: item.added_at
      }));

      return {
        restaurants,
        menuItems
      };

    } catch (error) {
      throw error;
    }
  }

  // Add item to favorites
  static async addFavorite(userId, type, itemId) {
    const pool = databaseManager.getPool();

    try {
      // Validate that the item exists
      if (type === 'restaurant') {
        const restaurantCheck = await pool.query(
          'SELECT id FROM restaurants WHERE id = $1 AND is_active = true',
          [itemId]
        );
        if (restaurantCheck.rows.length === 0) {
          throw new Error('Restaurant not found');
        }
      } else if (type === 'menu_item') {
        const menuItemCheck = await pool.query(
          'SELECT id FROM menu_items WHERE id = $1 AND is_available = true',
          [itemId]
        );
        if (menuItemCheck.rows.length === 0) {
          throw new Error('Menu item not found');
        }
      }

      // Add to favorites (will ignore if already exists due to unique constraint)
      await pool.query(`
        INSERT INTO user_favorites (user_id, item_type, item_id)
        VALUES ($1, $2, $3)
        ON CONFLICT (user_id, item_type, item_id) DO NOTHING
      `, [userId, type, itemId]);

      return true;

    } catch (error) {
      if (error.code === '23505') { // PostgreSQL unique violation
        throw new Error('Item is already in favorites');
      }
      throw error;
    }
  }

  // Remove item from favorites
  static async removeFavorite(userId, favoriteId) {
    const pool = databaseManager.getPool();

    try {
      const result = await pool.query(
        'DELETE FROM user_favorites WHERE id = $1 AND user_id = $2',
        [favoriteId, userId]
      );

      if (result.rowCount === 0) {
        throw new Error('Favorite not found');
      }

      return true;

    } catch (error) {
      throw error;
    }
  }

  // Remove item from favorites by type and item ID
  static async removeFavoriteByItem(userId, type, itemId) {
    const pool = databaseManager.getPool();

    try {
      const result = await pool.query(
        'DELETE FROM user_favorites WHERE user_id = $1 AND item_type = $2 AND item_id = $3',
        [userId, type, itemId]
      );

      if (result.rowCount === 0) {
        throw new Error('Favorite not found');
      }

      return true;

    } catch (error) {
      throw error;
    }
  }

  // Check if item is favorited by user
  static async isFavorite(userId, type, itemId) {
    const pool = databaseManager.getPool();

    try {
      const result = await pool.query(
        'SELECT id FROM user_favorites WHERE user_id = $1 AND item_type = $2 AND item_id = $3',
        [userId, type, itemId]
      );

      return result.rows.length > 0;

    } catch (error) {
      throw error;
    }
  }

  // Get favorites count for user
  static async getFavoritesCount(userId) {
    const pool = databaseManager.getPool();

    try {
      const result = await pool.query(`
        SELECT 
          item_type,
          COUNT(*) as count
        FROM user_favorites
        WHERE user_id = $1
        GROUP BY item_type
      `, [userId]);

      const counts = {
        restaurants: 0,
        menuItems: 0
      };

      result.rows.forEach(row => {
        if (row.item_type === 'restaurant') {
          counts.restaurants = parseInt(row.count);
        } else if (row.item_type === 'menu_item') {
          counts.menuItems = parseInt(row.count);
        }
      });

      return counts;

    } catch (error) {
      throw error;
    }
  }
}

module.exports = FavoritesService;
