// Favorites Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const FavoritesService = require('../services/favoritesService');
const queryOptimizationService = require('../services/queryOptimizationService');

class FavoritesController {
  // Get user's favorite restaurants and menu items (OPTIMIZED)
  static async getFavorites(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const type = req.query.type;

      // Use optimized query service with caching
      const favorites = await queryOptimizationService.getUserFavorites(userId, type);

      return ResponseHelper.success(res, favorites, 'Favorites retrieved successfully');
    } catch (error) {
      console.error('Get favorites error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve favorites', 500);
    }
  }

  // Add item to favorites
  static async addFavorite(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const { type, itemId } = req.body;

      await FavoritesService.addFavorite(userId, type, itemId);

      // Invalidate user favorites cache
      await queryOptimizationService.invalidateUserCaches(userId);

      return ResponseHelper.success(res, null, 'Added to favorites');
    } catch (error) {
      console.error('Add favorite error:', error);

      if (error.message.includes('not found') || error.message.includes('already in favorites')) {
        return ResponseHelper.error(res, error.message, 400);
      }

      return ResponseHelper.error(res, 'Failed to add favorite', 500);
    }
  }

  // Remove item from favorites
  static async removeFavorite(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const favoriteId = req.params.id;

      await FavoritesService.removeFavorite(userId, favoriteId);

      // Invalidate user favorites cache
      await queryOptimizationService.invalidateUserCaches(userId);

      return ResponseHelper.success(res, null, 'Removed from favorites');
    } catch (error) {
      console.error('Remove favorite error:', error);

      if (error.message.includes('not found')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to remove favorite', 500);
    }
  }
}

module.exports = FavoritesController;
