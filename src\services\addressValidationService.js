// Address Validation Service
const databaseManager = require('../config/database');

class AddressValidationService {
  // Validate delivery address
  static async validateAddress(addressData) {
    try {
      const { street, city, state, zipCode } = addressData;

      // Basic validation
      if (!street || !city || !state || !zipCode) {
        return {
          valid: false,
          deliverable: false,
          estimatedDeliveryTime: null,
          suggestions: [],
          coordinates: null,
          errors: ['All address fields are required']
        };
      }

      // Simulate address validation (in production, use a service like Google Maps API, SmartyStreets, etc.)
      const validationResult = await this.performAddressValidation(addressData);
      
      // Check if address is within delivery area
      const deliveryCheck = await this.checkDeliveryArea(validationResult.coordinates);

      return {
        valid: validationResult.valid,
        deliverable: deliveryCheck.deliverable,
        estimatedDeliveryTime: deliveryCheck.estimatedDeliveryTime,
        suggestions: validationResult.suggestions,
        coordinates: validationResult.coordinates
      };

    } catch (error) {
      console.error('Address validation error:', error);
      throw error;
    }
  }

  // Perform address validation (mock implementation)
  static async performAddressValidation(addressData) {
    try {
      const { street, city, state, zipCode } = addressData;

      // Mock validation logic - in production, integrate with address validation API
      const isValidFormat = this.validateAddressFormat(addressData);
      
      if (!isValidFormat.valid) {
        return {
          valid: false,
          suggestions: isValidFormat.suggestions,
          coordinates: null
        };
      }

      // Mock geocoding - in production, use Google Maps Geocoding API or similar
      const coordinates = await this.geocodeAddress(addressData);

      return {
        valid: true,
        suggestions: [],
        coordinates
      };

    } catch (error) {
      throw error;
    }
  }

  // Validate address format
  static validateAddressFormat(addressData) {
    const { street, city, state, zipCode } = addressData;
    const suggestions = [];

    // Basic format validation
    if (street.length < 5) {
      suggestions.push('Street address seems too short');
    }

    if (!/^\d{5}(-\d{4})?$/.test(zipCode)) {
      suggestions.push('ZIP code should be in format 12345 or 12345-6789');
    }

    // State validation (basic)
    const validStates = ['NY', 'CA', 'TX', 'FL', 'IL', 'PA', 'OH', 'GA', 'NC', 'MI'];
    if (!validStates.includes(state.toUpperCase())) {
      suggestions.push('Please use valid state abbreviation (e.g., NY, CA, TX)');
    }

    return {
      valid: suggestions.length === 0,
      suggestions
    };
  }

  // Mock geocoding service
  static async geocodeAddress(addressData) {
    try {
      const { street, city, state, zipCode } = addressData;

      // Mock coordinates based on city (in production, use real geocoding API)
      const cityCoordinates = {
        'new york': { latitude: 40.7128, longitude: -74.0060 },
        'los angeles': { latitude: 34.0522, longitude: -118.2437 },
        'chicago': { latitude: 41.8781, longitude: -87.6298 },
        'houston': { latitude: 29.7604, longitude: -95.3698 },
        'phoenix': { latitude: 33.4484, longitude: -112.0740 },
        'philadelphia': { latitude: 39.9526, longitude: -75.1652 },
        'san antonio': { latitude: 29.4241, longitude: -98.4936 },
        'san diego': { latitude: 32.7157, longitude: -117.1611 },
        'dallas': { latitude: 32.7767, longitude: -96.7970 },
        'san jose': { latitude: 37.3382, longitude: -121.8863 }
      };

      const cityKey = city.toLowerCase();
      const baseCoords = cityCoordinates[cityKey] || { latitude: 40.7128, longitude: -74.0060 };

      // Add small random offset to simulate exact address coordinates
      const latitude = baseCoords.latitude + (Math.random() - 0.5) * 0.01;
      const longitude = baseCoords.longitude + (Math.random() - 0.5) * 0.01;

      return { latitude, longitude };

    } catch (error) {
      throw error;
    }
  }

  // Check if address is within delivery area
  static async checkDeliveryArea(coordinates) {
    try {
      if (!coordinates) {
        return {
          deliverable: false,
          estimatedDeliveryTime: null
        };
      }

      const pool = databaseManager.getPool();

      // Get restaurant location and delivery radius
      const restaurantQuery = `
        SELECT latitude, longitude, delivery_radius, delivery_time_min, delivery_time_max
        FROM restaurants
        WHERE is_active = true
        ORDER BY created_at ASC
        LIMIT 1
      `;

      const result = await pool.query(restaurantQuery);

      if (result.rows.length === 0) {
        return {
          deliverable: false,
          estimatedDeliveryTime: null
        };
      }

      const restaurant = result.rows[0];

      // Calculate distance using Haversine formula
      const distance = this.calculateDistance(
        restaurant.latitude,
        restaurant.longitude,
        coordinates.latitude,
        coordinates.longitude
      );

      const deliveryRadius = restaurant.delivery_radius || 10000; // Default 10km
      const isDeliverable = distance <= deliveryRadius;

      let estimatedDeliveryTime = null;
      if (isDeliverable) {
        // Calculate estimated delivery time based on distance
        const baseTime = restaurant.delivery_time_min || 25;
        const maxTime = restaurant.delivery_time_max || 35;
        const distanceInKm = distance / 1000;
        
        // Add 2 minutes per km beyond 2km
        const additionalTime = Math.max(0, (distanceInKm - 2) * 2);
        const minTime = Math.round(baseTime + additionalTime);
        const maxTimeAdjusted = Math.round(maxTime + additionalTime);

        estimatedDeliveryTime = `${minTime}-${maxTimeAdjusted} min`;
      }

      return {
        deliverable: isDeliverable,
        estimatedDeliveryTime
      };

    } catch (error) {
      console.error('Delivery area check error:', error);
      return {
        deliverable: false,
        estimatedDeliveryTime: null
      };
    }
  }

  // Calculate distance between two coordinates using Haversine formula
  static calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371000; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
  }

  // Get delivery zones (for admin purposes)
  static async getDeliveryZones() {
    try {
      const pool = databaseManager.getPool();

      const query = `
        SELECT 
          id, name, latitude, longitude, delivery_radius,
          delivery_time_min, delivery_time_max
        FROM restaurants
        WHERE is_active = true
      `;

      const result = await pool.query(query);

      return result.rows.map(restaurant => ({
        id: restaurant.id,
        name: restaurant.name,
        center: {
          latitude: parseFloat(restaurant.latitude),
          longitude: parseFloat(restaurant.longitude)
        },
        radius: restaurant.delivery_radius,
        estimatedDeliveryTime: `${restaurant.delivery_time_min}-${restaurant.delivery_time_max} min`
      }));

    } catch (error) {
      throw error;
    }
  }

  // Validate multiple addresses (batch validation)
  static async validateMultipleAddresses(addresses) {
    try {
      const results = await Promise.all(
        addresses.map(address => this.validateAddress(address))
      );

      return results;

    } catch (error) {
      throw error;
    }
  }
}

module.exports = AddressValidationService;
