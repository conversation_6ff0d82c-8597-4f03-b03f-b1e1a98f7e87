// User Address Routes
const express = require('express');
const UserAddressController = require('../controllers/userAddressController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting to all routes
router.use(apiLimiter);

// Public routes (no authentication required)

// POST /api/v1/addresses/validate - Validate delivery address
router.post('/validate',
  ValidationMiddleware.validateDeliveryAddress(),
  UserAddressController.validateAddress
);

// Protected routes (authentication required)
router.use(AuthMiddleware.authenticate);

// GET /api/v1/addresses - Get user's addresses
router.get('/',
  UserAddressController.getUserAddresses
);

// GET /api/v1/addresses/default - Get user's default address
router.get('/default',
  UserAddressController.getDefaultAddress
);

// POST /api/v1/addresses - Create new address
router.post('/',
  ValidationMiddleware.validateCreateAddress(),
  UserAddressController.createAddress
);

// PUT /api/v1/addresses/:id - Update address
router.put('/:id',
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateUpdateAddress(),
  UserAddressController.updateAddress
);

// PUT /api/v1/addresses/:id/default - Set default address
router.put('/:id/default',
  ValidationMiddleware.validateUUID('id'),
  UserAddressController.setDefaultAddress
);

// DELETE /api/v1/addresses/:id - Delete address
router.delete('/:id',
  ValidationMiddleware.validateUUID('id'),
  UserAddressController.deleteAddress
);

// GET /api/v1/addresses/:addressId/distance/:restaurantId - Calculate delivery distance
router.get('/:addressId/distance/:restaurantId',
  ValidationMiddleware.validateUUID('addressId'),
  ValidationMiddleware.validateUUID('restaurantId'),
  UserAddressController.calculateDeliveryDistance
);

module.exports = router;
