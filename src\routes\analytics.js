// Analytics Routes
const express = require('express');
const AnalyticsController = require('../controllers/analyticsController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting and authentication to all routes
router.use(apiLimiter);
router.use(AuthMiddleware.authenticate);
router.use(AuthMiddleware.requireAdmin); // All analytics routes require admin access

// GET /api/v1/analytics/dashboard - Get dashboard overview
router.get('/dashboard',
  AnalyticsController.getDashboardOverview
);

// GET /api/v1/analytics/orders - Get order analytics
router.get('/orders',
  AnalyticsController.getOrderAnalytics
);

// GET /api/v1/analytics/popular-items - Get popular items analytics
router.get('/popular-items',
  AnalyticsController.getPopularItemsAnalytics
);

// GET /api/v1/analytics/restaurants/:restaurantId - Get restaurant performance analytics
router.get('/restaurants/:restaurantId',
  ValidationMiddleware.validateUUID('restaurantId'),
  AnalyticsController.getRestaurantAnalytics
);

// GET /api/v1/analytics/customers - Get customer analytics
router.get('/customers',
  AnalyticsController.getCustomerAnalytics
);

// GET /api/v1/analytics/revenue - Get revenue analytics
router.get('/revenue',
  ValidationMiddleware.validateRevenueAnalyticsQuery(),
  AnalyticsController.getRevenueAnalytics
);

// GET /api/v1/analytics/promo-codes - Get promo code analytics
router.get('/promo-codes',
  AnalyticsController.getPromoCodeAnalytics
);

// GET /api/v1/analytics/export - Export analytics data
router.get('/export',
  ValidationMiddleware.validateAnalyticsExport(),
  AnalyticsController.exportAnalytics
);

module.exports = router;
