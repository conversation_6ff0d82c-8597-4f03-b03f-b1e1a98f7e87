// Authentication Service
const bcrypt = require('bcrypt');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const config = require('../config/app');
const databaseManager = require('../config/database');
const AuthMiddleware = require('../middleware/auth');
const EmailService = require('./emailService');

class AuthService {
  // Register new user
  static async register(userData) {
    const { email, password, firstName, lastName, phone } = userData;
    const pool = databaseManager.getPool();

    try {
      // Check if user already exists
      const existingUser = await pool.query(
        'SELECT id FROM users WHERE email = $1',
        [email.toLowerCase()]
      );

      if (existingUser.rows.length > 0) {
        throw new Error('User with this email already exists');
      }

      // Hash password
      const passwordHash = await bcrypt.hash(password, config.security.bcryptSaltRounds);

      // Generate email verification token
      const emailVerificationToken = crypto.randomBytes(32).toString('hex');
      const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Create user
      const userResult = await pool.query(`
        INSERT INTO users (
          email, password_hash, first_name, last_name, phone,
          email_verification_token, email_verification_expires
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id, email, first_name, last_name, phone, avatar_url, 
                  email_verified, phone_verified, created_at
      `, [
        email.toLowerCase(),
        passwordHash,
        firstName,
        lastName,
        phone,
        emailVerificationToken,
        emailVerificationExpires
      ]);

      const user = userResult.rows[0];

      // Generate tokens
      const token = AuthMiddleware.generateToken(user.id, user.email, 'customer');
      const refreshToken = AuthMiddleware.generateRefreshToken();

      // Store refresh token
      await pool.query(
        'UPDATE users SET refresh_token = $1 WHERE id = $2',
        [refreshToken, user.id]
      );

      // Send verification email (async, don't wait)
      EmailService.sendVerificationEmail(user.email, user.first_name, emailVerificationToken)
        .catch(error => console.error('Failed to send verification email:', error));

      return {
        user,
        token,
        refreshToken
      };

    } catch (error) {
      if (error.code === '23505') { // PostgreSQL unique violation
        throw new Error('User with this email already exists');
      }
      throw error;
    }
  }

  // Login user
  static async login(email, password) {
    const pool = databaseManager.getPool();

    try {
      // Get user with password hash
      const userResult = await pool.query(`
        SELECT id, email, password_hash, first_name, last_name, phone, 
               avatar_url, email_verified, phone_verified, is_active
        FROM users 
        WHERE email = $1
      `, [email.toLowerCase()]);

      if (userResult.rows.length === 0) {
        throw new Error('Invalid email or password');
      }

      const user = userResult.rows[0];

      if (!user.is_active) {
        throw new Error('Account has been deactivated');
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        throw new Error('Invalid email or password');
      }

      // Generate tokens
      const token = AuthMiddleware.generateToken(user.id, user.email, 'customer');
      const refreshToken = AuthMiddleware.generateRefreshToken();

      // Update last login and refresh token
      await pool.query(
        'UPDATE users SET refresh_token = $1, last_login = NOW() WHERE id = $2',
        [refreshToken, user.id]
      );

      // Remove password hash from response
      delete user.password_hash;

      return {
        user,
        token,
        refreshToken
      };

    } catch (error) {
      throw error;
    }
  }

  // Refresh access token
  static async refreshToken(refreshToken) {
    const pool = databaseManager.getPool();

    try {
      // Verify refresh token
      const decoded = AuthMiddleware.verifyRefreshToken(refreshToken);
      if (!decoded) {
        throw new Error('Invalid refresh token');
      }

      // Find user with this refresh token
      const userResult = await pool.query(`
        SELECT id, email, first_name, last_name, phone, avatar_url, 
               email_verified, phone_verified, is_active
        FROM users 
        WHERE refresh_token = $1 AND is_active = true
      `, [refreshToken]);

      if (userResult.rows.length === 0) {
        throw new Error('Invalid refresh token');
      }

      const user = userResult.rows[0];

      // Generate new tokens
      const newToken = AuthMiddleware.generateToken(user.id, user.email, 'customer');
      const newRefreshToken = AuthMiddleware.generateRefreshToken();

      // Update refresh token in database
      await pool.query(
        'UPDATE users SET refresh_token = $1 WHERE id = $2',
        [newRefreshToken, user.id]
      );

      return {
        user,
        token: newToken,
        refreshToken: newRefreshToken
      };

    } catch (error) {
      throw error;
    }
  }

  // Logout user
  static async logout(userId) {
    const pool = databaseManager.getPool();

    try {
      // Clear refresh token
      await pool.query(
        'UPDATE users SET refresh_token = NULL WHERE id = $1',
        [userId]
      );

      return true;
    } catch (error) {
      throw error;
    }
  }

  // Request password reset
  static async requestPasswordReset(email) {
    const pool = databaseManager.getPool();

    try {
      // Check if user exists
      const userResult = await pool.query(
        'SELECT id, first_name FROM users WHERE email = $1 AND is_active = true',
        [email.toLowerCase()]
      );

      if (userResult.rows.length === 0) {
        // Don't reveal if email exists or not
        return true;
      }

      const user = userResult.rows[0];

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      // Store reset token
      await pool.query(
        'UPDATE users SET password_reset_token = $1, password_reset_expires = $2 WHERE id = $3',
        [resetToken, resetExpires, user.id]
      );

      // Send reset email (async, don't wait)
      EmailService.sendPasswordResetEmail(email, user.first_name, resetToken)
        .catch(error => console.error('Failed to send password reset email:', error));

      return true;
    } catch (error) {
      throw error;
    }
  }

  // Reset password
  static async resetPassword(token, newPassword) {
    const pool = databaseManager.getPool();

    try {
      // Find user with valid reset token
      const userResult = await pool.query(`
        SELECT id FROM users 
        WHERE password_reset_token = $1 
        AND password_reset_expires > NOW() 
        AND is_active = true
      `, [token]);

      if (userResult.rows.length === 0) {
        throw new Error('Invalid or expired reset token');
      }

      const user = userResult.rows[0];

      // Hash new password
      const passwordHash = await bcrypt.hash(newPassword, config.security.bcryptSaltRounds);

      // Update password and clear reset token
      await pool.query(`
        UPDATE users 
        SET password_hash = $1, 
            password_reset_token = NULL, 
            password_reset_expires = NULL,
            refresh_token = NULL
        WHERE id = $2
      `, [passwordHash, user.id]);

      return true;
    } catch (error) {
      throw error;
    }
  }

  // Verify email
  static async verifyEmail(token) {
    const pool = databaseManager.getPool();

    try {
      // Find user with valid verification token
      const userResult = await pool.query(`
        SELECT id FROM users 
        WHERE email_verification_token = $1 
        AND email_verification_expires > NOW() 
        AND is_active = true
      `, [token]);

      if (userResult.rows.length === 0) {
        throw new Error('Invalid or expired verification token');
      }

      const user = userResult.rows[0];

      // Mark email as verified and clear verification token
      await pool.query(`
        UPDATE users 
        SET email_verified = true, 
            email_verification_token = NULL, 
            email_verification_expires = NULL
        WHERE id = $1
      `, [user.id]);

      return true;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = AuthService;
