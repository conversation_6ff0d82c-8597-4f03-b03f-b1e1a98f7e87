// Restaurant Routes
const express = require('express');
const RestaurantController = require('../controllers/restaurantController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter, searchLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting to all routes
router.use(apiLimiter);

// Public routes (no authentication required)

// GET /api/v1/restaurant - Get restaurants with filtering and pagination
router.get('/',
  ValidationMiddleware.validateRestaurantQuery(),
  RestaurantController.getRestaurants
);

// GET /api/v1/restaurants/search - Search restaurants
router.get('/search',
  ValidationMiddleware.validateRestaurantQuery(),
  RestaurantController.getRestaurants
);

// GET /api/v1/restaurants/nearby - Get nearby restaurants
router.get('/nearby',
  ValidationMiddleware.validateNearbyRestaurants(),
  RestaurantController.getNearbyRestaurants
);

// GET /api/v1/restaurants/featured - Get featured restaurants
router.get('/featured',
  RestaurantController.getFeaturedRestaurants
);

// GET /api/v1/restaurants/popular - Get popular restaurants
router.get('/popular',
  RestaurantController.getPopularRestaurants
);

// GET /api/v1/restaurants/cuisine/:cuisine - Get restaurants by cuisine type
router.get('/cuisine/:cuisine',
  ValidationMiddleware.validateCuisineType(),
  RestaurantController.getRestaurantsByCuisine
);

// GET /api/v1/restaurant/slug/:slug - Get restaurant by slug
router.get('/slug/:slug',
  ValidationMiddleware.validateSlug(),
  RestaurantController.getRestaurantBySlug
);

// GET /api/v1/restaurant/:id - Get restaurant details with menu
router.get('/:id',
  ValidationMiddleware.validateUUID('id'),
  RestaurantController.getRestaurantById
);

// GET /api/v1/restaurant/:id/reviews - Get restaurant reviews
router.get('/:id/reviews',
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateReviewQuery(),
  RestaurantController.getRestaurantReviews
);

// GET /api/v1/restaurant/:id/reviews/stats - Get restaurant review statistics
router.get('/:id/reviews/stats',
  ValidationMiddleware.validateUUID('id'),
  RestaurantController.getRestaurantReviewStats
);

// GET /api/v1/restaurant/:id/stats - Get restaurant statistics
router.get('/:id/stats',
  ValidationMiddleware.validateUUID('id'),
  RestaurantController.getRestaurantStats
);

// GET /api/v1/restaurant/:id/status - Check restaurant status (open/closed)
router.get('/:id/status',
  ValidationMiddleware.validateUUID('id'),
  RestaurantController.checkRestaurantStatus
);

// Protected routes (authentication required)

// PUT /api/v1/restaurant/:id/accepting-orders - Update restaurant accepting orders status (admin only)
router.put('/:id/accepting-orders',
  AuthMiddleware.authenticate,
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateAcceptingOrdersUpdate(),
  RestaurantController.updateAcceptingOrders
);

module.exports = router;
