// Payment Service
const databaseManager = require('../config/database');

// Initialize Stripe only if API key is provided
let stripe = null;
if (process.env.STRIPE_SECRET_KEY) {
  stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
} else {
  console.warn('⚠️  STRIPE_SECRET_KEY not found - Payment features will be disabled');
}

class PaymentService {
  // Create payment intent for order
  static async createPaymentIntent(orderId, amount, currency = 'usd') {
    try {
      if (!stripe) {
        throw new Error('Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.');
      }

      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency,
        metadata: {
          orderId: orderId
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id
      };

    } catch (error) {
      throw new Error(`Payment intent creation failed: ${error.message}`);
    }
  }

  // Confirm payment intent
  static async confirmPaymentIntent(paymentIntentId) {
    try {
      const paymentIntent = await stripe.paymentIntents.confirm(paymentIntentId);
      return paymentIntent;

    } catch (error) {
      throw new Error(`Payment confirmation failed: ${error.message}`);
    }
  }

  // Create customer in Stripe
  static async createCustomer(userEmail, userName) {
    try {
      const customer = await stripe.customers.create({
        email: userEmail,
        name: userName,
      });

      return customer;

    } catch (error) {
      throw new Error(`Customer creation failed: ${error.message}`);
    }
  }

  // Add payment method to customer
  static async addPaymentMethod(customerId, paymentMethodId) {
    try {
      const paymentMethod = await stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      return paymentMethod;

    } catch (error) {
      throw new Error(`Payment method attachment failed: ${error.message}`);
    }
  }

  // Get customer payment methods
  static async getCustomerPaymentMethods(customerId) {
    try {
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customerId,
        type: 'card',
      });

      return paymentMethods.data.map(pm => ({
        id: pm.id,
        type: pm.type,
        brand: pm.card.brand,
        last4: pm.card.last4,
        expiryMonth: pm.card.exp_month,
        expiryYear: pm.card.exp_year,
        cardholderName: pm.billing_details.name
      }));

    } catch (error) {
      throw new Error(`Failed to retrieve payment methods: ${error.message}`);
    }
  }

  // Delete payment method
  static async deletePaymentMethod(paymentMethodId) {
    try {
      const paymentMethod = await stripe.paymentMethods.detach(paymentMethodId);
      return paymentMethod;

    } catch (error) {
      throw new Error(`Payment method deletion failed: ${error.message}`);
    }
  }

  // Process refund
  static async processRefund(paymentIntentId, amount = null, reason = 'requested_by_customer') {
    try {
      const refundData = {
        payment_intent: paymentIntentId,
        reason: reason
      };

      if (amount) {
        refundData.amount = Math.round(amount * 100); // Convert to cents
      }

      const refund = await stripe.refunds.create(refundData);
      return refund;

    } catch (error) {
      throw new Error(`Refund processing failed: ${error.message}`);
    }
  }

  // Handle webhook events
  static async handleWebhook(body, signature) {
    try {
      const event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSuccess(event.data.object);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentFailure(event.data.object);
          break;
        case 'payment_method.attached':
          await this.handlePaymentMethodAttached(event.data.object);
          break;
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      return { received: true };

    } catch (error) {
      throw new Error(`Webhook handling failed: ${error.message}`);
    }
  }

  // Handle successful payment
  static async handlePaymentSuccess(paymentIntent) {
    const pool = databaseManager.getPool();

    try {
      const orderId = paymentIntent.metadata.orderId;

      // Update order payment status
      await pool.query(
        'UPDATE orders SET payment_status = $1, stripe_payment_intent_id = $2, updated_at = NOW() WHERE id = $3',
        ['paid', paymentIntent.id, orderId]
      );

      // Update order status to confirmed
      await pool.query(
        'UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2',
        ['confirmed', orderId]
      );

      // Add order tracking entry
      await pool.query(
        'INSERT INTO order_tracking (order_id, status, message) VALUES ($1, $2, $3)',
        [orderId, 'confirmed', 'Payment confirmed, order confirmed by restaurant']
      );

      console.log(`Payment successful for order ${orderId}`);

    } catch (error) {
      console.error('Error handling payment success:', error);
    }
  }

  // Handle failed payment
  static async handlePaymentFailure(paymentIntent) {
    const pool = databaseManager.getPool();

    try {
      const orderId = paymentIntent.metadata.orderId;

      // Update order payment status
      await pool.query(
        'UPDATE orders SET payment_status = $1, stripe_payment_intent_id = $2, updated_at = NOW() WHERE id = $3',
        ['failed', paymentIntent.id, orderId]
      );

      // Update order status to cancelled
      await pool.query(
        'UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2',
        ['cancelled', orderId]
      );

      // Add order tracking entry
      await pool.query(
        'INSERT INTO order_tracking (order_id, status, message) VALUES ($1, $2, $3)',
        [orderId, 'cancelled', 'Payment failed, order cancelled']
      );

      console.log(`Payment failed for order ${orderId}`);

    } catch (error) {
      console.error('Error handling payment failure:', error);
    }
  }

  // Handle payment method attached
  static async handlePaymentMethodAttached(paymentMethod) {
    const pool = databaseManager.getPool();

    try {
      // Update user's payment methods in database if needed
      console.log(`Payment method ${paymentMethod.id} attached to customer ${paymentMethod.customer}`);

    } catch (error) {
      console.error('Error handling payment method attachment:', error);
    }
  }

  // Get payment intent details
  static async getPaymentIntent(paymentIntentId) {
    try {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      return paymentIntent;

    } catch (error) {
      throw new Error(`Failed to retrieve payment intent: ${error.message}`);
    }
  }

  // Calculate application fee (for marketplace scenarios)
  static calculateApplicationFee(orderAmount, feePercentage = 2.9) {
    return Math.round(orderAmount * (feePercentage / 100) * 100); // Convert to cents
  }
}

module.exports = PaymentService;
