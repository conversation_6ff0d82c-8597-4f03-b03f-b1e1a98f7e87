// Address Controller
const ResponseHelper = require('../utils/response');
const AddressValidationService = require('../services/addressValidationService');

class AddressController {
  // Validate delivery address
  static async validateAddress(req, res) {
    try {
      // Import validationResult here to avoid initialization issues
      const { validationResult } = require('express-validator');

      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const addressData = req.body;
      const addressValidationResult = await AddressValidationService.validateAddress(addressData);

      return ResponseHelper.success(res, addressValidationResult, 'Address validation completed');

    } catch (error) {
      console.error('Address validation error:', error);
      return ResponseHelper.error(res, 'Failed to validate address', 500);
    }
  }

  // Get delivery zones
  static async getDeliveryZones(req, res) {
    try {
      const zones = await AddressValidationService.getDeliveryZones();

      return ResponseHelper.success(res, zones, 'Delivery zones retrieved successfully');

    } catch (error) {
      console.error('Get delivery zones error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve delivery zones', 500);
    }
  }

  // Validate multiple addresses
  static async validateMultipleAddresses(req, res) {
    try {
      // Import validationResult here to avoid initialization issues
      const { validationResult } = require('express-validator');

      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { addresses } = req.body;

      if (!Array.isArray(addresses) || addresses.length === 0) {
        return ResponseHelper.error(res, 'Addresses array is required', 400);
      }

      if (addresses.length > 10) {
        return ResponseHelper.error(res, 'Maximum 10 addresses can be validated at once', 400);
      }

      const results = await AddressValidationService.validateMultipleAddresses(addresses);

      return ResponseHelper.success(res, { results }, 'Multiple addresses validated successfully');

    } catch (error) {
      console.error('Multiple address validation error:', error);
      return ResponseHelper.error(res, 'Failed to validate addresses', 500);
    }
  }
}

module.exports = AddressController;
