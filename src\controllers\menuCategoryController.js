// MenuCategory Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const MenuCategory = require('../models/MenuCategory');

class MenuCategoryController {
  // Get categories by restaurant
  static async getCategoriesByRestaurant(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.restaurantId;
      const activeOnly = req.query.activeOnly !== 'false';
      
      const menuCategoryModel = new MenuCategory();
      const categories = await menuCategoryModel.getCategoriesByRestaurant(restaurantId, activeOnly);

      return ResponseHelper.success(res, { categories }, 'Menu categories retrieved successfully');
    } catch (error) {
      console.error('Get categories by restaurant error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve menu categories', 500);
    }
  }

  // Get category with menu items
  static async getCategoryWithItems(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const categoryId = req.params.id;
      const menuCategoryModel = new MenuCategory();
      
      const category = await menuCategoryModel.getCategoryWithItems(categoryId);

      if (!category) {
        return ResponseHelper.error(res, 'Menu category not found', 404, {
          code: 'NOT_FOUND',
          message: 'Menu category not found or is not active'
        });
      }

      return ResponseHelper.success(res, category, 'Menu category with items retrieved successfully');
    } catch (error) {
      console.error('Get category with items error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve menu category with items', 500);
    }
  }

  // Create menu category
  static async createCategory(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const categoryData = req.body;
      const menuCategoryModel = new MenuCategory();
      
      const category = await menuCategoryModel.createCategory(categoryData);

      return ResponseHelper.success(res, category, 'Menu category created successfully', 201);
    } catch (error) {
      console.error('Create category error:', error);
      
      if (error.code === '23505') { // Unique violation
        return ResponseHelper.error(res, 'Category name already exists for this restaurant', 409);
      }

      return ResponseHelper.error(res, 'Failed to create menu category', 500);
    }
  }

  // Update menu category
  static async updateCategory(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const categoryId = req.params.id;
      const updateData = req.body;
      const menuCategoryModel = new MenuCategory();
      
      const category = await menuCategoryModel.updateById(categoryId, updateData);

      if (!category) {
        return ResponseHelper.error(res, 'Menu category not found', 404, {
          code: 'NOT_FOUND',
          message: 'Menu category not found'
        });
      }

      return ResponseHelper.success(res, category, 'Menu category updated successfully');
    } catch (error) {
      console.error('Update category error:', error);
      
      if (error.code === '23505') { // Unique violation
        return ResponseHelper.error(res, 'Category name already exists for this restaurant', 409);
      }

      return ResponseHelper.error(res, 'Failed to update menu category', 500);
    }
  }

  // Delete menu category
  static async deleteCategory(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const categoryId = req.params.id;
      const { newCategoryId } = req.body;
      const menuCategoryModel = new MenuCategory();
      
      const result = await menuCategoryModel.deleteCategoryAndReassignItems(categoryId, newCategoryId);

      if (!result) {
        return ResponseHelper.error(res, 'Menu category not found', 404, {
          code: 'NOT_FOUND',
          message: 'Menu category not found'
        });
      }

      return ResponseHelper.success(res, { deleted: true }, 'Menu category deleted successfully');
    } catch (error) {
      console.error('Delete category error:', error);
      return ResponseHelper.error(res, 'Failed to delete menu category', 500);
    }
  }

  // Reorder categories
  static async reorderCategories(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.restaurantId;
      const { categoryOrders } = req.body;
      const menuCategoryModel = new MenuCategory();
      
      const result = await menuCategoryModel.reorderCategories(restaurantId, categoryOrders);

      return ResponseHelper.success(res, { reordered: result }, 'Menu categories reordered successfully');
    } catch (error) {
      console.error('Reorder categories error:', error);
      return ResponseHelper.error(res, 'Failed to reorder menu categories', 500);
    }
  }

  // Toggle category active status
  static async toggleCategoryActive(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const categoryId = req.params.id;
      const menuCategoryModel = new MenuCategory();
      
      const category = await menuCategoryModel.toggleActive(categoryId);

      if (!category) {
        return ResponseHelper.error(res, 'Menu category not found', 404, {
          code: 'NOT_FOUND',
          message: 'Menu category not found'
        });
      }

      return ResponseHelper.success(res, category, 'Menu category status updated successfully');
    } catch (error) {
      console.error('Toggle category active error:', error);
      return ResponseHelper.error(res, 'Failed to update menu category status', 500);
    }
  }

  // Get category statistics
  static async getCategoryStats(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const categoryId = req.params.id;
      const days = parseInt(req.query.days) || 30;
      const menuCategoryModel = new MenuCategory();
      
      const stats = await menuCategoryModel.getCategoryStats(categoryId, days);

      if (!stats) {
        return ResponseHelper.error(res, 'Menu category not found', 404, {
          code: 'NOT_FOUND',
          message: 'Menu category not found'
        });
      }

      return ResponseHelper.success(res, stats, 'Menu category statistics retrieved successfully');
    } catch (error) {
      console.error('Get category stats error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve menu category statistics', 500);
    }
  }

  // Get popular categories
  static async getPopularCategories(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.restaurantId;
      const limit = Math.min(parseInt(req.query.limit) || 10, 50);
      const days = parseInt(req.query.days) || 30;
      const menuCategoryModel = new MenuCategory();
      
      const categories = await menuCategoryModel.getPopularCategories(restaurantId, limit, days);

      return ResponseHelper.success(res, { categories }, 'Popular menu categories retrieved successfully');
    } catch (error) {
      console.error('Get popular categories error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve popular menu categories', 500);
    }
  }
}

module.exports = MenuCategoryController;
