// Insert Default Business Hours
require('dotenv').config();
const { Pool } = require('pg');

async function insertDefaultHours() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    console.log('📊 Inserting default business hours...');
    
    const restaurants = await pool.query('SELECT id FROM restaurants WHERE is_active = true');
    
    for (const restaurant of restaurants.rows) {
      for (let day = 0; day <= 6; day++) {
        const openTime = day === 0 ? '10:00' : (day >= 5 ? '10:00' : '11:00');
        const closeTime = day === 0 ? '21:00' : (day >= 5 ? '23:00' : '22:00');
        
        await pool.query(
          'INSERT INTO restaurant_business_hours (restaurant_id, day_of_week, open_time, close_time) VALUES ($1, $2, $3, $4) ON CONFLICT (restaurant_id, day_of_week) DO NOTHING',
          [restaurant.id, day, openTime, closeTime]
        );
      }
    }
    
    console.log('✅ Default business hours inserted successfully');
    
  } catch (error) {
    console.error('❌ Error inserting business hours:', error.message);
  } finally {
    await pool.end();
  }
}

insertDefaultHours();
