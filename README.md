# FoodWay Backend 🍕

Modern food delivery backend API built with Node.js, Express, PostgreSQL, and Redis. Deployed on Railway with full CI/CD integration.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env with your Railway database credentials

# Run database migrations
npm run db:migrate

# Start development server
npm run dev
```

## 📁 Project Structure

```
foodway-backend/
├── docs/                    # 📚 Documentation
│   ├── README.md           # Detailed project documentation
│   ├── DEPLOYMENT.md       # Railway deployment guide
│   └── DEVELOPMENT.md      # Development guidelines
├── src/                    # 🏗️ Source code
│   ├── config/            # Configuration files
│   ├── controllers/       # Route controllers
│   ├── middleware/        # Custom middleware
│   ├── models/           # Database models (to be added)
│   ├── routes/           # API routes
│   ├── services/         # Business logic services (to be added)
│   └── utils/            # Utility functions (to be added)
├── __tests__/             # 🧪 Test files
│   ├── unit/             # Unit tests
│   ├── integration/      # Integration tests
│   ├── fixtures/         # Test data
│   └── setup.js          # Test configuration
├── scripts/              # 🔧 Database and utility scripts
├── coverage/             # 📊 Test coverage reports
├── server.js             # 🚀 Application entry point
└── package.json          # 📦 Dependencies and scripts
```

## 🛠️ Available Scripts

```bash
# Development
npm run dev              # Start with nodemon
npm start               # Start production server

# Testing
npm test                # Run all tests
npm run test:unit       # Run unit tests only
npm run test:integration # Run integration tests only
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage

# Database
npm run db:migrate      # Run database migrations
npm run db:seed         # Seed with sample data
npm run db:reset        # Reset database (development only)
```

## 📚 Documentation

For detailed documentation, please see the [docs](./docs/) folder:

- **[📖 Complete Documentation](./docs/README.md)** - Full project documentation
- **[🚀 Deployment Guide](./docs/DEPLOYMENT.md)** - Railway deployment instructions
- **[👩‍💻 Development Guide](./docs/DEVELOPMENT.md)** - Development guidelines and best practices

## 🌟 Features

- **Modern Architecture**: Clean, modular structure with separation of concerns
- **Railway Ready**: Optimized for Railway deployment with health checks
- **Database Management**: Migration and seeding scripts
- **Testing**: Jest testing framework with unit and integration tests
- **Security**: Helmet, CORS, input validation
- **Performance**: Compression, connection pooling
- **Development**: Hot reload, comprehensive logging, organized structure

## 🔧 Tech Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Database**: PostgreSQL (Railway)
- **Cache**: Redis (Railway)
- **Testing**: Jest + Supertest
- **Security**: Helmet, CORS, bcrypt
- **Validation**: express-validator

## 📊 API Endpoints

### Health & Status
- `GET /` - Server information
- `GET /health` - Health check (Railway compatible)
- `GET /test-db` - Database connection test
- `GET /test-redis` - Redis connection test

### Coming Soon
- Authentication endpoints
- User management
- Restaurant management
- Order management

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License.

---

**FoodWay Backend** - Built with ❤️ for modern food delivery

For more detailed information, visit the [documentation](./docs/) folder.
