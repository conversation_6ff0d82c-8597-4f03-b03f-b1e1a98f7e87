// Analytics Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const Order = require('../models/Order');
const MenuItem = require('../models/MenuItem');
const Restaurant = require('../models/Restaurant');
const Review = require('../models/Review');
const UserFavorite = require('../models/UserFavorite');
const PromoCode = require('../models/PromoCode');

class AnalyticsController {
  // Get dashboard overview (admin only)
  static async getDashboardOverview(req, res) {
    try {
      const days = parseInt(req.query.days) || 30;
      
      const orderModel = new Order();
      const menuItemModel = new MenuItem();
      const restaurantModel = new Restaurant();
      const reviewModel = new Review();

      // Get basic metrics
      const [
        orderAnalytics,
        popularItems,
        recentOrders,
        ordersByStatus
      ] = await Promise.all([
        orderModel.getOrderAnalytics(days),
        menuItemModel.getPopularItems(10),
        orderModel.getOrdersByStatus('pending', 1, 10),
        orderModel.getOrdersByStatus('confirmed', 1, 5)
      ]);

      // Calculate summary metrics
      const totalRevenue = orderAnalytics.reduce((sum, day) => sum + (parseFloat(day.total_revenue) || 0), 0);
      const totalOrders = orderAnalytics.reduce((sum, day) => sum + parseInt(day.total_orders), 0);
      const completedOrders = orderAnalytics.reduce((sum, day) => sum + parseInt(day.completed_orders), 0);
      const avgOrderValue = totalOrders > 0 ? totalRevenue / completedOrders : 0;

      const overview = {
        summary: {
          totalRevenue: Math.round(totalRevenue * 100) / 100,
          totalOrders,
          completedOrders,
          avgOrderValue: Math.round(avgOrderValue * 100) / 100,
          completionRate: totalOrders > 0 ? Math.round((completedOrders / totalOrders) * 100) : 0
        },
        dailyAnalytics: orderAnalytics,
        popularItems: popularItems.slice(0, 5),
        pendingOrders: recentOrders.orders,
        confirmedOrders: ordersByStatus.orders
      };

      return ResponseHelper.success(res, overview, 'Dashboard overview retrieved successfully');
    } catch (error) {
      console.error('Get dashboard overview error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve dashboard overview', 500);
    }
  }

  // Get order analytics (admin only)
  static async getOrderAnalytics(req, res) {
    try {
      const days = parseInt(req.query.days) || 30;
      const orderModel = new Order();

      const analytics = await orderModel.getOrderAnalytics(days);

      return ResponseHelper.success(res, { analytics }, 'Order analytics retrieved successfully');
    } catch (error) {
      console.error('Get order analytics error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve order analytics', 500);
    }
  }

  // Get popular menu items analytics (admin only)
  static async getPopularItemsAnalytics(req, res) {
    try {
      const limit = Math.min(parseInt(req.query.limit) || 20, 100);
      const days = parseInt(req.query.days) || 30;
      const menuItemModel = new MenuItem();

      const popularItems = await menuItemModel.getPopularItems(limit);

      return ResponseHelper.success(res, { popularItems }, 'Popular items analytics retrieved successfully');
    } catch (error) {
      console.error('Get popular items analytics error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve popular items analytics', 500);
    }
  }

  // Get restaurant performance analytics (admin only)
  static async getRestaurantAnalytics(req, res) {
    try {
      const restaurantId = req.params.restaurantId;
      const days = parseInt(req.query.days) || 30;
      
      const restaurantModel = new Restaurant();
      const orderModel = new Order();
      const reviewModel = new Review();

      const [
        restaurantStats,
        reviewStats,
        orderAnalytics
      ] = await Promise.all([
        restaurantModel.getRestaurantStats(restaurantId, days),
        reviewModel.getRestaurantReviewStats(restaurantId),
        orderModel.getOrderAnalytics(days) // Filter by restaurant in a real implementation
      ]);

      const analytics = {
        restaurant: restaurantStats,
        reviews: reviewStats,
        orders: orderAnalytics
      };

      return ResponseHelper.success(res, analytics, 'Restaurant analytics retrieved successfully');
    } catch (error) {
      console.error('Get restaurant analytics error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve restaurant analytics', 500);
    }
  }

  // Get customer analytics (admin only)
  static async getCustomerAnalytics(req, res) {
    try {
      const days = parseInt(req.query.days) || 30;
      
      const orderModel = new Order();
      const userFavoriteModel = new UserFavorite();

      // Get customer metrics
      const customerQuery = `
        SELECT 
          COUNT(DISTINCT user_id) as total_customers,
          COUNT(DISTINCT CASE WHEN created_at >= NOW() - INTERVAL '${days} days' THEN user_id END) as new_customers,
          COUNT(DISTINCT CASE WHEN created_at >= NOW() - INTERVAL '${days} days' AND 
                (SELECT COUNT(*) FROM orders o2 WHERE o2.user_id = orders.user_id AND o2.created_at < NOW() - INTERVAL '${days} days') > 0 
                THEN user_id END) as returning_customers,
          AVG(total_amount) as avg_order_value,
          COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '${days} days') as total_orders
        FROM orders
        WHERE status = 'delivered'
      `;

      const customerResult = await orderModel.query(customerQuery);
      const customerMetrics = customerResult.rows[0];

      // Get top customers
      const topCustomersQuery = `
        SELECT 
          u.first_name,
          u.last_name,
          u.email,
          COUNT(o.id) as order_count,
          SUM(o.total_amount) as total_spent,
          AVG(o.total_amount) as avg_order_value
        FROM users u
        JOIN orders o ON u.id = o.user_id
        WHERE o.status = 'delivered'
        AND o.created_at >= NOW() - INTERVAL '${days} days'
        GROUP BY u.id, u.first_name, u.last_name, u.email
        ORDER BY total_spent DESC
        LIMIT 10
      `;

      const topCustomersResult = await orderModel.query(topCustomersQuery);

      const analytics = {
        metrics: {
          totalCustomers: parseInt(customerMetrics.total_customers),
          newCustomers: parseInt(customerMetrics.new_customers),
          returningCustomers: parseInt(customerMetrics.returning_customers),
          avgOrderValue: Math.round(parseFloat(customerMetrics.avg_order_value || 0) * 100) / 100,
          totalOrders: parseInt(customerMetrics.total_orders)
        },
        topCustomers: topCustomersResult.rows
      };

      return ResponseHelper.success(res, analytics, 'Customer analytics retrieved successfully');
    } catch (error) {
      console.error('Get customer analytics error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve customer analytics', 500);
    }
  }

  // Get revenue analytics (admin only)
  static async getRevenueAnalytics(req, res) {
    try {
      const days = parseInt(req.query.days) || 30;
      const groupBy = req.query.groupBy || 'day'; // day, week, month
      
      const orderModel = new Order();

      let dateFormat;
      switch (groupBy) {
        case 'week':
          dateFormat = 'YYYY-"W"WW';
          break;
        case 'month':
          dateFormat = 'YYYY-MM';
          break;
        default:
          dateFormat = 'YYYY-MM-DD';
      }

      const revenueQuery = `
        SELECT 
          TO_CHAR(created_at, '${dateFormat}') as period,
          COUNT(*) as order_count,
          SUM(total_amount) as revenue,
          AVG(total_amount) as avg_order_value,
          SUM(delivery_fee) as delivery_revenue,
          SUM(tip) as tip_revenue,
          SUM(tax) as tax_collected
        FROM orders
        WHERE status = 'delivered'
        AND created_at >= NOW() - INTERVAL '${days} days'
        GROUP BY TO_CHAR(created_at, '${dateFormat}')
        ORDER BY period DESC
      `;

      const result = await orderModel.query(revenueQuery);

      const analytics = result.rows.map(row => ({
        period: row.period,
        orderCount: parseInt(row.order_count),
        revenue: Math.round(parseFloat(row.revenue) * 100) / 100,
        avgOrderValue: Math.round(parseFloat(row.avg_order_value) * 100) / 100,
        deliveryRevenue: Math.round(parseFloat(row.delivery_revenue || 0) * 100) / 100,
        tipRevenue: Math.round(parseFloat(row.tip_revenue || 0) * 100) / 100,
        taxCollected: Math.round(parseFloat(row.tax_collected || 0) * 100) / 100
      }));

      return ResponseHelper.success(res, { analytics, groupBy }, 'Revenue analytics retrieved successfully');
    } catch (error) {
      console.error('Get revenue analytics error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve revenue analytics', 500);
    }
  }

  // Get promo code analytics (admin only)
  static async getPromoCodeAnalytics(req, res) {
    try {
      const days = parseInt(req.query.days) || 30;
      const promoCodeModel = new PromoCode();

      const analyticsQuery = `
        SELECT 
          pc.code,
          pc.name,
          pc.type,
          pc.value,
          pc.usage_count,
          pc.usage_limit,
          COALESCE(recent_usage.usage_count, 0) as recent_usage,
          COALESCE(recent_usage.total_discount, 0) as total_discount,
          COALESCE(recent_usage.avg_discount, 0) as avg_discount
        FROM promo_codes pc
        LEFT JOIN (
          SELECT 
            pcu.promo_code_id,
            COUNT(*) as usage_count,
            SUM(pcu.discount_amount) as total_discount,
            AVG(pcu.discount_amount) as avg_discount
          FROM promo_code_usage pcu
          WHERE pcu.created_at >= NOW() - INTERVAL '${days} days'
          GROUP BY pcu.promo_code_id
        ) recent_usage ON pc.id = recent_usage.promo_code_id
        ORDER BY recent_usage.usage_count DESC NULLS LAST, pc.usage_count DESC
      `;

      const result = await promoCodeModel.query(analyticsQuery);

      const analytics = result.rows.map(row => ({
        code: row.code,
        name: row.name,
        type: row.type,
        value: parseFloat(row.value),
        totalUsage: parseInt(row.usage_count),
        usageLimit: row.usage_limit,
        recentUsage: parseInt(row.recent_usage),
        totalDiscount: Math.round(parseFloat(row.total_discount || 0) * 100) / 100,
        avgDiscount: Math.round(parseFloat(row.avg_discount || 0) * 100) / 100
      }));

      return ResponseHelper.success(res, { analytics }, 'Promo code analytics retrieved successfully');
    } catch (error) {
      console.error('Get promo code analytics error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve promo code analytics', 500);
    }
  }

  // Export analytics data (admin only)
  static async exportAnalytics(req, res) {
    try {
      const { type, format = 'json', days = 30 } = req.query;
      
      let data;
      switch (type) {
        case 'orders':
          const orderModel = new Order();
          data = await orderModel.getOrderAnalytics(days);
          break;
        case 'revenue':
          data = await AnalyticsController.getRevenueData(days);
          break;
        case 'customers':
          data = await AnalyticsController.getCustomerData(days);
          break;
        default:
          return ResponseHelper.error(res, 'Invalid export type', 400);
      }

      if (format === 'csv') {
        // Convert to CSV format
        const csv = AnalyticsController.convertToCSV(data);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${type}_analytics.csv"`);
        return res.send(csv);
      }

      return ResponseHelper.success(res, { data, type, format }, 'Analytics data exported successfully');
    } catch (error) {
      console.error('Export analytics error:', error);
      return ResponseHelper.error(res, 'Failed to export analytics data', 500);
    }
  }

  // Helper method to convert data to CSV
  static convertToCSV(data) {
    if (!data || data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');
    
    return csvContent;
  }
}

module.exports = AnalyticsController;
