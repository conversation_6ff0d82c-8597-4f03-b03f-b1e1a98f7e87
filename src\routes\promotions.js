// Promotions Routes
const express = require('express');
const PromotionController = require('../controllers/promotionController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting to all routes
router.use(apiLimiter);

// Public routes (no authentication required)

// GET /api/v1/promotions - Get active promotions
router.get('/',
  PromotionController.getPromotions
);

// GET /api/v1/promotions/featured - Get featured promotions
router.get('/featured',
  PromotionController.getFeaturedPromotions
);

// GET /api/v1/promotions/:id - Get promotion by ID
router.get('/:id',
  ValidationMiddleware.validateUUID('id'),
  PromotionController.getPromotionById
);

// Protected routes (authentication required)

// POST /api/v1/promotions/validate - Validate promotion code
router.post('/validate',
  AuthMiddleware.authenticate,
  ValidationMiddleware.validatePromoCodeValidation(),
  PromotionController.validatePromotion
);

module.exports = router;
