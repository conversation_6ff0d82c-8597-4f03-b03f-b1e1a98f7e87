// Promotion Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const PromoCode = require('../models/PromoCode');

class PromotionController {
  // Get active promotions
  static async getPromotions(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);
      const promoCodeModel = new PromoCode();

      const result = await promoCodeModel.getActivePromoCodes(page, limit);

      // Format promotions for customer app
      const promotions = result.promoCodes.map(promo => ({
        id: promo.id,
        code: promo.code,
        name: promo.name,
        description: promo.description,
        type: promo.type,
        value: promo.value,
        minimumOrder: promo.minimum_order,
        maximumDiscount: promo.maximum_discount,
        expiresAt: promo.expires_at,
        usageLimit: promo.usage_limit,
        usageCount: promo.usage_count,
        isActive: promo.is_active
      }));

      return ResponseHelper.success(res, {
        promotions,
        pagination: result.pagination
      }, 'Promotions retrieved successfully');
    } catch (error) {
      console.error('Get promotions error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve promotions', 500);
    }
  }

  // Validate promotion code
  static async validatePromotion(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { code, orderAmount } = req.body;
      const userId = req.user ? req.user.id : null;
      const promoCodeModel = new PromoCode();

      if (!userId) {
        return ResponseHelper.error(res, 'Authentication required', 401);
      }

      const validation = await promoCodeModel.validatePromoCode(code, userId, orderAmount);

      if (!validation.isValid) {
        return ResponseHelper.error(res, validation.error, 400, {
          code: 'INVALID_PROMO_CODE',
          message: validation.error
        });
      }

      return ResponseHelper.success(res, {
        isValid: true,
        promotion: {
          id: validation.promoCode.id,
          code: validation.promoCode.code,
          name: validation.promoCode.name,
          type: validation.promoCode.type,
          value: validation.promoCode.value,
          discountAmount: validation.discountAmount,
          minimumOrder: validation.promoCode.minimum_order,
          maximumDiscount: validation.promoCode.maximum_discount
        }
      }, 'Promotion code is valid');
    } catch (error) {
      console.error('Validate promotion error:', error);
      return ResponseHelper.error(res, 'Failed to validate promotion code', 500);
    }
  }

  // Get promotion by ID
  static async getPromotionById(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const promotionId = req.params.id;
      const promoCodeModel = new PromoCode();

      const promotion = await promoCodeModel.findById(promotionId);

      if (!promotion || !promotion.is_active) {
        return ResponseHelper.error(res, 'Promotion not found', 404, {
          code: 'NOT_FOUND',
          message: 'Promotion not found or is not active'
        });
      }

      // Format promotion for customer app
      const formattedPromotion = {
        id: promotion.id,
        code: promotion.code,
        name: promotion.name,
        description: promotion.description,
        type: promotion.type,
        value: promotion.value,
        minimumOrder: promotion.minimum_order,
        maximumDiscount: promotion.maximum_discount,
        expiresAt: promotion.expires_at,
        usageLimit: promotion.usage_limit,
        usageCount: promotion.usage_count
      };

      return ResponseHelper.success(res, formattedPromotion, 'Promotion details retrieved successfully');
    } catch (error) {
      console.error('Get promotion by ID error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve promotion details', 500);
    }
  }

  // Get featured promotions
  static async getFeaturedPromotions(req, res) {
    try {
      const limit = Math.min(parseInt(req.query.limit) || 5, 20);
      const promoCodeModel = new PromoCode();

      // Get active promotions and treat them as featured
      const result = await promoCodeModel.getActivePromoCodes(1, limit);

      const featuredPromotions = result.promoCodes.map(promo => ({
        id: promo.id,
        code: promo.code,
        name: promo.name,
        description: promo.description,
        type: promo.type,
        value: promo.value,
        minimumOrder: promo.minimum_order,
        maximumDiscount: promo.maximum_discount,
        expiresAt: promo.expires_at
      }));

      return ResponseHelper.success(res, {
        promotions: featuredPromotions
      }, 'Featured promotions retrieved successfully');
    } catch (error) {
      console.error('Get featured promotions error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve featured promotions', 500);
    }
  }
}

module.exports = PromotionController;
