// Query Optimization Performance Test
// Tests the performance improvements from optimized queries and caching

const databaseManager = require('../src/config/database');
const redisManager = require('../src/config/redis');
const queryOptimizationService = require('../src/services/queryOptimizationService');
const cacheService = require('../src/services/cacheService');

class QueryOptimizationTester {
  constructor() {
    this.pool = null;
    this.testResults = {
      originalQueries: {},
      optimizedQueries: {},
      cachePerformance: {},
      improvements: {}
    };
  }

  async initialize() {
    try {
      // Initialize database connection
      await databaseManager.initialize();
      this.pool = databaseManager.getPool();
      
      // Initialize Redis connection
      await redisManager.initialize();
      
      // Initialize query optimization service
      queryOptimizationService.initialize();
      
      console.log('✅ Query optimization tester initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize tester:', error);
      return false;
    }
  }

  // Test user favorites query performance
  async testUserFavoritesPerformance() {
    console.log('\n🔍 Testing User Favorites Query Performance...');

    try {
      // Get a real user ID from the database
      const userResult = await this.pool.query('SELECT id FROM users LIMIT 1');
      if (userResult.rows.length === 0) {
        console.log('⚠️ No users found in database, skipping user favorites test');
        return;
      }
      const testUserId = userResult.rows[0].id;
      const iterations = 10;
      
      // Test original query approach (multiple separate queries)
      console.log('Testing original approach...');
      const originalStart = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        // Simulate original approach with separate queries
        const restaurantsQuery = `
          SELECT uf.*, r.* FROM user_favorites uf
          INNER JOIN restaurants r ON uf.item_id = r.id
          WHERE uf.user_id = $1 AND uf.item_type = 'restaurant'
          AND r.is_active = true
          LIMIT 50
        `;

        const menuItemsQuery = `
          SELECT uf.*, mi.*, r.name as restaurant_name FROM user_favorites uf
          INNER JOIN menu_items mi ON uf.item_id = mi.id
          INNER JOIN restaurants r ON mi.restaurant_id = r.id
          WHERE uf.user_id = $1 AND uf.item_type = 'menu_item'
          AND mi.is_available = true AND r.is_active = true
          LIMIT 50
        `;
        
        await Promise.all([
          this.pool.query(restaurantsQuery, [testUserId]),
          this.pool.query(menuItemsQuery, [testUserId])
        ]);
      }
      
      const originalTime = Date.now() - originalStart;
      this.testResults.originalQueries.userFavorites = originalTime / iterations;
      
      // Clear cache to ensure fair comparison
      await cacheService.clearUserCache(testUserId);
      
      // Test optimized query approach
      console.log('Testing optimized approach...');
      const optimizedStart = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        await queryOptimizationService.getUserFavorites(testUserId);
      }
      
      const optimizedTime = Date.now() - optimizedStart;
      this.testResults.optimizedQueries.userFavorites = optimizedTime / iterations;
      
      // Test cache performance
      console.log('Testing cache performance...');
      const cacheStart = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        await queryOptimizationService.getUserFavorites(testUserId);
      }
      
      const cacheTime = Date.now() - cacheStart;
      this.testResults.cachePerformance.userFavorites = cacheTime / iterations;
      
      // Calculate improvements
      const queryImprovement = ((originalTime - optimizedTime) / originalTime * 100).toFixed(1);
      const cacheImprovement = ((optimizedTime - cacheTime) / optimizedTime * 100).toFixed(1);
      
      this.testResults.improvements.userFavorites = {
        queryImprovement: `${queryImprovement}%`,
        cacheImprovement: `${cacheImprovement}%`
      };
      
      console.log(`✅ User Favorites - Original: ${originalTime/iterations}ms, Optimized: ${optimizedTime/iterations}ms, Cached: ${cacheTime/iterations}ms`);
      console.log(`📈 Query Improvement: ${queryImprovement}%, Cache Improvement: ${cacheImprovement}%`);
      
    } catch (error) {
      console.error('❌ User favorites test failed:', error);
    }
  }

  // Test order details query performance
  async testOrderDetailsPerformance() {
    console.log('\n🔍 Testing Order Details Query Performance...');

    try {
      // Get real IDs from the database
      const orderResult = await this.pool.query('SELECT id, user_id FROM orders LIMIT 1');
      if (orderResult.rows.length === 0) {
        console.log('⚠️ No orders found in database, skipping order details test');
        return;
      }
      const testOrderId = orderResult.rows[0].id;
      const testUserId = orderResult.rows[0].user_id;
      const iterations = 10;
      
      // Test original query approach
      console.log('Testing original approach...');
      const originalStart = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        // Simulate original approach with complex JOIN
        const originalQuery = `
          SELECT o.*, r.name as restaurant_name, r.cover_image_url,
                 json_agg(json_build_object(
                   'id', oi.id, 'menuItemId', oi.menu_item_id,
                   'menuItemName', mi.name, 'quantity', oi.quantity,
                   'unitPrice', oi.unit_price, 'totalPrice', oi.total_price
                 )) FILTER (WHERE oi.id IS NOT NULL) as items
          FROM orders o
          LEFT JOIN restaurants r ON o.restaurant_id = r.id
          LEFT JOIN order_items oi ON o.id = oi.order_id
          LEFT JOIN menu_items mi ON oi.menu_item_id = mi.id
          WHERE o.id = $1
          GROUP BY o.id, r.name, r.cover_image_url
        `;
        
        await this.pool.query(originalQuery, [testOrderId]);
      }
      
      const originalTime = Date.now() - originalStart;
      this.testResults.originalQueries.orderDetails = originalTime / iterations;
      
      // Clear cache
      await cacheService.del(cacheService.generateKey('order_details', testOrderId));
      
      // Test optimized query approach
      console.log('Testing optimized approach...');
      const optimizedStart = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        await queryOptimizationService.getOrderWithDetails(testOrderId, testUserId);
      }
      
      const optimizedTime = Date.now() - optimizedStart;
      this.testResults.optimizedQueries.orderDetails = optimizedTime / iterations;
      
      // Test cache performance
      console.log('Testing cache performance...');
      const cacheStart = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        await queryOptimizationService.getOrderWithDetails(testOrderId, testUserId);
      }
      
      const cacheTime = Date.now() - cacheStart;
      this.testResults.cachePerformance.orderDetails = cacheTime / iterations;
      
      // Calculate improvements
      const queryImprovement = ((originalTime - optimizedTime) / originalTime * 100).toFixed(1);
      const cacheImprovement = ((optimizedTime - cacheTime) / optimizedTime * 100).toFixed(1);
      
      this.testResults.improvements.orderDetails = {
        queryImprovement: `${queryImprovement}%`,
        cacheImprovement: `${cacheImprovement}%`
      };
      
      console.log(`✅ Order Details - Original: ${originalTime/iterations}ms, Optimized: ${optimizedTime/iterations}ms, Cached: ${cacheTime/iterations}ms`);
      console.log(`📈 Query Improvement: ${queryImprovement}%, Cache Improvement: ${cacheImprovement}%`);
      
    } catch (error) {
      console.error('❌ Order details test failed:', error);
    }
  }

  // Test restaurant with menu query performance
  async testRestaurantMenuPerformance() {
    console.log('\n🔍 Testing Restaurant Menu Query Performance...');

    try {
      // Get a real restaurant ID from the database
      const restaurantResult = await this.pool.query('SELECT id FROM restaurants WHERE is_active = true LIMIT 1');
      if (restaurantResult.rows.length === 0) {
        console.log('⚠️ No active restaurants found in database, skipping restaurant menu test');
        return;
      }
      const testRestaurantId = restaurantResult.rows[0].id;
      const iterations = 10;
      
      // Test original query approach (multiple queries)
      console.log('Testing original approach...');
      const originalStart = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        // Simulate original approach with separate queries
        const restaurantQuery = `SELECT * FROM restaurants WHERE id = $1 AND is_active = true`;
        const menuQuery = `
          SELECT mi.*, mc.name as category_name, mc.sort_order as category_order
          FROM menu_items mi
          INNER JOIN menu_categories mc ON mi.category_id = mc.id
          WHERE mi.restaurant_id = $1 AND mi.is_available = true
          ORDER BY mc.sort_order, mi.sort_order
        `;
        
        await Promise.all([
          this.pool.query(restaurantQuery, [testRestaurantId]),
          this.pool.query(menuQuery, [testRestaurantId])
        ]);
      }
      
      const originalTime = Date.now() - originalStart;
      this.testResults.originalQueries.restaurantMenu = originalTime / iterations;
      
      // Clear cache
      await cacheService.clearRestaurantCache(testRestaurantId);
      await cacheService.clearMenuCache(testRestaurantId);
      
      // Test optimized query approach
      console.log('Testing optimized approach...');
      const optimizedStart = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        await queryOptimizationService.getRestaurantWithMenu(testRestaurantId);
      }
      
      const optimizedTime = Date.now() - optimizedStart;
      this.testResults.optimizedQueries.restaurantMenu = optimizedTime / iterations;
      
      // Test cache performance
      console.log('Testing cache performance...');
      const cacheStart = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        await queryOptimizationService.getRestaurantWithMenu(testRestaurantId);
      }
      
      const cacheTime = Date.now() - cacheStart;
      this.testResults.cachePerformance.restaurantMenu = cacheTime / iterations;
      
      // Calculate improvements
      const queryImprovement = ((originalTime - optimizedTime) / originalTime * 100).toFixed(1);
      const cacheImprovement = ((optimizedTime - cacheTime) / optimizedTime * 100).toFixed(1);
      
      this.testResults.improvements.restaurantMenu = {
        queryImprovement: `${queryImprovement}%`,
        cacheImprovement: `${cacheImprovement}%`
      };
      
      console.log(`✅ Restaurant Menu - Original: ${originalTime/iterations}ms, Optimized: ${optimizedTime/iterations}ms, Cached: ${cacheTime/iterations}ms`);
      console.log(`📈 Query Improvement: ${queryImprovement}%, Cache Improvement: ${cacheImprovement}%`);
      
    } catch (error) {
      console.error('❌ Restaurant menu test failed:', error);
    }
  }

  // Test concurrent query performance
  async testConcurrentQueryPerformance() {
    console.log('\n🔍 Testing Concurrent Query Performance...');

    try {
      const concurrentRequests = 20;

      // Get real IDs from the database
      const [userResult, restaurantResult, orderResult] = await Promise.all([
        this.pool.query('SELECT id FROM users LIMIT 1'),
        this.pool.query('SELECT id FROM restaurants WHERE is_active = true LIMIT 1'),
        this.pool.query('SELECT id, user_id FROM orders LIMIT 1')
      ]);

      if (userResult.rows.length === 0 || restaurantResult.rows.length === 0 || orderResult.rows.length === 0) {
        console.log('⚠️ Missing test data in database, skipping concurrent test');
        return;
      }

      const testUserId = userResult.rows[0].id;
      const testRestaurantId = restaurantResult.rows[0].id;
      const testOrderId = orderResult.rows[0].id;
      
      // Test concurrent optimized queries
      console.log('Testing concurrent optimized queries...');
      const concurrentStart = Date.now();
      
      const promises = [];
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          queryOptimizationService.getUserFavorites(testUserId),
          queryOptimizationService.getRestaurantWithMenu(testRestaurantId),
          queryOptimizationService.getOrderWithDetails(testOrderId, testUserId)
        );
      }
      
      await Promise.all(promises);
      
      const concurrentTime = Date.now() - concurrentStart;
      const avgConcurrentTime = concurrentTime / (concurrentRequests * 3);
      
      this.testResults.cachePerformance.concurrent = avgConcurrentTime;
      
      console.log(`✅ Concurrent Queries - ${concurrentRequests * 3} queries in ${concurrentTime}ms (avg: ${avgConcurrentTime.toFixed(2)}ms per query)`);
      
    } catch (error) {
      console.error('❌ Concurrent query test failed:', error);
    }
  }

  // Generate comprehensive performance report
  generateReport() {
    console.log('\n📊 QUERY OPTIMIZATION PERFORMANCE REPORT');
    console.log('=' .repeat(60));
    
    console.log('\n🔍 Query Performance Comparison:');
    Object.keys(this.testResults.originalQueries).forEach(queryType => {
      const original = this.testResults.originalQueries[queryType];
      const optimized = this.testResults.optimizedQueries[queryType];
      const cached = this.testResults.cachePerformance[queryType];
      const improvements = this.testResults.improvements[queryType];
      
      console.log(`\n${queryType.toUpperCase()}:`);
      console.log(`  Original Query: ${original.toFixed(2)}ms`);
      console.log(`  Optimized Query: ${optimized.toFixed(2)}ms`);
      console.log(`  Cached Query: ${cached.toFixed(2)}ms`);
      console.log(`  Query Improvement: ${improvements.queryImprovement}`);
      console.log(`  Cache Improvement: ${improvements.cacheImprovement}`);
    });
    
    if (this.testResults.cachePerformance.concurrent) {
      console.log(`\n🚀 CONCURRENT PERFORMANCE:`);
      console.log(`  Average per query: ${this.testResults.cachePerformance.concurrent.toFixed(2)}ms`);
    }
    
    console.log('\n✅ Query optimization testing completed successfully!');
  }

  async cleanup() {
    try {
      await databaseManager.close();
      await redisManager.close();
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('❌ Cleanup failed:', error);
    }
  }
}

// Run the performance tests
async function runQueryOptimizationTests() {
  const tester = new QueryOptimizationTester();
  
  try {
    const initialized = await tester.initialize();
    if (!initialized) {
      console.error('❌ Failed to initialize tester');
      process.exit(1);
    }
    
    // Run all performance tests
    await tester.testUserFavoritesPerformance();
    await tester.testOrderDetailsPerformance();
    await tester.testRestaurantMenuPerformance();
    await tester.testConcurrentQueryPerformance();
    
    // Generate comprehensive report
    tester.generateReport();
    
  } catch (error) {
    console.error('❌ Query optimization testing failed:', error);
  } finally {
    await tester.cleanup();
    process.exit(0);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runQueryOptimizationTests();
}

module.exports = QueryOptimizationTester;
