// Validation Middleware
const { body, param, query } = require('express-validator');

class ValidationMiddleware {
  // User registration validation
  static validateRegistration() {
    return [
      body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),

      body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),

      body('firstName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('First name can only contain letters and spaces'),

      body('lastName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('Last name can only contain letters and spaces'),

      body('phone')
        .optional()
        .matches(/^\+?[1-9]\d{1,14}$/)
        .withMessage('Please provide a valid phone number')
    ];
  }

  // User login validation
  static validateLogin() {
    return [
      body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),

      body('password')
        .notEmpty()
        .withMessage('Password is required')
    ];
  }

  // Refresh token validation
  static validateRefreshToken() {
    return [
      body('refreshToken')
        .notEmpty()
        .withMessage('Refresh token is required')
    ];
  }

  // Forgot password validation
  static validateForgotPassword() {
    return [
      body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address')
    ];
  }

  // Reset password validation
  static validateResetPassword() {
    return [
      body('token')
        .notEmpty()
        .withMessage('Reset token is required'),

      body('newPassword')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number')
    ];
  }

  // Email verification validation
  static validateEmailVerification() {
    return [
      body('token')
        .notEmpty()
        .withMessage('Verification token is required')
    ];
  }

  // Profile update validation
  static validateProfileUpdate() {
    return [
      body('firstName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('First name can only contain letters and spaces'),

      body('lastName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('Last name can only contain letters and spaces'),

      body('phone')
        .optional()
        .matches(/^\+?[1-9]\d{1,14}$/)
        .withMessage('Please provide a valid phone number')
    ];
  }

  // Address validation for creation (flexible - accepts multiple field name formats)
  static validateAddress() {
    return [
      body('type')
        .optional()
        .isIn(['home', 'work', 'other'])
        .withMessage('Address type must be home, work, or other'),

      body('label')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Label must be less than 100 characters'),

      // Accept both streetAddress (old format) and street (new format)
      body(['streetAddress', 'street'])
        .custom((value, { req }) => {
          const streetAddress = req.body.streetAddress || req.body.street;
          if (!streetAddress || streetAddress.trim().length === 0) {
            throw new Error('Street address is required');
          }
          if (streetAddress.trim().length > 255) {
            throw new Error('Street address must be less than 255 characters');
          }
          return true;
        }),

      body('apartment')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('Apartment must be less than 50 characters'),

      body('city')
        .trim()
        .notEmpty()
        .withMessage('City is required')
        .isLength({ max: 100 })
        .withMessage('City must be less than 100 characters'),

      body('state')
        .trim()
        .notEmpty()
        .withMessage('State is required')
        .isLength({ max: 100 })
        .withMessage('State must be less than 100 characters'),

      // Accept postalCode, zipCode, or zip_code
      body(['postalCode', 'zipCode', 'zip_code'])
        .custom((value, { req }) => {
          const zipCode = req.body.postalCode || req.body.zipCode || req.body.zip_code;
          if (!zipCode || zipCode.trim().length === 0) {
            throw new Error('ZIP code is required');
          }
          if (!/^\d{5}(-\d{4})?$/.test(zipCode.trim())) {
            throw new Error('ZIP code must be in format 12345 or 12345-6789');
          }
          return true;
        }),

      body('country')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Country must be between 2 and 50 characters'),

      body('latitude')
        .optional()
        .isFloat({ min: -90, max: 90 })
        .withMessage('Latitude must be between -90 and 90'),

      body('longitude')
        .optional()
        .isFloat({ min: -180, max: 180 })
        .withMessage('Longitude must be between -180 and 180'),

      // Accept both isDefault and is_default
      body(['isDefault', 'is_default'])
        .optional()
        .isBoolean()
        .withMessage('isDefault must be a boolean value'),

      body(['deliveryInstructions', 'delivery_instructions'])
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Delivery instructions must be less than 500 characters')
    ];
  }

  // Address validation for updates (all fields optional)
  static validateAddressUpdate() {
    return [
      body('type')
        .optional()
        .isIn(['home', 'work', 'other'])
        .withMessage('Address type must be home, work, or other'),

      body('label')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Label must be less than 100 characters'),

      body('streetAddress')
        .optional()
        .trim()
        .notEmpty()
        .withMessage('Street address cannot be empty')
        .isLength({ max: 255 })
        .withMessage('Street address must be less than 255 characters'),

      body('city')
        .optional()
        .trim()
        .notEmpty()
        .withMessage('City cannot be empty')
        .isLength({ max: 100 })
        .withMessage('City must be less than 100 characters'),

      body('state')
        .optional()
        .trim()
        .notEmpty()
        .withMessage('State cannot be empty')
        .isLength({ max: 100 })
        .withMessage('State must be less than 100 characters'),

      body('postalCode')
        .optional()
        .trim()
        .notEmpty()
        .withMessage('Postal code cannot be empty')
        .matches(/^\d{5}(-\d{4})?$/)
        .withMessage('Please provide a valid postal code'),

      body('country')
        .optional()
        .isLength({ min: 2, max: 2 })
        .withMessage('Country must be a 2-letter country code'),

      body('latitude')
        .optional()
        .isFloat({ min: -90, max: 90 })
        .withMessage('Latitude must be between -90 and 90'),

      body('longitude')
        .optional()
        .isFloat({ min: -180, max: 180 })
        .withMessage('Longitude must be between -180 and 180'),

      body('isDefault')
        .optional()
        .isBoolean()
        .withMessage('isDefault must be a boolean value')
    ];
  }

  // UUID parameter validation
  static validateUUID(paramName = 'id') {
    return [
      param(paramName)
        .isUUID()
        .withMessage(`${paramName} must be a valid UUID`)
    ];
  }

  // Pagination validation
  static validatePagination() {
    return [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),

      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100')
    ];
  }

  // Search validation
  static validateSearch() {
    return [
      query('q')
        .optional()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Search query must be between 1 and 100 characters'),

      query('latitude')
        .optional()
        .isFloat({ min: -90, max: 90 })
        .withMessage('Latitude must be between -90 and 90'),

      query('longitude')
        .optional()
        .isFloat({ min: -180, max: 180 })
        .withMessage('Longitude must be between -180 and 180'),

      query('radius')
        .optional()
        .isInt({ min: 100, max: 50000 })
        .withMessage('Radius must be between 100 and 50000 meters')
    ];
  }

  // Order validation
  static validateOrder() {
    return [
      body('restaurantId')
        .isUUID()
        .withMessage('Restaurant ID must be a valid UUID'),

      body('items')
        .isArray({ min: 1 })
        .withMessage('Order must contain at least one item'),

      body('items.*.menuItemId')
        .isUUID()
        .withMessage('Menu item ID must be a valid UUID'),

      body('items.*.quantity')
        .isInt({ min: 1, max: 10 })
        .withMessage('Quantity must be between 1 and 10'),

      body('deliveryAddress')
        .isObject()
        .withMessage('Delivery address is required'),

      body('deliveryAddress.streetAddress')
        .notEmpty()
        .withMessage('Street address is required'),

      body('deliveryAddress.city')
        .notEmpty()
        .withMessage('City is required'),

      body('deliveryAddress.state')
        .notEmpty()
        .withMessage('State is required'),

      body('deliveryAddress.postalCode')
        .matches(/^\d{5}(-\d{4})?$/)
        .withMessage('Please provide a valid postal code'),

      body('paymentMethodId')
        .optional()
        .isUUID()
        .withMessage('Payment method ID must be a valid UUID'),

      body('tipAmount')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Tip amount must be a positive number')
    ];
  }

  // Review validation
  static validateReview() {
    return [
      body('restaurantId')
        .isUUID()
        .withMessage('Restaurant ID must be a valid UUID'),

      body('orderId')
        .optional()
        .isUUID()
        .withMessage('Order ID must be a valid UUID'),

      body('rating')
        .isInt({ min: 1, max: 5 })
        .withMessage('Rating must be between 1 and 5'),

      body('comment')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Comment must be less than 1000 characters'),

      body('images')
        .optional()
        .isArray({ max: 5 })
        .withMessage('Maximum 5 images allowed')
    ];
  }

  // Boolean validation
  static validateBoolean(fieldName) {
    return body(fieldName)
      .optional()
      .isBoolean()
      .withMessage(`${fieldName} must be a boolean value`);
  }

  // Payment method validation
  static validatePaymentMethod() {
    return [
      body('stripePaymentMethodId')
        .notEmpty()
        .withMessage('Stripe payment method ID is required'),

      body('isDefault')
        .optional()
        .isBoolean()
        .withMessage('isDefault must be a boolean value')
    ];
  }

  // Restaurant query validation
  static validateRestaurantQuery() {
    return [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),

      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),

      query('latitude')
        .optional()
        .isFloat({ min: -90, max: 90 })
        .withMessage('Latitude must be between -90 and 90'),

      query('longitude')
        .optional()
        .isFloat({ min: -180, max: 180 })
        .withMessage('Longitude must be between -180 and 180'),

      query('radius')
        .optional()
        .isInt({ min: 100, max: 50000 })
        .withMessage('Radius must be between 100 and 50000 meters'),

      query('rating')
        .optional()
        .isFloat({ min: 0, max: 5 })
        .withMessage('Rating must be between 0 and 5'),

      query('priceRange')
        .optional()
        .isInt({ min: 1, max: 4 })
        .withMessage('Price range must be between 1 and 4'),

      query('deliveryFee')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Delivery fee must be a positive number'),

      query('sortBy')
        .optional()
        .isIn(['distance', 'rating', 'deliveryTime', 'popularity'])
        .withMessage('Sort by must be one of: distance, rating, deliveryTime, popularity'),

      query('featured')
        .optional()
        .isBoolean()
        .withMessage('Featured must be a boolean value'),

      query('search')
        .optional()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Search query must be between 1 and 100 characters')
    ];
  }

  // Nearby restaurants validation
  static validateNearbyRestaurants() {
    return [
      query('latitude')
        .notEmpty()
        .isFloat({ min: -90, max: 90 })
        .withMessage('Valid latitude is required'),

      query('longitude')
        .notEmpty()
        .isFloat({ min: -180, max: 180 })
        .withMessage('Valid longitude is required'),

      query('radius')
        .optional()
        .isInt({ min: 100, max: 50000 })
        .withMessage('Radius must be between 100 and 50000 meters'),

      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100')
    ];
  }

  // Menu items query validation
  static validateMenuItemsQuery() {
    return [
      query('categoryId')
        .optional()
        .isUUID()
        .withMessage('Category ID must be a valid UUID'),

      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),

      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),

      query('available')
        .optional()
        .isBoolean()
        .withMessage('Available must be a boolean value')
    ];
  }

  // Menu search validation
  static validateMenuSearch() {
    return [
      query('q')
        .notEmpty()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Search query is required and must be between 1 and 100 characters'),

      query('categoryId')
        .optional()
        .isUUID()
        .withMessage('Category ID must be a valid UUID'),

      query('minPrice')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Minimum price must be a positive number'),

      query('maxPrice')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Maximum price must be a positive number'),

      query('dietary')
        .optional()
        .custom((value) => {
          const validDietary = ['vegetarian', 'vegan', 'gluten_free'];
          if (Array.isArray(value)) {
            return value.every(item => validDietary.includes(item));
          }
          return validDietary.includes(value);
        })
        .withMessage('Dietary restrictions must be one of: vegetarian, vegan, gluten_free')
    ];
  }

  // Create order validation (flexible format support)
  static validateCreateOrder() {
    return [
      // Restaurant ID validation - handle both formats
      body(['restaurantId', 'restaurant_id'])
        .custom((value, { req }) => {
          const restaurantId = req.body.restaurantId || req.body.restaurant_id;
          if (!restaurantId) {
            throw new Error('Restaurant ID is required');
          }
          // Basic UUID format check (more lenient)
          if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(restaurantId)) {
            throw new Error('Restaurant ID must be a valid UUID');
          }
          return true;
        }),

      // Items validation - handle multiple formats
      body()
        .custom((value) => {
          let items = [];

          // Frontend format: { items: [...] }
          if (value.items && Array.isArray(value.items)) {
            items = value.items;
          }
          // Backend format: { orderItems: [...] }
          else if (value.orderItems && Array.isArray(value.orderItems)) {
            items = value.orderItems;
          }
          // Alternative format: { menuItems: [...] }
          else if (value.menuItems && Array.isArray(value.menuItems)) {
            items = value.menuItems;
          }
          else {
            throw new Error('Order must contain items array (items, orderItems, or menuItems)');
          }

          if (items.length === 0) {
            throw new Error('Order must contain at least one item');
          }

          // Validate each item
          items.forEach((item, index) => {
            // Menu item ID validation - handle different field names
            const menuItemId = item.menuItemId || item.menu_item_id || item.id;
            if (!menuItemId) {
              throw new Error(`Item ${index + 1}: Menu item ID is required`);
            }
            // More lenient UUID check
            if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(menuItemId)) {
              throw new Error(`Item ${index + 1}: Menu item ID must be a valid UUID`);
            }

            // Quantity validation
            const quantity = item.quantity || 1;
            if (!Number.isInteger(quantity) || quantity < 1 || quantity > 10) {
              throw new Error(`Item ${index + 1}: Quantity must be between 1 and 10`);
            }

            // Price validation (optional for some formats)
            if (item.price !== undefined) {
              const price = parseFloat(item.price);
              if (isNaN(price) || price <= 0) {
                throw new Error(`Item ${index + 1}: Price must be a positive number`);
              }
            }
          });

          return true;
        }),

      // Delivery address validation (flexible)
      body(['deliveryAddress', 'deliveryAddressId', 'delivery_address'])
        .optional()
        .custom((value) => {
          if (!value) return true; // Optional field

          if (typeof value === 'string') {
            // Address ID format - more lenient UUID check
            if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
              throw new Error('Delivery address ID must be a valid UUID');
            }
            return true;
          }

          if (typeof value === 'object' && value !== null) {
            // Full address object - basic validation
            const street = value.street || value.streetAddress;
            if (!street || street.trim().length === 0) {
              throw new Error('Delivery address must include street address');
            }
            if (!value.city || value.city.trim().length === 0) {
              throw new Error('Delivery address must include city');
            }
            return true;
          }

          throw new Error('Delivery address must be either an address ID or address object');
        }),

      // Payment method validation (optional)
      body(['paymentMethodId', 'paymentMethod', 'payment_method'])
        .optional()
        .custom((value) => {
          if (typeof value === 'string') {
            // Could be UUID or payment type
            if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
              return true; // Valid UUID
            }
            if (['card', 'cash', 'digital_wallet', 'stripe'].includes(value)) {
              return true; // Valid payment type
            }
            throw new Error('Payment method must be a valid UUID or payment type (card, cash, digital_wallet, stripe)');
          }
          return true;
        }),

      body('tip')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Tip must be a positive number'),

      body(['specialInstructions', 'special_instructions', 'notes'])
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Special instructions must be less than 500 characters')
    ];
  }

  // Orders query validation
  static validateOrdersQuery() {
    return [
      query('status')
        .optional()
        .isIn(['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery', 'delivered', 'cancelled'])
        .withMessage('Invalid order status'),

      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),

      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),

      query('startDate')
        .optional()
        .isISO8601()
        .withMessage('Start date must be a valid ISO 8601 date'),

      query('endDate')
        .optional()
        .isISO8601()
        .withMessage('End date must be a valid ISO 8601 date')
    ];
  }

  // Cancel order validation
  static validateCancelOrder() {
    return [
      body('reason')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Cancellation reason must be less than 500 characters')
    ];
  }

  // Favorites query validation
  static validateFavoritesQuery() {
    return [
      query('type')
        .optional()
        .isIn(['restaurants', 'menu_items'])
        .withMessage('Type must be either "restaurants" or "menu_items"')
    ];
  }

  // Add favorite validation
  static validateAddFavorite() {
    return [
      body('type')
        .isIn(['restaurant', 'menu_item'])
        .withMessage('Type must be either "restaurant" or "menu_item"'),

      body('itemId')
        .isUUID()
        .withMessage('Item ID must be a valid UUID')
    ];
  }

  // Create payment intent validation
  static validateCreatePaymentIntent() {
    return [
      body('orderId')
        .isUUID()
        .withMessage('Order ID must be a valid UUID'),

      body('amount')
        .isFloat({ min: 0.01 })
        .withMessage('Amount must be a positive number')
    ];
  }

  // Confirm payment validation
  static validateConfirmPayment() {
    return [
      body('paymentIntentId')
        .notEmpty()
        .trim()
        .withMessage('Payment intent ID is required')
    ];
  }

  // Process refund validation
  static validateProcessRefund() {
    return [
      body('orderId')
        .isUUID()
        .withMessage('Order ID must be a valid UUID'),

      body('amount')
        .optional()
        .isFloat({ min: 0.01 })
        .withMessage('Amount must be a positive number'),

      body('reason')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Reason must be less than 500 characters')
    ];
  }

  // Address validation
  static validateAddressValidation() {
    return [
      body('street')
        .notEmpty()
        .trim()
        .isLength({ min: 5, max: 200 })
        .withMessage('Street address is required and must be between 5 and 200 characters'),

      body('city')
        .notEmpty()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('City is required and must be between 2 and 100 characters'),

      body('state')
        .notEmpty()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('State is required and must be between 2 and 50 characters'),

      body('zipCode')
        .notEmpty()
        .trim()
        .matches(/^\d{5}(-\d{4})?$/)
        .withMessage('ZIP code must be in format 12345 or 12345-6789')
    ];
  }

  // Multiple addresses validation
  static validateMultipleAddresses() {
    return [
      body('addresses')
        .isArray({ min: 1, max: 10 })
        .withMessage('Addresses must be an array with 1-10 items'),

      body('addresses.*.street')
        .notEmpty()
        .trim()
        .isLength({ min: 5, max: 200 })
        .withMessage('Each street address is required and must be between 5 and 200 characters'),

      body('addresses.*.city')
        .notEmpty()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Each city is required and must be between 2 and 100 characters'),

      body('addresses.*.state')
        .notEmpty()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Each state is required and must be between 2 and 50 characters'),

      body('addresses.*.zipCode')
        .notEmpty()
        .trim()
        .matches(/^\d{5}(-\d{4})?$/)
        .withMessage('Each ZIP code must be in format 12345 or 12345-6789')
    ];
  }

  // Cuisine type validation
  static validateCuisineType() {
    return [
      param('cuisine')
        .notEmpty()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Cuisine type must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s_-]+$/)
        .withMessage('Cuisine type can only contain letters, spaces, hyphens, and underscores')
    ];
  }

  // Slug validation
  static validateSlug() {
    return [
      param('slug')
        .notEmpty()
        .trim()
        .isLength({ min: 3, max: 100 })
        .withMessage('Slug must be between 3 and 100 characters')
        .matches(/^[a-z0-9-]+$/)
        .withMessage('Slug can only contain lowercase letters, numbers, and hyphens')
    ];
  }

  // Review query validation
  static validateReviewQuery() {
    return [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),

      query('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('Limit must be between 1 and 50'),

      query('rating')
        .optional()
        .isInt({ min: 1, max: 5 })
        .withMessage('Rating filter must be between 1 and 5'),

      query('sortBy')
        .optional()
        .isIn(['created_at', 'rating', 'helpful_count'])
        .withMessage('Sort by must be one of: created_at, rating, helpful_count'),

      query('sortOrder')
        .optional()
        .isIn(['ASC', 'DESC'])
        .withMessage('Sort order must be ASC or DESC')
    ];
  }

  // Accepting orders update validation
  static validateAcceptingOrdersUpdate() {
    return [
      body('acceptingOrders')
        .isBoolean()
        .withMessage('acceptingOrders must be a boolean value')
    ];
  }

  // UUID parameter validation
  static validateUUID(paramName = 'id') {
    return [
      param(paramName)
        .isUUID()
        .withMessage(`${paramName} must be a valid UUID`)
    ];
  }

  // Menu item availability update validation
  static validateItemAvailabilityUpdate() {
    return [
      body('isAvailable')
        .isBoolean()
        .withMessage('isAvailable must be a boolean value')
    ];
  }

  // Menu item price update validation
  static validateItemPriceUpdate() {
    return [
      body('price')
        .isFloat({ min: 0 })
        .withMessage('Price must be a positive number')
    ];
  }

  // Create category validation
  static validateCreateCategory() {
    return [
      body('restaurant_id')
        .isUUID()
        .withMessage('Restaurant ID must be a valid UUID'),

      body('name')
        .trim()
        .isLength({ min: 1, max: 255 })
        .withMessage('Category name must be between 1 and 255 characters'),

      body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Description must be less than 1000 characters'),

      body('image')
        .optional()
        .isURL()
        .withMessage('Image must be a valid URL'),

      body('sort_order')
        .optional()
        .isInt({ min: 0 })
        .withMessage('Sort order must be a non-negative integer')
    ];
  }

  // Update category validation
  static validateUpdateCategory() {
    return [
      body('name')
        .optional()
        .trim()
        .isLength({ min: 1, max: 255 })
        .withMessage('Category name must be between 1 and 255 characters'),

      body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Description must be less than 1000 characters'),

      body('image')
        .optional()
        .isURL()
        .withMessage('Image must be a valid URL'),

      body('sort_order')
        .optional()
        .isInt({ min: 0 })
        .withMessage('Sort order must be a non-negative integer'),

      body('is_active')
        .optional()
        .isBoolean()
        .withMessage('is_active must be a boolean value')
    ];
  }

  // Delete category validation
  static validateDeleteCategory() {
    return [
      body('newCategoryId')
        .optional()
        .isUUID()
        .withMessage('New category ID must be a valid UUID')
    ];
  }

  // Reorder categories validation
  static validateReorderCategories() {
    return [
      body('categoryOrders')
        .isArray({ min: 1 })
        .withMessage('Category orders must be a non-empty array'),

      body('categoryOrders.*.categoryId')
        .isUUID()
        .withMessage('Each category ID must be a valid UUID'),

      body('categoryOrders.*.sortOrder')
        .isInt({ min: 0 })
        .withMessage('Each sort order must be a non-negative integer')
    ];
  }

  // Update order status validation
  static validateUpdateOrderStatus() {
    return [
      body('status')
        .isIn(['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery', 'delivered', 'cancelled'])
        .withMessage('Status must be one of: pending, confirmed, preparing, ready, out_for_delivery, delivered, cancelled'),

      body('note')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Note must be less than 500 characters')
    ];
  }

  // Order status parameter validation
  static validateOrderStatus() {
    return [
      param('status')
        .isIn(['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery', 'delivered', 'cancelled'])
        .withMessage('Status must be one of: pending, confirmed, preparing, ready, out_for_delivery, delivered, cancelled')
    ];
  }

  // Mark delivered validation
  static validateMarkDelivered() {
    return [
      body('actualDeliveryTime')
        .optional()
        .isISO8601()
        .withMessage('Actual delivery time must be a valid ISO 8601 date')
    ];
  }

  // Create address validation
  static validateCreateAddress() {
    return [
      body('label')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Label must be between 1 and 50 characters'),

      body('street')
        .trim()
        .isLength({ min: 1, max: 255 })
        .withMessage('Street address is required and must be less than 255 characters'),

      body('apartment')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('Apartment must be less than 50 characters'),

      body('city')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('City is required and must be less than 100 characters'),

      body('state')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('State is required and must be between 2 and 50 characters'),

      // Accept both zipCode (frontend) and zip_code (backend)
      body(['zipCode', 'zip_code'])
        .trim()
        .matches(/^\d{5}(-\d{4})?$/)
        .withMessage('ZIP code must be in format 12345 or 12345-6789'),

      body('country')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Country must be between 2 and 50 characters'),

      body('latitude')
        .optional()
        .isFloat({ min: -90, max: 90 })
        .withMessage('Latitude must be between -90 and 90'),

      body('longitude')
        .optional()
        .isFloat({ min: -180, max: 180 })
        .withMessage('Longitude must be between -180 and 180'),

      body(['deliveryInstructions', 'delivery_instructions'])
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Delivery instructions must be less than 500 characters'),

      body(['isDefault', 'is_default'])
        .optional()
        .isBoolean()
        .withMessage('isDefault must be a boolean value')
    ];
  }

  // Update address validation
  static validateUpdateAddress() {
    return [
      body('label')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Label must be between 1 and 50 characters'),

      body('street')
        .optional()
        .trim()
        .isLength({ min: 1, max: 255 })
        .withMessage('Street address must be less than 255 characters'),

      body('apartment')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('Apartment must be less than 50 characters'),

      body('city')
        .optional()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('City must be less than 100 characters'),

      body('state')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('State must be between 2 and 50 characters'),

      body('zip_code')
        .optional()
        .trim()
        .matches(/^\d{5}(-\d{4})?$/)
        .withMessage('ZIP code must be in format 12345 or 12345-6789'),

      body('country')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Country must be between 2 and 50 characters'),

      body('latitude')
        .optional()
        .isFloat({ min: -90, max: 90 })
        .withMessage('Latitude must be between -90 and 90'),

      body('longitude')
        .optional()
        .isFloat({ min: -180, max: 180 })
        .withMessage('Longitude must be between -180 and 180'),

      body('delivery_instructions')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Delivery instructions must be less than 500 characters'),

      body('is_default')
        .optional()
        .isBoolean()
        .withMessage('is_default must be a boolean value')
    ];
  }

  // Delivery address validation
  static validateDeliveryAddress() {
    return [
      body('street')
        .trim()
        .isLength({ min: 1, max: 255 })
        .withMessage('Street address is required'),

      body('city')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('City is required'),

      body('state')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('State is required'),

      body('zip_code')
        .trim()
        .matches(/^\d{5}(-\d{4})?$/)
        .withMessage('Valid ZIP code is required'),

      body('latitude')
        .optional()
        .isFloat({ min: -90, max: 90 })
        .withMessage('Invalid latitude'),

      body('longitude')
        .optional()
        .isFloat({ min: -180, max: 180 })
        .withMessage('Invalid longitude')
    ];
  }

  // Create payment method validation
  static validateCreatePaymentMethod() {
    return [
      body('type')
        .isIn(['card', 'paypal', 'apple_pay', 'google_pay'])
        .withMessage('Type must be one of: card, paypal, apple_pay, google_pay'),

      body('provider')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Provider must be less than 50 characters'),

      body('external_id')
        .trim()
        .isLength({ min: 1, max: 255 })
        .withMessage('External ID is required'),

      body('last_four')
        .optional()
        .matches(/^\d{4}$/)
        .withMessage('Last four digits must be exactly 4 digits'),

      body('brand')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('Brand must be less than 50 characters'),

      body('expiry_month')
        .optional()
        .isInt({ min: 1, max: 12 })
        .withMessage('Expiry month must be between 1 and 12'),

      body('expiry_year')
        .optional()
        .isInt({ min: new Date().getFullYear(), max: new Date().getFullYear() + 20 })
        .withMessage('Expiry year must be valid'),

      body('cardholder_name')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Cardholder name must be less than 100 characters'),

      body('is_default')
        .optional()
        .isBoolean()
        .withMessage('is_default must be a boolean value')
    ];
  }

  // Update payment method validation
  static validateUpdatePaymentMethod() {
    return [
      body('last_four')
        .optional()
        .matches(/^\d{4}$/)
        .withMessage('Last four digits must be exactly 4 digits'),

      body('brand')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('Brand must be less than 50 characters'),

      body('expiry_month')
        .optional()
        .isInt({ min: 1, max: 12 })
        .withMessage('Expiry month must be between 1 and 12'),

      body('expiry_year')
        .optional()
        .isInt({ min: new Date().getFullYear(), max: new Date().getFullYear() + 20 })
        .withMessage('Expiry year must be valid'),

      body('cardholder_name')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Cardholder name must be less than 100 characters'),

      body('is_default')
        .optional()
        .isBoolean()
        .withMessage('is_default must be a boolean value')
    ];
  }

  // Create review validation (flexible format support)
  static validateCreateReview() {
    return [
      // Handle both frontend format (restaurantId) and backend format (restaurant_id)
      body(['restaurantId', 'restaurant_id'])
        .custom((value, { req }) => {
          const restaurantId = req.body.restaurantId || req.body.restaurant_id;
          if (!restaurantId) {
            throw new Error('Restaurant ID is required');
          }
          // More lenient UUID check
          if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(restaurantId)) {
            throw new Error('Restaurant ID must be a valid UUID');
          }
          return true;
        }),

      body(['orderId', 'order_id'])
        .optional()
        .custom((value) => {
          if (value && !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
            throw new Error('Order ID must be a valid UUID');
          }
          return true;
        }),

      body(['menuItemId', 'menu_item_id'])
        .optional()
        .custom((value) => {
          if (value && !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
            throw new Error('Menu item ID must be a valid UUID');
          }
          return true;
        }),

      body('rating')
        .custom((value) => {
          const rating = parseInt(value);
          if (isNaN(rating) || rating < 1 || rating > 5) {
            throw new Error('Rating must be between 1 and 5');
          }
          return true;
        }),

      // Handle additional rating fields from frontend
      body(['foodRating', 'food_rating'])
        .optional()
        .custom((value) => {
          if (value !== undefined) {
            const rating = parseInt(value);
            if (isNaN(rating) || rating < 1 || rating > 5) {
              throw new Error('Food rating must be between 1 and 5');
            }
          }
          return true;
        }),

      body(['serviceRating', 'service_rating'])
        .optional()
        .custom((value) => {
          if (value !== undefined) {
            const rating = parseInt(value);
            if (isNaN(rating) || rating < 1 || rating > 5) {
              throw new Error('Service rating must be between 1 and 5');
            }
          }
          return true;
        }),

      body(['deliveryRating', 'delivery_rating'])
        .optional()
        .custom((value) => {
          if (value !== undefined) {
            const rating = parseInt(value);
            if (isNaN(rating) || rating < 1 || rating > 5) {
              throw new Error('Delivery rating must be between 1 and 5');
            }
          }
          return true;
        }),

      body('title')
        .optional()
        .trim()
        .isLength({ max: 255 })
        .withMessage('Title must be less than 255 characters'),

      body('comment')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Comment must be less than 1000 characters'),

      body('images')
        .optional()
        .custom((value) => {
          if (value !== undefined) {
            if (!Array.isArray(value)) {
              throw new Error('Images must be an array');
            }
            if (value.length > 5) {
              throw new Error('Maximum 5 images allowed');
            }
            // Validate each image URL if provided
            value.forEach((image, index) => {
              if (image && typeof image === 'string' && image.trim().length > 0) {
                try {
                  new URL(image);
                } catch (e) {
                  throw new Error(`Image ${index + 1} must be a valid URL`);
                }
              }
            });
          }
          return true;
        })
    ];
  }

  // Update review validation
  static validateUpdateReview() {
    return [
      body('rating')
        .optional()
        .isInt({ min: 1, max: 5 })
        .withMessage('Rating must be between 1 and 5'),

      body('title')
        .optional()
        .trim()
        .isLength({ max: 255 })
        .withMessage('Title must be less than 255 characters'),

      body('comment')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Comment must be less than 1000 characters'),

      body('images')
        .optional()
        .isArray()
        .withMessage('Images must be an array'),

      body('images.*')
        .optional()
        .isURL()
        .withMessage('Each image must be a valid URL')
    ];
  }

  // Restaurant response validation
  static validateRestaurantResponse() {
    return [
      body('response')
        .trim()
        .isLength({ min: 1, max: 1000 })
        .withMessage('Response is required and must be less than 1000 characters')
    ];
  }

  // Promo code validation validation (flexible format support)
  static validatePromoCodeValidation() {
    return [
      body('code')
        .custom((value) => {
          if (!value || typeof value !== 'string') {
            throw new Error('Promo code is required');
          }

          const trimmedCode = value.trim().toUpperCase();

          if (trimmedCode.length < 3 || trimmedCode.length > 20) {
            throw new Error('Promo code must be between 3 and 20 characters');
          }

          // More flexible pattern - allow lowercase and convert to uppercase
          if (!/^[A-Z0-9]+$/i.test(trimmedCode)) {
            throw new Error('Promo code can only contain letters and numbers');
          }

          return true;
        }),

      body(['orderAmount', 'order_amount', 'amount'])
        .custom((value, { req }) => {
          const orderAmount = req.body.orderAmount || req.body.order_amount || req.body.amount;

          if (orderAmount === undefined || orderAmount === null) {
            throw new Error('Order amount is required');
          }

          const amount = parseFloat(orderAmount);
          if (isNaN(amount) || amount < 0) {
            throw new Error('Order amount must be a positive number');
          }

          return true;
        })
    ];
  }

  // Apply promo code validation
  static validateApplyPromoCode() {
    return [
      body('promoCodeId')
        .isUUID()
        .withMessage('Promo code ID must be a valid UUID'),

      body('orderId')
        .isUUID()
        .withMessage('Order ID must be a valid UUID'),

      body('discountAmount')
        .isFloat({ min: 0 })
        .withMessage('Discount amount must be a positive number')
    ];
  }

  // Create promo code validation
  static validateCreatePromoCode() {
    return [
      body('code')
        .trim()
        .isLength({ min: 3, max: 20 })
        .withMessage('Promo code must be between 3 and 20 characters')
        .matches(/^[A-Z0-9]+$/)
        .withMessage('Promo code can only contain uppercase letters and numbers'),

      body('name')
        .trim()
        .isLength({ min: 1, max: 255 })
        .withMessage('Name is required and must be less than 255 characters'),

      body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Description must be less than 1000 characters'),

      body('type')
        .isIn(['percentage', 'fixed_amount', 'free_delivery'])
        .withMessage('Type must be one of: percentage, fixed_amount, free_delivery'),

      body('value')
        .isFloat({ min: 0 })
        .withMessage('Value must be a positive number'),

      body('minimum_order')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Minimum order must be a positive number'),

      body('maximum_discount')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Maximum discount must be a positive number'),

      body('usage_limit')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Usage limit must be a positive integer'),

      body('user_limit')
        .optional()
        .isInt({ min: 1 })
        .withMessage('User limit must be a positive integer'),

      body('starts_at')
        .optional()
        .isISO8601()
        .withMessage('Start date must be a valid ISO 8601 date'),

      body('expires_at')
        .optional()
        .isISO8601()
        .withMessage('Expiry date must be a valid ISO 8601 date'),

      body('is_active')
        .optional()
        .isBoolean()
        .withMessage('is_active must be a boolean value')
    ];
  }

  // Update promo code validation
  static validateUpdatePromoCode() {
    return [
      body('code')
        .optional()
        .trim()
        .isLength({ min: 3, max: 20 })
        .withMessage('Promo code must be between 3 and 20 characters')
        .matches(/^[A-Z0-9]+$/)
        .withMessage('Promo code can only contain uppercase letters and numbers'),

      body('name')
        .optional()
        .trim()
        .isLength({ min: 1, max: 255 })
        .withMessage('Name must be less than 255 characters'),

      body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Description must be less than 1000 characters'),

      body('type')
        .optional()
        .isIn(['percentage', 'fixed_amount', 'free_delivery'])
        .withMessage('Type must be one of: percentage, fixed_amount, free_delivery'),

      body('value')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Value must be a positive number'),

      body('minimum_order')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Minimum order must be a positive number'),

      body('maximum_discount')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Maximum discount must be a positive number'),

      body('usage_limit')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Usage limit must be a positive integer'),

      body('user_limit')
        .optional()
        .isInt({ min: 1 })
        .withMessage('User limit must be a positive integer'),

      body('starts_at')
        .optional()
        .isISO8601()
        .withMessage('Start date must be a valid ISO 8601 date'),

      body('expires_at')
        .optional()
        .isISO8601()
        .withMessage('Expiry date must be a valid ISO 8601 date'),

      body('is_active')
        .optional()
        .isBoolean()
        .withMessage('is_active must be a boolean value')
    ];
  }

  // Revenue analytics query validation
  static validateRevenueAnalyticsQuery() {
    return [
      query('days')
        .optional()
        .isInt({ min: 1, max: 365 })
        .withMessage('Days must be between 1 and 365'),

      query('groupBy')
        .optional()
        .isIn(['day', 'week', 'month'])
        .withMessage('Group by must be one of: day, week, month')
    ];
  }

  // Analytics export validation
  static validateAnalyticsExport() {
    return [
      query('type')
        .isIn(['orders', 'revenue', 'customers', 'popular-items', 'promo-codes'])
        .withMessage('Type must be one of: orders, revenue, customers, popular-items, promo-codes'),

      query('format')
        .optional()
        .isIn(['json', 'csv'])
        .withMessage('Format must be json or csv'),

      query('days')
        .optional()
        .isInt({ min: 1, max: 365 })
        .withMessage('Days must be between 1 and 365')
    ];
  }

  // Nearby restaurants validation
  static validateNearbyRestaurants() {
    return [
      query('lat')
        .isFloat({ min: -90, max: 90 })
        .withMessage('Latitude must be between -90 and 90'),

      query('lng')
        .isFloat({ min: -180, max: 180 })
        .withMessage('Longitude must be between -180 and 180'),

      query('radius')
        .optional()
        .isInt({ min: 100, max: 50000 })
        .withMessage('Radius must be between 100 and 50000 meters'),

      query('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('Limit must be between 1 and 50')
    ];
  }

  // Add favorite validation
  static validateAddFavorite() {
    return [
      body('type')
        .isIn(['restaurant', 'menu_item'])
        .withMessage('Type must be either restaurant or menu_item'),

      body('itemId')
        .isUUID()
        .withMessage('Item ID must be a valid UUID')
    ];
  }

  // OTP verification validation (flexible format support)
  static validateOTPVerification() {
    return [
      body('phone')
        .custom((value) => {
          if (!value || typeof value !== 'string') {
            throw new Error('Phone number is required');
          }

          const cleanPhone = value.trim().replace(/[\s\-\(\)]/g, '');

          // More flexible phone validation - accept various formats
          if (!/^\+?[1-9]\d{7,14}$/.test(cleanPhone)) {
            throw new Error('Please provide a valid phone number');
          }

          return true;
        }),

      body('otp')
        .custom((value) => {
          if (!value) {
            throw new Error('OTP is required');
          }

          const cleanOTP = value.toString().trim();

          // Accept 4-8 digit OTPs (more flexible than just 6)
          if (!/^\d{4,8}$/.test(cleanOTP)) {
            throw new Error('OTP must be 4-8 digits');
          }

          return true;
        })
    ];
  }
}

module.exports = ValidationMiddleware;
