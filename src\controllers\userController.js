// User Profile Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const UserService = require('../services/userService');

class UserController {
  // Get current user profile
  static async getProfile(req, res) {
    try {
      const userId = req.user.id;
      const profile = await UserService.getProfile(userId);

      return ResponseHelper.success(res, profile, 'Profile retrieved successfully');
    } catch (error) {
      console.error('Get profile error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve profile', 500);
    }
  }

  // Update user profile
  static async updateProfile(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const updateData = req.body;

      const updatedProfile = await UserService.updateProfile(userId, updateData);

      return ResponseHelper.success(res, updatedProfile, 'Profile updated successfully');
    } catch (error) {
      console.error('Update profile error:', error);
      return ResponseHelper.error(res, 'Failed to update profile', 500);
    }
  }

  // Upload user avatar
  static async uploadAvatar(req, res) {
    try {
      if (!req.file) {
        return ResponseHelper.error(res, 'No image file provided', 400, {
          code: 'VALIDATION_ERROR',
          message: 'Avatar image is required'
        });
      }

      const userId = req.user.id;
      const avatarUrl = await UserService.uploadAvatar(userId, req.file);

      return ResponseHelper.success(res, { avatarUrl }, 'Avatar uploaded successfully');
    } catch (error) {
      console.error('Upload avatar error:', error);
      return ResponseHelper.error(res, 'Failed to upload avatar', 500);
    }
  }

  // Get user addresses
  static async getAddresses(req, res) {
    try {
      const userId = req.user.id;
      const addresses = await UserService.getAddresses(userId);

      return ResponseHelper.success(res, { addresses }, 'Addresses retrieved successfully');
    } catch (error) {
      console.error('Get addresses error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve addresses', 500);
    }
  }

  // Add new address
  static async addAddress(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;

      // Normalize field names from frontend format to backend format
      const addressData = {
        ...req.body,
        user_id: userId,
        // Handle different field name formats
        street: req.body.street || req.body.streetAddress,
        zip_code: req.body.zip_code || req.body.zipCode || req.body.postalCode,
        delivery_instructions: req.body.delivery_instructions || req.body.deliveryInstructions,
        is_default: req.body.is_default !== undefined ? req.body.is_default : req.body.isDefault
      };

      // Remove frontend field names to avoid conflicts
      delete addressData.streetAddress;
      delete addressData.zipCode;
      delete addressData.postalCode;
      delete addressData.deliveryInstructions;
      delete addressData.isDefault;

      const newAddress = await UserService.addAddress(userId, addressData);

      return ResponseHelper.success(res, newAddress, 'Address added successfully', 201);
    } catch (error) {
      console.error('Add address error:', error);
      return ResponseHelper.error(res, 'Failed to add address', 500);
    }
  }

  // Update address
  static async updateAddress(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const addressId = req.params.id;
      const updateData = req.body;

      const updatedAddress = await UserService.updateAddress(userId, addressId, updateData);

      if (!updatedAddress) {
        return ResponseHelper.error(res, 'Address not found', 404, {
          code: 'NOT_FOUND',
          message: 'Address not found or does not belong to user'
        });
      }

      return ResponseHelper.success(res, updatedAddress, 'Address updated successfully');
    } catch (error) {
      console.error('Update address error:', error);
      return ResponseHelper.error(res, 'Failed to update address', 500);
    }
  }

  // Delete address
  static async deleteAddress(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const addressId = req.params.id;

      const deleted = await UserService.deleteAddress(userId, addressId);

      if (!deleted) {
        return ResponseHelper.error(res, 'Address not found', 404, {
          code: 'NOT_FOUND',
          message: 'Address not found or does not belong to user'
        });
      }

      return ResponseHelper.success(res, null, 'Address deleted successfully');
    } catch (error) {
      console.error('Delete address error:', error);
      return ResponseHelper.error(res, 'Failed to delete address', 500);
    }
  }

  // Get payment methods
  static async getPaymentMethods(req, res) {
    try {
      const userId = req.user.id;
      const paymentMethods = await UserService.getPaymentMethods(userId);

      return ResponseHelper.success(res, { paymentMethods }, 'Payment methods retrieved successfully');
    } catch (error) {
      console.error('Get payment methods error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve payment methods', 500);
    }
  }

  // Add payment method
  static async addPaymentMethod(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const { stripePaymentMethodId, isDefault } = req.body;

      const paymentMethod = await UserService.addPaymentMethod(userId, stripePaymentMethodId, isDefault);

      return ResponseHelper.success(res, paymentMethod, 'Payment method added successfully', 201);
    } catch (error) {
      console.error('Add payment method error:', error);

      if (error.message.includes('Stripe')) {
        return ResponseHelper.error(res, error.message, 400, {
          code: 'PAYMENT_ERROR',
          message: error.message
        });
      }

      return ResponseHelper.error(res, 'Failed to add payment method', 500);
    }
  }

  // Delete payment method
  static async deletePaymentMethod(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const paymentMethodId = req.params.id;

      const deleted = await UserService.deletePaymentMethod(userId, paymentMethodId);

      if (!deleted) {
        return ResponseHelper.error(res, 'Payment method not found', 404, {
          code: 'NOT_FOUND',
          message: 'Payment method not found or does not belong to user'
        });
      }

      return ResponseHelper.success(res, null, 'Payment method deleted successfully');
    } catch (error) {
      console.error('Delete payment method error:', error);
      return ResponseHelper.error(res, 'Failed to delete payment method', 500);
    }
  }

  // Get user favorites
  static async getFavorites(req, res) {
    try {
      const userId = req.user.id;
      const pool = require('../config/database').getPool();

      // Try to detect which schema is being used by checking table structure
      let restaurants = [];
      let menuItems = [];

      try {
        // First, try the separate columns approach (restaurant_id, menu_item_id)
        const restaurantsQuery = `
          SELECT
            uf.id as favorite_id,
            uf.created_at as favorited_at,
            r.id, r.name, r.description, r.image, r.logo, r.slug,
            r.rating, r.review_count, r.cuisine_types, r.delivery_fee,
            r.minimum_order, r.estimated_delivery_time, r.accepting_orders
          FROM user_favorites uf
          JOIN restaurants r ON uf.restaurant_id = r.id
          WHERE uf.user_id = $1 AND uf.restaurant_id IS NOT NULL
          AND r.is_active = true
          ORDER BY uf.created_at DESC
          LIMIT 50
        `;

        const menuItemsQuery = `
          SELECT
            uf.id as favorite_id,
            uf.created_at as favorited_at,
            mi.id, mi.name, mi.description, mi.price, mi.image,
            mi.is_available, mi.is_popular, mi.is_vegetarian, mi.is_vegan, mi.is_gluten_free,
            r.id as restaurant_id, r.name as restaurant_name, r.logo as restaurant_logo
          FROM user_favorites uf
          JOIN menu_items mi ON uf.menu_item_id = mi.id
          JOIN restaurants r ON mi.restaurant_id = r.id
          WHERE uf.user_id = $1 AND uf.menu_item_id IS NOT NULL
          AND mi.is_available = true AND r.is_active = true
          ORDER BY uf.created_at DESC
          LIMIT 50
        `;

        const [restaurantsResult, menuItemsResult] = await Promise.all([
          pool.query(restaurantsQuery, [userId]),
          pool.query(menuItemsQuery, [userId])
        ]);

        restaurants = restaurantsResult.rows;
        menuItems = menuItemsResult.rows;

      } catch (separateColumnsError) {
        console.log('Separate columns approach failed, trying generic approach...');

        // Fallback to generic approach (item_type, item_id)
        try {
          const genericRestaurantsQuery = `
            SELECT
              uf.id as favorite_id,
              uf.created_at as favorited_at,
              r.id, r.name, r.description, r.image, r.logo, r.slug,
              r.rating, r.review_count, r.cuisine_types, r.delivery_fee,
              r.minimum_order, r.estimated_delivery_time, r.accepting_orders
            FROM user_favorites uf
            JOIN restaurants r ON uf.item_id = r.id
            WHERE uf.user_id = $1 AND uf.item_type = 'restaurant'
            AND r.is_active = true
            ORDER BY uf.created_at DESC
            LIMIT 50
          `;

          const genericMenuItemsQuery = `
            SELECT
              uf.id as favorite_id,
              uf.created_at as favorited_at,
              mi.id, mi.name, mi.description, mi.price, mi.image,
              mi.is_available, mi.is_popular, mi.is_vegetarian, mi.is_vegan, mi.is_gluten_free,
              r.id as restaurant_id, r.name as restaurant_name, r.logo as restaurant_logo
            FROM user_favorites uf
            JOIN menu_items mi ON uf.item_id = mi.id
            JOIN restaurants r ON mi.restaurant_id = r.id
            WHERE uf.user_id = $1 AND uf.item_type = 'menu_item'
            AND mi.is_available = true AND r.is_active = true
            ORDER BY uf.created_at DESC
            LIMIT 50
          `;

          const [restaurantsResult, menuItemsResult] = await Promise.all([
            pool.query(genericRestaurantsQuery, [userId]),
            pool.query(genericMenuItemsQuery, [userId])
          ]);

          restaurants = restaurantsResult.rows;
          menuItems = menuItemsResult.rows;

        } catch (genericError) {
          console.error('Both schema approaches failed:', genericError);
          // Return empty favorites instead of error
          restaurants = [];
          menuItems = [];
        }
      }

      const favorites = {
        restaurants: restaurants || [],
        menuItems: menuItems || []
      };

      return ResponseHelper.success(res, favorites, 'User favorites retrieved successfully');
    } catch (error) {
      console.error('Get favorites error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve favorites', 500);
    }
  }

  // Add to favorites
  static async addFavorite(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const { type, itemId } = req.body;
      const UserFavorite = require('../models/UserFavorite');
      const userFavoriteModel = new UserFavorite();

      let favorite;
      if (type === 'restaurant') {
        favorite = await userFavoriteModel.addRestaurantToFavorites(userId, itemId);
      } else if (type === 'menu_item') {
        favorite = await userFavoriteModel.addMenuItemToFavorites(userId, itemId);
      } else {
        return ResponseHelper.error(res, 'Invalid favorite type', 400);
      }

      return ResponseHelper.success(res, favorite, 'Item added to favorites successfully', 201);
    } catch (error) {
      console.error('Add favorite error:', error);

      if (error.message.includes('already in favorites')) {
        return ResponseHelper.error(res, error.message, 409);
      }

      return ResponseHelper.error(res, 'Failed to add to favorites', 500);
    }
  }

  // Remove from favorites
  static async removeFavorite(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const restaurantId = req.params.restaurantId;
      const UserFavorite = require('../models/UserFavorite');
      const userFavoriteModel = new UserFavorite();

      await userFavoriteModel.removeRestaurantFromFavorites(userId, restaurantId);

      return ResponseHelper.success(res, { removed: true }, 'Restaurant removed from favorites successfully');
    } catch (error) {
      console.error('Remove favorite error:', error);

      if (error.message.includes('not found')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to remove from favorites', 500);
    }
  }

  // Get user reviews
  static async getReviews(req, res) {
    try {
      const userId = req.user.id;
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);

      const Review = require('../models/Review');
      const reviewModel = new Review();

      const result = await reviewModel.getUserReviews(userId, page, limit);

      return ResponseHelper.success(res, result, 'User reviews retrieved successfully');
    } catch (error) {
      console.error('Get reviews error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve reviews', 500);
    }
  }

  // Get user notification settings
  static async getNotificationSettings(req, res) {
    try {
      const userId = req.user.id;

      // Mock notification settings - in a real implementation, this would come from database
      const notificationSettings = {
        orderUpdates: true,
        promotions: true,
        newRestaurants: false,
        weeklyDigest: true,
        pushNotifications: {
          orderUpdates: true,
          promotions: false,
          newRestaurants: false,
          weeklyDigest: false
        },
        emailNotifications: {
          orderUpdates: true,
          promotions: true,
          newRestaurants: false,
          weeklyDigest: true
        },
        smsNotifications: {
          orderUpdates: true,
          promotions: false,
          newRestaurants: false,
          weeklyDigest: false
        }
      };

      return ResponseHelper.success(res, notificationSettings, 'Notification settings retrieved successfully');
    } catch (error) {
      console.error('Get notification settings error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve notification settings', 500);
    }
  }

  // Update user notification settings
  static async updateNotificationSettings(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const settings = req.body;

      // In a real implementation, you would save these settings to the database
      // For now, just return the updated settings
      const updatedSettings = {
        ...settings,
        updatedAt: new Date().toISOString()
      };

      return ResponseHelper.success(res, updatedSettings, 'Notification settings updated successfully');
    } catch (error) {
      console.error('Update notification settings error:', error);
      return ResponseHelper.error(res, 'Failed to update notification settings', 500);
    }
  }
}

module.exports = UserController;
