// User Profile Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const UserService = require('../services/userService');

class UserController {
  // Get current user profile
  static async getProfile(req, res) {
    try {
      const userId = req.user.id;
      const profile = await UserService.getProfile(userId);

      return ResponseHelper.success(res, profile, 'Profile retrieved successfully');
    } catch (error) {
      console.error('Get profile error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve profile', 500);
    }
  }

  // Update user profile
  static async updateProfile(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const updateData = req.body;

      const updatedProfile = await UserService.updateProfile(userId, updateData);

      return ResponseHelper.success(res, updatedProfile, 'Profile updated successfully');
    } catch (error) {
      console.error('Update profile error:', error);
      return ResponseHelper.error(res, 'Failed to update profile', 500);
    }
  }

  // Upload user avatar
  static async uploadAvatar(req, res) {
    try {
      if (!req.file) {
        return ResponseHelper.error(res, 'No image file provided', 400, {
          code: 'VALIDATION_ERROR',
          message: 'Avatar image is required'
        });
      }

      const userId = req.user.id;
      const avatarUrl = await UserService.uploadAvatar(userId, req.file);

      return ResponseHelper.success(res, { avatarUrl }, 'Avatar uploaded successfully');
    } catch (error) {
      console.error('Upload avatar error:', error);
      return ResponseHelper.error(res, 'Failed to upload avatar', 500);
    }
  }

  // Get user addresses
  static async getAddresses(req, res) {
    try {
      const userId = req.user.id;
      const addresses = await UserService.getAddresses(userId);

      return ResponseHelper.success(res, { addresses }, 'Addresses retrieved successfully');
    } catch (error) {
      console.error('Get addresses error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve addresses', 500);
    }
  }

  // Add new address
  static async addAddress(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const addressData = req.body;

      const newAddress = await UserService.addAddress(userId, addressData);

      return ResponseHelper.success(res, newAddress, 'Address added successfully', 201);
    } catch (error) {
      console.error('Add address error:', error);
      return ResponseHelper.error(res, 'Failed to add address', 500);
    }
  }

  // Update address
  static async updateAddress(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const addressId = req.params.id;
      const updateData = req.body;

      const updatedAddress = await UserService.updateAddress(userId, addressId, updateData);

      if (!updatedAddress) {
        return ResponseHelper.error(res, 'Address not found', 404, {
          code: 'NOT_FOUND',
          message: 'Address not found or does not belong to user'
        });
      }

      return ResponseHelper.success(res, updatedAddress, 'Address updated successfully');
    } catch (error) {
      console.error('Update address error:', error);
      return ResponseHelper.error(res, 'Failed to update address', 500);
    }
  }

  // Delete address
  static async deleteAddress(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const addressId = req.params.id;

      const deleted = await UserService.deleteAddress(userId, addressId);

      if (!deleted) {
        return ResponseHelper.error(res, 'Address not found', 404, {
          code: 'NOT_FOUND',
          message: 'Address not found or does not belong to user'
        });
      }

      return ResponseHelper.success(res, null, 'Address deleted successfully');
    } catch (error) {
      console.error('Delete address error:', error);
      return ResponseHelper.error(res, 'Failed to delete address', 500);
    }
  }

  // Get payment methods
  static async getPaymentMethods(req, res) {
    try {
      const userId = req.user.id;
      const paymentMethods = await UserService.getPaymentMethods(userId);

      return ResponseHelper.success(res, { paymentMethods }, 'Payment methods retrieved successfully');
    } catch (error) {
      console.error('Get payment methods error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve payment methods', 500);
    }
  }

  // Add payment method
  static async addPaymentMethod(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const { stripePaymentMethodId, isDefault } = req.body;

      const paymentMethod = await UserService.addPaymentMethod(userId, stripePaymentMethodId, isDefault);

      return ResponseHelper.success(res, paymentMethod, 'Payment method added successfully', 201);
    } catch (error) {
      console.error('Add payment method error:', error);

      if (error.message.includes('Stripe')) {
        return ResponseHelper.error(res, error.message, 400, {
          code: 'PAYMENT_ERROR',
          message: error.message
        });
      }

      return ResponseHelper.error(res, 'Failed to add payment method', 500);
    }
  }

  // Delete payment method
  static async deletePaymentMethod(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const paymentMethodId = req.params.id;

      const deleted = await UserService.deletePaymentMethod(userId, paymentMethodId);

      if (!deleted) {
        return ResponseHelper.error(res, 'Payment method not found', 404, {
          code: 'NOT_FOUND',
          message: 'Payment method not found or does not belong to user'
        });
      }

      return ResponseHelper.success(res, null, 'Payment method deleted successfully');
    } catch (error) {
      console.error('Delete payment method error:', error);
      return ResponseHelper.error(res, 'Failed to delete payment method', 500);
    }
  }

  // Get user favorites
  static async getFavorites(req, res) {
    try {
      const userId = req.user.id;
      const UserFavorite = require('../models/UserFavorite');
      const userFavoriteModel = new UserFavorite();

      // Get both favorite restaurants and menu items
      const [restaurants, menuItems] = await Promise.all([
        userFavoriteModel.getFavoriteRestaurants(userId, 1, 50),
        userFavoriteModel.getFavoriteMenuItems(userId, 1, 50)
      ]);

      const favorites = {
        restaurants: restaurants.restaurants || [],
        menuItems: menuItems.menuItems || []
      };

      return ResponseHelper.success(res, favorites, 'User favorites retrieved successfully');
    } catch (error) {
      console.error('Get favorites error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve favorites', 500);
    }
  }

  // Add to favorites
  static async addFavorite(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const { type, itemId } = req.body;
      const UserFavorite = require('../models/UserFavorite');
      const userFavoriteModel = new UserFavorite();

      let favorite;
      if (type === 'restaurant') {
        favorite = await userFavoriteModel.addRestaurantToFavorites(userId, itemId);
      } else if (type === 'menu_item') {
        favorite = await userFavoriteModel.addMenuItemToFavorites(userId, itemId);
      } else {
        return ResponseHelper.error(res, 'Invalid favorite type', 400);
      }

      return ResponseHelper.success(res, favorite, 'Item added to favorites successfully', 201);
    } catch (error) {
      console.error('Add favorite error:', error);

      if (error.message.includes('already in favorites')) {
        return ResponseHelper.error(res, error.message, 409);
      }

      return ResponseHelper.error(res, 'Failed to add to favorites', 500);
    }
  }

  // Remove from favorites
  static async removeFavorite(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const restaurantId = req.params.restaurantId;
      const UserFavorite = require('../models/UserFavorite');
      const userFavoriteModel = new UserFavorite();

      await userFavoriteModel.removeRestaurantFromFavorites(userId, restaurantId);

      return ResponseHelper.success(res, { removed: true }, 'Restaurant removed from favorites successfully');
    } catch (error) {
      console.error('Remove favorite error:', error);

      if (error.message.includes('not found')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to remove from favorites', 500);
    }
  }

  // Get user reviews
  static async getReviews(req, res) {
    try {
      const userId = req.user.id;
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);

      const Review = require('../models/Review');
      const reviewModel = new Review();

      const result = await reviewModel.getUserReviews(userId, page, limit);

      return ResponseHelper.success(res, result, 'User reviews retrieved successfully');
    } catch (error) {
      console.error('Get reviews error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve reviews', 500);
    }
  }
}

module.exports = UserController;
