// Search Service
const databaseManager = require('../config/database');

class SearchService {
  // Search restaurants by name, cuisine, or menu items
  static async searchRestaurants(searchParams) {
    const pool = databaseManager.getPool();
    const {
      q: query,
      latitude,
      longitude,
      radius = 10000,
      page = 1,
      limit = 20,
      filters = {}
    } = searchParams;

    try {
      let whereConditions = ['r.is_active = true'];
      let queryParams = [];
      let paramIndex = 1;

      // Location-based filtering
      let distanceSelect = '';
      if (latitude && longitude) {
        distanceSelect = `, calculate_distance(r.latitude, r.longitude, $${paramIndex}, $${paramIndex + 1}) as distance`;
        whereConditions.push(`calculate_distance(r.latitude, r.longitude, $${paramIndex}, $${paramIndex + 1}) <= $${paramIndex + 2}`);
        queryParams.push(parseFloat(latitude), parseFloat(longitude), parseFloat(radius) / 1000);
        paramIndex += 3;
      }

      // Search query
      if (query && query.trim()) {
        const searchTerm = `%${query.trim()}%`;
        whereConditions.push(`(
          r.name ILIKE $${paramIndex} OR 
          r.description ILIKE $${paramIndex} OR 
          r.cuisine_type ILIKE $${paramIndex} OR
          EXISTS (
            SELECT 1 FROM menu_items mi 
            WHERE mi.restaurant_id = r.id 
            AND mi.is_available = true 
            AND (mi.name ILIKE $${paramIndex} OR mi.description ILIKE $${paramIndex})
          )
        )`);
        queryParams.push(searchTerm);
        paramIndex++;
      }

      // Apply additional filters
      if (filters.cuisine) {
        whereConditions.push(`r.cuisine_type ILIKE $${paramIndex}`);
        queryParams.push(`%${filters.cuisine}%`);
        paramIndex++;
      }

      if (filters.rating) {
        whereConditions.push(`r.rating >= $${paramIndex}`);
        queryParams.push(parseFloat(filters.rating));
        paramIndex++;
      }

      if (filters.priceRange) {
        whereConditions.push(`r.price_range <= $${paramIndex}`);
        queryParams.push(parseInt(filters.priceRange));
        paramIndex++;
      }

      if (filters.deliveryFee) {
        whereConditions.push(`r.delivery_fee <= $${paramIndex}`);
        queryParams.push(parseFloat(filters.deliveryFee));
        paramIndex++;
      }

      // Calculate offset
      const offset = (page - 1) * limit;

      // Build the main query
      const searchQuery = `
        SELECT 
          r.id, r.name, r.description, r.cuisine_type, r.logo_url, r.cover_image_url,
          r.street_address, r.city, r.state, r.rating, r.review_count, r.price_range,
          r.delivery_fee, r.minimum_order, r.delivery_time_min, r.delivery_time_max,
          r.is_featured ${distanceSelect}
        FROM restaurants r
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY 
          CASE WHEN r.name ILIKE $${paramIndex + 2} THEN 1 ELSE 2 END,
          r.rating DESC, 
          r.review_count DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      queryParams.push(limit, offset, query ? `%${query}%` : '%');

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM restaurants r
        WHERE ${whereConditions.join(' AND ')}
      `;

      const [searchResult, countResult] = await Promise.all([
        pool.query(searchQuery, queryParams),
        pool.query(countQuery, queryParams.slice(0, -3)) // Remove limit, offset, and name match param
      ]);

      const restaurants = searchResult.rows.map(restaurant => ({
        id: restaurant.id,
        name: restaurant.name,
        description: restaurant.description,
        cuisineType: restaurant.cuisine_type,
        logoUrl: restaurant.logo_url,
        coverImageUrl: restaurant.cover_image_url,
        rating: restaurant.rating ? parseFloat(restaurant.rating) : 0,
        reviewCount: restaurant.review_count,
        priceRange: restaurant.price_range,
        deliveryFee: restaurant.delivery_fee ? parseFloat(restaurant.delivery_fee) : 0,
        minimumOrder: restaurant.minimum_order ? parseFloat(restaurant.minimum_order) : 0,
        deliveryTimeMin: restaurant.delivery_time_min,
        deliveryTimeMax: restaurant.delivery_time_max,
        distance: restaurant.distance ? parseFloat(restaurant.distance) : null,
        isFeatured: restaurant.is_featured,
        address: {
          streetAddress: restaurant.street_address,
          city: restaurant.city,
          state: restaurant.state
        }
      }));

      const total = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / limit);

      return {
        restaurants,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages
        },
        query: query || '',
        filters
      };
    } catch (error) {
      throw error;
    }
  }

  // Get search suggestions and autocomplete
  static async getSearchSuggestions(query, limit = 10) {
    const pool = databaseManager.getPool();

    try {
      if (!query || query.trim().length < 2) {
        return { suggestions: [] };
      }

      const searchTerm = `%${query.trim()}%`;

      // Get restaurant name suggestions
      const restaurantSuggestions = await pool.query(`
        SELECT DISTINCT name as suggestion, 'restaurant' as type
        FROM restaurants
        WHERE name ILIKE $1 AND is_active = true
        ORDER BY name
        LIMIT $2
      `, [searchTerm, Math.ceil(limit / 2)]);

      // Get cuisine type suggestions
      const cuisineSuggestions = await pool.query(`
        SELECT DISTINCT cuisine_type as suggestion, 'cuisine' as type
        FROM restaurants
        WHERE cuisine_type ILIKE $1 AND is_active = true
        ORDER BY cuisine_type
        LIMIT $2
      `, [searchTerm, Math.floor(limit / 2)]);

      // Get menu item suggestions
      const menuItemSuggestions = await pool.query(`
        SELECT DISTINCT mi.name as suggestion, 'menu_item' as type
        FROM menu_items mi
        JOIN restaurants r ON mi.restaurant_id = r.id
        WHERE mi.name ILIKE $1 AND mi.is_available = true AND r.is_active = true
        ORDER BY mi.name
        LIMIT $2
      `, [searchTerm, Math.floor(limit / 3)]);

      // Combine and format suggestions
      const allSuggestions = [
        ...restaurantSuggestions.rows,
        ...cuisineSuggestions.rows,
        ...menuItemSuggestions.rows
      ].slice(0, limit);

      return {
        suggestions: allSuggestions.map(s => ({
          text: s.suggestion,
          type: s.type
        }))
      };
    } catch (error) {
      throw error;
    }
  }

  // Get popular search terms
  static async getPopularSearches(limit = 10) {
    // In a real application, you would track search queries and return popular ones
    // For now, return some static popular searches
    return {
      popularSearches: [
        { text: 'Pizza', type: 'cuisine' },
        { text: 'Burger', type: 'menu_item' },
        { text: 'Chinese', type: 'cuisine' },
        { text: 'Italian', type: 'cuisine' },
        { text: 'Sushi', type: 'menu_item' },
        { text: 'Mexican', type: 'cuisine' },
        { text: 'Thai', type: 'cuisine' },
        { text: 'Indian', type: 'cuisine' },
        { text: 'Sandwich', type: 'menu_item' },
        { text: 'Salad', type: 'menu_item' }
      ].slice(0, limit)
    };
  }
}

module.exports = SearchService;
