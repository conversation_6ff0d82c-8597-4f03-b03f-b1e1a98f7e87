// Order Routes
const express = require('express');
const OrderController = require('../controllers/orderController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting and authentication to all routes
router.use(apiLimiter);
router.use(AuthMiddleware.authenticate);

// POST /api/v1/orders - Create a new order
router.post('/',
  ValidationMiddleware.validateCreateOrder(),
  OrderController.createOrder
);

// GET /api/v1/orders - Get user's orders with pagination and filtering
router.get('/',
  ValidationMiddleware.validateOrdersQuery(),
  OrderController.getUserOrders
);

// GET /api/v1/orders/:id - Get specific order details
router.get('/:id',
  ValidationMiddleware.validateUUID('id'),
  OrderController.getOrderById
);

// GET /api/v1/orders/:id/track - Get real-time order tracking information
router.get('/:id/track',
  ValidationMiddleware.validateUUID('id'),
  OrderController.trackOrder
);

// PATCH /api/v1/orders/:id/cancel - Cancel an order
router.patch('/:id/cancel',
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateCancelOrder(),
  OrderController.cancelOrder
);

// Admin routes (require admin privileges)

// PUT /api/v1/orders/:id/status - Update order status (admin only)
router.put('/:id/status',
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateUpdateOrderStatus(),
  OrderController.updateOrderStatus
);

// GET /api/v1/orders/status/:status - Get orders by status (admin only)
router.get('/status/:status',
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateOrderStatus(),
  OrderController.getOrdersByStatus
);

// GET /api/v1/orders/analytics/summary - Get order analytics (admin only)
router.get('/analytics/summary',
  AuthMiddleware.requireAdmin,
  OrderController.getOrderAnalytics
);

// GET /api/v1/orders/attention/required - Get orders requiring attention (admin only)
router.get('/attention/required',
  AuthMiddleware.requireAdmin,
  OrderController.getOrdersRequiringAttention
);

// PUT /api/v1/orders/:id/delivered - Mark order as delivered (admin only)
router.put('/:id/delivered',
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateMarkDelivered(),
  OrderController.markOrderDelivered
);

module.exports = router;
