// Order Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const Order = require('../models/Order');
const OrderStatusHistory = require('../models/OrderStatusHistory');

class OrderController {
  // Create a new order
  static async createOrder(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const requestBody = req.body;

      // Handle both frontend format and backend format
      let orderData, orderItems;

      if (requestBody.orderData && requestBody.orderItems) {
        // Backend format
        orderData = requestBody.orderData;
        orderItems = requestBody.orderItems;
      } else {
        // Frontend format - extract order data and items
        const { items, deliveryAddress, paymentMethodId, specialInstructions, ...otherData } = requestBody;

        // Calculate order totals (simplified calculation)
        const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const tax = subtotal * 0.08; // 8% tax
        const deliveryFee = 2.99; // Fixed delivery fee
        const totalAmount = subtotal + tax + deliveryFee;

        orderData = {
          restaurant_id: requestBody.restaurantId,
          subtotal: subtotal,
          tax: tax,
          delivery_fee: deliveryFee,
          total_amount: totalAmount,
          delivery_address: deliveryAddress,
          payment_method_id: paymentMethodId,
          special_instructions: specialInstructions,
          ...otherData
        };

        orderItems = items.map(item => ({
          menu_item_id: item.menuItemId,
          quantity: item.quantity,
          unit_price: item.price,
          total_price: item.price * item.quantity,
          special_instructions: item.specialInstructions,
          customizations: item.customizations || []
        }));
      }

      // Add user_id to order data
      orderData.user_id = userId;

      const orderModel = new Order();
      const order = await orderModel.createOrderWithItems(orderData, orderItems);

      return ResponseHelper.success(res, order, 'Order created successfully', 201);
    } catch (error) {
      console.error('Create order error:', error);

      if (error.message.includes('not available') || error.message.includes('Minimum order')) {
        return ResponseHelper.error(res, error.message, 400);
      }

      return ResponseHelper.error(res, 'Failed to create order', 500);
    }
  }

  // Get user's orders with pagination and filtering
  static async getUserOrders(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const filters = {
        status: req.query.status,
        page: parseInt(req.query.page) || 1,
        limit: Math.min(parseInt(req.query.limit) || 10, 50),
        startDate: req.query.startDate,
        endDate: req.query.endDate
      };

      const orderModel = new Order();
      const result = await orderModel.getUserOrders(userId, filters);

      return ResponseHelper.success(res, result, 'Orders retrieved successfully');
    } catch (error) {
      console.error('Get user orders error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve orders', 500);
    }
  }

  // Get specific order details
  static async getOrderById(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const orderId = req.params.id;
      const orderModel = new Order();

      const order = await orderModel.getOrderWithDetails(orderId);

      if (!order || order.user_id !== userId) {
        return ResponseHelper.error(res, 'Order not found', 404, {
          code: 'NOT_FOUND',
          message: 'Order not found or access denied'
        });
      }

      return ResponseHelper.success(res, order, 'Order details retrieved successfully');
    } catch (error) {
      console.error('Get order by ID error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve order details', 500);
    }
  }

  // Get real-time order tracking information
  static async trackOrder(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const orderId = req.params.id;
      const orderModel = new Order();

      const order = await orderModel.getOrderWithHistory(orderId);

      if (!order || order.user_id !== userId) {
        return ResponseHelper.error(res, 'Order not found', 404, {
          code: 'NOT_FOUND',
          message: 'Order not found or access denied'
        });
      }

      // Format tracking information
      const tracking = {
        orderId: order.id,
        orderNumber: order.order_number,
        status: order.status,
        estimatedDeliveryTime: order.estimated_delivery_time,
        statusHistory: order.status_history,
        restaurant: {
          name: order.restaurant_name,
          phone: order.restaurant_phone
        }
      };

      return ResponseHelper.success(res, tracking, 'Order tracking retrieved successfully');
    } catch (error) {
      console.error('Track order error:', error);
      return ResponseHelper.error(res, 'Failed to track order', 500);
    }
  }

  // Cancel an order
  static async cancelOrder(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const orderId = req.params.id;
      const { reason } = req.body;
      const orderModel = new Order();

      // Check if order exists and belongs to user
      const order = await orderModel.findById(orderId);
      if (!order || order.user_id !== userId) {
        return ResponseHelper.error(res, 'Order not found', 404, {
          code: 'NOT_FOUND',
          message: 'Order not found or access denied'
        });
      }

      // Check if order can be cancelled
      const cancellableStatuses = ['pending', 'confirmed'];
      if (!cancellableStatuses.includes(order.status)) {
        return ResponseHelper.error(res, 'Order cannot be cancelled at this stage', 400, {
          code: 'INVALID_STATUS',
          message: 'Order cannot be cancelled once it is being prepared'
        });
      }

      // Cancel the order
      await orderModel.updateOrderStatus(orderId, 'cancelled', reason || 'Cancelled by customer', userId);

      return ResponseHelper.success(res, { cancelled: true }, 'Order cancelled successfully');
    } catch (error) {
      console.error('Cancel order error:', error);
      return ResponseHelper.error(res, 'Failed to cancel order', 500);
    }
  }

  // Update order status (admin only)
  static async updateOrderStatus(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const orderId = req.params.id;
      const { status, note } = req.body;
      const changedBy = req.user.id;
      const orderModel = new Order();

      const order = await orderModel.updateOrderStatus(orderId, status, note, changedBy);

      if (!order) {
        return ResponseHelper.error(res, 'Order not found', 404, {
          code: 'NOT_FOUND',
          message: 'Order not found'
        });
      }

      return ResponseHelper.success(res, order, 'Order status updated successfully');
    } catch (error) {
      console.error('Update order status error:', error);
      return ResponseHelper.error(res, 'Failed to update order status', 500);
    }
  }

  // Get orders by status (admin only)
  static async getOrdersByStatus(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const status = req.params.status;
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);
      const orderModel = new Order();

      const result = await orderModel.getOrdersByStatus(status, page, limit);

      return ResponseHelper.success(res, result, `Orders with status '${status}' retrieved successfully`);
    } catch (error) {
      console.error('Get orders by status error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve orders by status', 500);
    }
  }

  // Get order analytics (admin only)
  static async getOrderAnalytics(req, res) {
    try {
      const days = parseInt(req.query.days) || 30;
      const orderModel = new Order();

      const analytics = await orderModel.getOrderAnalytics(days);

      return ResponseHelper.success(res, { analytics }, 'Order analytics retrieved successfully');
    } catch (error) {
      console.error('Get order analytics error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve order analytics', 500);
    }
  }

  // Get orders requiring attention (admin only)
  static async getOrdersRequiringAttention(req, res) {
    try {
      const hoursThreshold = parseInt(req.query.hours) || 2;
      const orderModel = new Order();

      const orders = await orderModel.getOrdersRequiringAttention(hoursThreshold);

      return ResponseHelper.success(res, { orders }, 'Orders requiring attention retrieved successfully');
    } catch (error) {
      console.error('Get orders requiring attention error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve orders requiring attention', 500);
    }
  }

  // Mark order as delivered (admin only)
  static async markOrderDelivered(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const orderId = req.params.id;
      const { actualDeliveryTime } = req.body;
      const orderModel = new Order();

      const result = await orderModel.markAsDelivered(orderId, actualDeliveryTime);

      if (!result) {
        return ResponseHelper.error(res, 'Order not found', 404, {
          code: 'NOT_FOUND',
          message: 'Order not found'
        });
      }

      return ResponseHelper.success(res, { delivered: true }, 'Order marked as delivered successfully');
    } catch (error) {
      console.error('Mark order delivered error:', error);
      return ResponseHelper.error(res, 'Failed to mark order as delivered', 500);
    }
  }
}

module.exports = OrderController;
