// FoodWay Database Reset Script
// Drops all tables and recreates the schema for fresh development

require('dotenv').config();
const { Pool } = require('pg');

async function reset() {
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not found in environment variables');
    process.exit(1);
  }

  // Confirm reset in production
  if (process.env.NODE_ENV === 'production') {
    console.error('❌ Database reset is not allowed in production environment');
    process.exit(1);
  }

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    console.log('🔄 Starting FoodWay database reset...\n');
    console.log('⚠️  WARNING: This will delete ALL data in the database!');
    
    // Drop all tables
    console.log('🗑️  Dropping existing tables...');
    
    const tables = ['users', 'restaurants', 'migrations'];
    
    for (const table of tables) {
      try {
        await pool.query(`DROP TABLE IF EXISTS ${table} CASCADE`);
        console.log(`✅ Dropped table: ${table}`);
      } catch (error) {
        console.log(`⚠️  Could not drop table ${table}: ${error.message}`);
      }
    }

    console.log('✅ All tables dropped successfully');

    console.log('\n🎉 Database reset completed successfully!');
    console.log('================================');
    console.log('🚀 Run "npm run db:migrate" to recreate the schema');
    console.log('🌱 Run "npm run db:seed" to add sample data');
    console.log('================================\n');

  } catch (error) {
    console.error('❌ Reset failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run reset if called directly
if (require.main === module) {
  reset();
}

module.exports = reset;
