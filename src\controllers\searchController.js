// Search Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const SearchService = require('../services/searchService');

class SearchController {
  // Search restaurants by name, cuisine, or menu items
  static async searchRestaurants(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const searchParams = {
        q: req.query.q,
        latitude: req.query.latitude ? parseFloat(req.query.latitude) : null,
        longitude: req.query.longitude ? parseFloat(req.query.longitude) : null,
        radius: req.query.radius ? parseInt(req.query.radius) : 10000,
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 20,
        filters: {}
      };

      // Parse filters if provided
      if (req.query.filters) {
        try {
          searchParams.filters = JSON.parse(req.query.filters);
        } catch (error) {
          return ResponseHelper.error(res, 'Invalid filters format', 400, {
            code: 'VALIDATION_ERROR',
            message: 'Filters must be valid JSON'
          });
        }
      }

      // Add individual filter parameters
      if (req.query.cuisine) searchParams.filters.cuisine = req.query.cuisine;
      if (req.query.rating) searchParams.filters.rating = parseFloat(req.query.rating);
      if (req.query.priceRange) searchParams.filters.priceRange = parseInt(req.query.priceRange);
      if (req.query.deliveryFee) searchParams.filters.deliveryFee = parseFloat(req.query.deliveryFee);

      const result = await SearchService.searchRestaurants(searchParams);
      
      return ResponseHelper.success(res, result, 'Search completed successfully');
    } catch (error) {
      console.error('Search restaurants error:', error);
      return ResponseHelper.error(res, 'Search failed', 500);
    }
  }

  // Get search suggestions and autocomplete
  static async getSearchSuggestions(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const query = req.query.q;
      const limit = parseInt(req.query.limit) || 10;

      if (!query || query.trim().length < 2) {
        return ResponseHelper.error(res, 'Query must be at least 2 characters long', 400, {
          code: 'VALIDATION_ERROR',
          message: 'Search query must be at least 2 characters long'
        });
      }

      const result = await SearchService.getSearchSuggestions(query, limit);
      
      return ResponseHelper.success(res, result, 'Search suggestions retrieved successfully');
    } catch (error) {
      console.error('Get search suggestions error:', error);
      return ResponseHelper.error(res, 'Failed to get search suggestions', 500);
    }
  }

  // Get popular search terms
  static async getPopularSearches(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 10;
      const result = await SearchService.getPopularSearches(limit);
      
      return ResponseHelper.success(res, result, 'Popular searches retrieved successfully');
    } catch (error) {
      console.error('Get popular searches error:', error);
      return ResponseHelper.error(res, 'Failed to get popular searches', 500);
    }
  }
}

module.exports = SearchController;
