// Redis Configuration and Connection Management
require('dotenv').config();
const redis = require('redis');

class RedisManager {
  constructor() {
    this.client = null;
  }

  async initialize() {
    if (!process.env.REDIS_URL) {
      console.log('⚠️  REDIS_URL not found in environment variables');
      return false;
    }

    try {
      this.client = redis.createClient({
        url: process.env.REDIS_URL,
        socket: {
          reconnectStrategy: (retries) => Math.min(retries * 50, 500),
          connectTimeout: 5000
        }
      });

      this.client.on('error', (err) => {
        console.error('❌ Redis Client Error:', err.message);
      });

      this.client.on('connect', () => {
        console.log('🔄 Redis connecting...');
      });

      this.client.on('ready', () => {
        console.log('✅ Redis connected successfully');
      });

      await this.client.connect();
      return true;
    } catch (error) {
      console.error('❌ Redis connection failed:', error.message);
      return false;
    }
  }

  getClient() {
    return this.client;
  }

  isReady() {
    return this.client?.isReady || false;
  }

  async close() {
    if (this.client) {
      await this.client.quit();
      console.log('✅ Redis connection closed');
    }
  }

  async set(key, value, options = {}) {
    if (!this.client || !this.client.isReady) {
      throw new Error('Redis not connected');
    }
    return this.client.set(key, value, options);
  }

  async get(key) {
    if (!this.client || !this.client.isReady) {
      throw new Error('Redis not connected');
    }
    return this.client.get(key);
  }

  async del(key) {
    if (!this.client || !this.client.isReady) {
      throw new Error('Redis not connected');
    }
    return this.client.del(key);
  }

  async ping() {
    if (!this.client || !this.client.isReady) {
      throw new Error('Redis not connected');
    }
    return this.client.ping();
  }
}

// Export singleton instance
const redisManager = new RedisManager();
module.exports = redisManager;
