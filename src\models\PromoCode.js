// PromoCode Model
const BaseModel = require('./BaseModel');

class PromoCode extends BaseModel {
  constructor() {
    super('promo_codes');
  }

  // Find promo code by code
  async findByCode(code) {
    try {
      return await this.findOne({ code: code.toUpperCase() });
    } catch (error) {
      throw error;
    }
  }

  // Validate promo code for user and order
  async validatePromoCode(code, userId, orderAmount) {
    try {
      const promoCode = await this.findByCode(code);
      
      if (!promoCode) {
        return {
          isValid: false,
          error: 'Promo code not found'
        };
      }

      // Check if promo code is active
      if (!promoCode.is_active) {
        return {
          isValid: false,
          error: 'Promo code is no longer active'
        };
      }

      // Check if promo code has started
      if (promoCode.starts_at && new Date() < new Date(promoCode.starts_at)) {
        return {
          isValid: false,
          error: 'Promo code is not yet active'
        };
      }

      // Check if promo code has expired
      if (promoCode.expires_at && new Date() > new Date(promoCode.expires_at)) {
        return {
          isValid: false,
          error: 'Promo code has expired'
        };
      }

      // Check usage limit
      if (promoCode.usage_limit && promoCode.usage_count >= promoCode.usage_limit) {
        return {
          isValid: false,
          error: 'Promo code usage limit reached'
        };
      }

      // Check minimum order amount
      if (promoCode.minimum_order && orderAmount < promoCode.minimum_order) {
        return {
          isValid: false,
          error: `Minimum order amount of $${promoCode.minimum_order} required`
        };
      }

      // Check user usage limit
      if (promoCode.user_limit) {
        const userUsageCount = await this.getUserUsageCount(promoCode.id, userId);
        if (userUsageCount >= promoCode.user_limit) {
          return {
            isValid: false,
            error: 'You have reached the usage limit for this promo code'
          };
        }
      }

      // Calculate discount amount
      const discountAmount = this.calculateDiscount(promoCode, orderAmount);

      return {
        isValid: true,
        promoCode,
        discountAmount,
        discountType: promoCode.type
      };
    } catch (error) {
      throw error;
    }
  }

  // Calculate discount amount
  calculateDiscount(promoCode, orderAmount) {
    let discountAmount = 0;

    switch (promoCode.type) {
      case 'percentage':
        discountAmount = (orderAmount * promoCode.value) / 100;
        break;
      case 'fixed_amount':
        discountAmount = promoCode.value;
        break;
      case 'free_delivery':
        // This would be handled separately in order calculation
        discountAmount = 0;
        break;
      default:
        discountAmount = 0;
    }

    // Apply maximum discount limit if set
    if (promoCode.maximum_discount && discountAmount > promoCode.maximum_discount) {
      discountAmount = promoCode.maximum_discount;
    }

    // Ensure discount doesn't exceed order amount
    if (discountAmount > orderAmount) {
      discountAmount = orderAmount;
    }

    return Math.round(discountAmount * 100) / 100; // Round to 2 decimal places
  }

  // Get user usage count for a promo code
  async getUserUsageCount(promoCodeId, userId) {
    try {
      const result = await this.query(
        'SELECT COUNT(*) as count FROM promo_code_usage WHERE promo_code_id = $1 AND user_id = $2',
        [promoCodeId, userId]
      );

      return parseInt(result.rows[0].count);
    } catch (error) {
      throw error;
    }
  }

  // Apply promo code to order
  async applyPromoCode(promoCodeId, userId, orderId, discountAmount) {
    try {
      const client = await this.beginTransaction();

      try {
        // Record promo code usage
        await client.query(
          'INSERT INTO promo_code_usage (promo_code_id, user_id, order_id, discount_amount) VALUES ($1, $2, $3, $4)',
          [promoCodeId, userId, orderId, discountAmount]
        );

        // Increment usage count
        await client.query(
          'UPDATE promo_codes SET usage_count = usage_count + 1 WHERE id = $1',
          [promoCodeId]
        );

        await this.commitTransaction(client);
        return true;
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Create promo code
  async createPromoCode(promoData) {
    try {
      // Validate promo code data
      const validation = this.validatePromoCodeData(promoData);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // Ensure code is uppercase
      promoData.code = promoData.code.toUpperCase();

      // Check if code already exists
      const existing = await this.findByCode(promoData.code);
      if (existing) {
        throw new Error('Promo code already exists');
      }

      return await this.create(promoData);
    } catch (error) {
      throw error;
    }
  }

  // Update promo code
  async updatePromoCode(promoCodeId, updateData) {
    try {
      // Validate update data
      const validation = this.validatePromoCodeData(updateData, true);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // Ensure code is uppercase if being updated
      if (updateData.code) {
        updateData.code = updateData.code.toUpperCase();

        // Check if new code already exists
        const existing = await this.query(
          'SELECT id FROM promo_codes WHERE code = $1 AND id != $2',
          [updateData.code, promoCodeId]
        );

        if (existing.rows.length > 0) {
          throw new Error('Promo code already exists');
        }
      }

      return await this.updateById(promoCodeId, updateData);
    } catch (error) {
      throw error;
    }
  }

  // Validate promo code data
  validatePromoCodeData(promoData, isUpdate = false) {
    const errors = [];

    // Validate required fields for creation
    if (!isUpdate) {
      if (!promoData.code || promoData.code.trim() === '') {
        errors.push('Promo code is required');
      }

      if (!promoData.name || promoData.name.trim() === '') {
        errors.push('Promo code name is required');
      }

      if (!promoData.type) {
        errors.push('Promo code type is required');
      }

      if (promoData.value === undefined || promoData.value === null) {
        errors.push('Promo code value is required');
      }
    }

    // Validate code format
    if (promoData.code) {
      if (!/^[A-Z0-9]{3,20}$/.test(promoData.code.toUpperCase())) {
        errors.push('Promo code must be 3-20 characters long and contain only letters and numbers');
      }
    }

    // Validate type
    if (promoData.type) {
      const validTypes = ['percentage', 'fixed_amount', 'free_delivery'];
      if (!validTypes.includes(promoData.type)) {
        errors.push('Invalid promo code type');
      }
    }

    // Validate value based on type
    if (promoData.type && promoData.value !== undefined) {
      if (promoData.type === 'percentage') {
        if (promoData.value <= 0 || promoData.value > 100) {
          errors.push('Percentage value must be between 1 and 100');
        }
      } else if (promoData.type === 'fixed_amount') {
        if (promoData.value <= 0) {
          errors.push('Fixed amount must be greater than 0');
        }
      }
    }

    // Validate dates
    if (promoData.starts_at && promoData.expires_at) {
      if (new Date(promoData.starts_at) >= new Date(promoData.expires_at)) {
        errors.push('Start date must be before expiry date');
      }
    }

    // Validate limits
    if (promoData.usage_limit !== undefined && promoData.usage_limit < 0) {
      errors.push('Usage limit cannot be negative');
    }

    if (promoData.user_limit !== undefined && promoData.user_limit < 0) {
      errors.push('User limit cannot be negative');
    }

    if (promoData.minimum_order !== undefined && promoData.minimum_order < 0) {
      errors.push('Minimum order cannot be negative');
    }

    if (promoData.maximum_discount !== undefined && promoData.maximum_discount < 0) {
      errors.push('Maximum discount cannot be negative');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get active promo codes
  async getActivePromoCodes(page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      const now = new Date();

      const query = `
        SELECT *
        FROM promo_codes
        WHERE is_active = true
        AND (starts_at IS NULL OR starts_at <= $1)
        AND (expires_at IS NULL OR expires_at > $1)
        AND (usage_limit IS NULL OR usage_count < usage_limit)
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
      `;

      const countQuery = `
        SELECT COUNT(*) as count
        FROM promo_codes
        WHERE is_active = true
        AND (starts_at IS NULL OR starts_at <= $1)
        AND (expires_at IS NULL OR expires_at > $1)
        AND (usage_limit IS NULL OR usage_count < usage_limit)
      `;

      const [results, countResult] = await Promise.all([
        this.query(query, [now, limit, offset]),
        this.query(countQuery, [now])
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        promoCodes: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get promo code usage statistics
  async getPromoCodeStats(promoCodeId, days = 30) {
    try {
      const query = `
        SELECT 
          pc.code,
          pc.name,
          pc.type,
          pc.value,
          pc.usage_count,
          pc.usage_limit,
          COALESCE(recent_usage.recent_count, 0) as recent_usage_count,
          COALESCE(recent_usage.total_discount, 0) as total_discount_given
        FROM promo_codes pc
        LEFT JOIN (
          SELECT 
            promo_code_id,
            COUNT(*) as recent_count,
            SUM(discount_amount) as total_discount
          FROM promo_code_usage
          WHERE created_at >= NOW() - INTERVAL '${days} days'
          GROUP BY promo_code_id
        ) recent_usage ON pc.id = recent_usage.promo_code_id
        WHERE pc.id = $1
      `;

      const result = await this.query(query, [promoCodeId]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Deactivate expired promo codes
  async deactivateExpiredPromoCodes() {
    try {
      const result = await this.query(
        'UPDATE promo_codes SET is_active = false WHERE expires_at < NOW() AND is_active = true RETURNING id, code',
        []
      );

      return result.rows;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = PromoCode;
