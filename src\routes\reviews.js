// Review Routes
const express = require('express');
const ReviewController = require('../controllers/reviewController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting to all routes
router.use(apiLimiter);

// Public routes (no authentication required)

// GET /api/v1/reviews/restaurants/:restaurantId - Get restaurant reviews
router.get('/restaurants/:restaurantId',
  ValidationMiddleware.validateUUID('restaurantId'),
  ValidationMiddleware.validateReviewQuery(),
  ReviewController.getRestaurantReviews
);

// GET /api/v1/reviews/menu-items/:menuItemId - Get menu item reviews
router.get('/menu-items/:menuItemId',
  ValidationMiddleware.validateUUID('menuItemId'),
  ValidationMiddleware.validateReviewQuery(),
  ReviewController.getMenuItemReviews
);

// GET /api/v1/reviews/restaurants/:restaurantId/stats - Get restaurant review statistics
router.get('/restaurants/:restaurantId/stats',
  ValidationMiddleware.validateUUID('restaurantId'),
  ReviewController.getRestaurantReviewStats
);

// Protected routes (authentication required)
router.use(AuthMiddleware.authenticate);

// GET /api/v1/reviews/user - Get user's reviews
router.get('/user',
  ReviewController.getUserReviews
);

// POST /api/v1/reviews - Create a review
router.post('/',
  ValidationMiddleware.validateCreateReview(),
  ReviewController.createReview
);

// PUT /api/v1/reviews/:id - Update a review
router.put('/:id',
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateUpdateReview(),
  ReviewController.updateReview
);

// DELETE /api/v1/reviews/:id - Delete a review
router.delete('/:id',
  ValidationMiddleware.validateUUID('id'),
  ReviewController.deleteReview
);

// POST /api/v1/reviews/:id/helpful - Mark review as helpful
router.post('/:id/helpful',
  ValidationMiddleware.validateUUID('id'),
  ReviewController.markReviewHelpful
);

// Admin routes (require admin privileges)

// POST /api/v1/reviews/:id/response - Add restaurant response to review (admin only)
router.post('/:id/response',
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateRestaurantResponse(),
  ReviewController.addRestaurantResponse
);

module.exports = router;
