# FoodWay Backend - Test Environment Configuration
# Environment variables for testing

# ================================
# SERVER CONFIGURATION
# ================================
NODE_ENV=test
PORT=5001

# ================================
# DATABASE CONFIGURATION
# ================================
# Use the same Railway PostgreSQL for testing (you may want to use a separate test database)
DATABASE_URL=postgresql://postgres:<EMAIL>:14736/railway

# ================================
# REDIS CONFIGURATION
# ================================
# Use the same Railway Redis for testing (you may want to use a separate test Redis)
REDIS_URL=redis://default:<EMAIL>:21883

# ================================
# AUTHENTICATION & SECURITY
# ================================
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=1h
BCRYPT_SALT_ROUNDS=4

# ================================
# APPLICATION URLS
# ================================
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:5001

# ================================
# DEVELOPMENT/DEBUG
# ================================
DEBUG=false
LOG_LEVEL=error
