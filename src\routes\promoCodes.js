// Promo Code Routes
const express = require('express');
const PromoCodeController = require('../controllers/promoCodeController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply rate limiting to all routes
router.use(apiLimiter);

// Public routes (no authentication required)

// GET /api/v1/promo-codes/active - Get active promo codes
router.get('/active',
  PromoCodeController.getActivePromoCodes
);

// Protected routes (authentication required)
router.use(AuthMiddleware.authenticate);

// POST /api/v1/promo-codes/validate - Validate promo code
router.post('/validate',
  ValidationMiddleware.validatePromoCodeValidation(),
  PromoCodeController.validatePromoCode
);

// POST /api/v1/promo-codes/apply - Apply promo code to order
router.post('/apply',
  ValidationMiddleware.validateApplyPromoCode(),
  PromoCodeController.applyPromoCode
);

// Admin routes (require admin privileges)

// GET /api/v1/promo-codes - Get all promo codes (admin only)
router.get('/',
  AuthMiddleware.requireAdmin,
  PromoCodeController.getAllPromoCodes
);

// GET /api/v1/promo-codes/:id - Get promo code by ID (admin only)
router.get('/:id',
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  PromoCodeController.getPromoCodeById
);

// GET /api/v1/promo-codes/:id/stats - Get promo code statistics (admin only)
router.get('/:id/stats',
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  PromoCodeController.getPromoCodeStats
);

// POST /api/v1/promo-codes - Create promo code (admin only)
router.post('/',
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateCreatePromoCode(),
  PromoCodeController.createPromoCode
);

// PUT /api/v1/promo-codes/:id - Update promo code (admin only)
router.put('/:id',
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateUpdatePromoCode(),
  PromoCodeController.updatePromoCode
);

// DELETE /api/v1/promo-codes/:id - Delete promo code (admin only)
router.delete('/:id',
  AuthMiddleware.requireAdmin,
  ValidationMiddleware.validateUUID('id'),
  PromoCodeController.deletePromoCode
);

// POST /api/v1/promo-codes/deactivate-expired - Deactivate expired promo codes (admin only)
router.post('/deactivate-expired',
  AuthMiddleware.requireAdmin,
  PromoCodeController.deactivateExpiredPromoCodes
);

module.exports = router;
