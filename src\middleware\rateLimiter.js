// Rate Limiting Middleware
const rateLimit = require('express-rate-limit');
const ResponseHelper = require('../utils/response');

// Custom rate limit handler
const rateLimitHandler = (req, res) => {
  return ResponseHelper.error(res, 'Too many requests', 429, {
    code: 'RATE_LIMITED',
    message: 'Too many requests from this IP, please try again later'
  });
};

// Authentication endpoints rate limiter (stricter)
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 requests per window
  message: 'Too many authentication attempts',
  standardHeaders: true,
  legacyHeaders: false,
  handler: rateLimit<PERSON>and<PERSON>,
  skip: (req) => {
    // Skip rate limiting in test and development environments
    return process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'development';
  }
});

// General API rate limiter
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window per IP
  message: 'Too many API requests',
  standardHeaders: true,
  legacyHeaders: false,
  handler: rateLimit<PERSON><PERSON><PERSON>,
  skip: (req) => {
    // Skip rate limiting in test and development environments
    return process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'development';
  }
});

// Search endpoints rate limiter
const searchLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 30, // 30 requests per window
  message: 'Too many search requests',
  standardHeaders: true,
  legacyHeaders: false,
  handler: rateLimitHandler,
  skip: (req) => {
    // Skip rate limiting in test and development environments
    return process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'development';
  }
});

// Order creation rate limiter
const orderLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 orders per window
  message: 'Too many order attempts',
  standardHeaders: true,
  legacyHeaders: false,
  handler: rateLimitHandler,
  skip: (req) => {
    // Skip rate limiting in test and development environments
    return process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'development';
  }
});

module.exports = {
  authLimiter,
  apiLimiter,
  searchLimiter,
  orderLimiter
};
