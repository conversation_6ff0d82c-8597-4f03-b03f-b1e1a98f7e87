// Authentication Middleware
const jwt = require('jsonwebtoken');
const config = require('../config/app');
const ResponseHelper = require('../utils/response');
const databaseManager = require('../config/database');

class AuthMiddleware {
  // Verify JWT token and attach user to request
  static async authenticate(req, res, next) {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return ResponseHelper.error(res, 'Authentication required', 401, {
          code: 'UNAUTHORIZED',
          message: 'Missing or invalid authorization header'
        });
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      if (!token) {
        return ResponseHelper.error(res, 'Authentication required', 401, {
          code: 'UNAUTHORIZED',
          message: 'No token provided'
        });
      }

      // Verify JWT token
      const decoded = jwt.verify(token, config.jwt.secret);

      // Get user ID from token (support both old and new format)
      const userId = decoded.sub || decoded.userId;

      // Get user from database
      const pool = databaseManager.getPool();
      const userResult = await pool.query(
        'SELECT id, email, first_name, last_name, phone, avatar_url, email_verified, phone_verified, is_active FROM users WHERE id = $1',
        [userId]
      );

      if (userResult.rows.length === 0) {
        return ResponseHelper.error(res, 'User not found', 401, {
          code: 'UNAUTHORIZED',
          message: 'Invalid token - user does not exist'
        });
      }

      const user = userResult.rows[0];

      if (!user.is_active) {
        return ResponseHelper.error(res, 'Account deactivated', 401, {
          code: 'UNAUTHORIZED',
          message: 'Your account has been deactivated'
        });
      }

      // Attach user to request object
      req.user = user;
      next();

    } catch (error) {
      if (error.name === 'JsonWebTokenError') {
        return ResponseHelper.error(res, 'Invalid token', 401, {
          code: 'UNAUTHORIZED',
          message: 'Invalid or malformed token'
        });
      }

      if (error.name === 'TokenExpiredError') {
        return ResponseHelper.error(res, 'Token expired', 401, {
          code: 'UNAUTHORIZED',
          message: 'Token has expired'
        });
      }

      console.error('Authentication error:', error);
      return ResponseHelper.error(res, 'Authentication failed', 500);
    }
  }

  // Optional authentication - doesn't fail if no token provided
  static async optionalAuth(req, res, next) {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return next(); // Continue without user
      }

      const token = authHeader.substring(7);

      if (!token) {
        return next(); // Continue without user
      }

      // Verify JWT token
      const decoded = jwt.verify(token, config.jwt.secret);

      // Get user ID from token (support both old and new format)
      const userId = decoded.sub || decoded.userId;

      // Get user from database
      const pool = databaseManager.getPool();
      const userResult = await pool.query(
        'SELECT id, email, first_name, last_name, phone, avatar_url, email_verified, phone_verified, is_active FROM users WHERE id = $1',
        [userId]
      );

      if (userResult.rows.length > 0 && userResult.rows[0].is_active) {
        req.user = userResult.rows[0];
      }

      next();

    } catch (error) {
      // Silently continue without user if token is invalid
      next();
    }
  }

  // Require email verification
  static requireEmailVerification(req, res, next) {
    if (!req.user) {
      return ResponseHelper.error(res, 'Authentication required', 401);
    }

    if (!req.user.email_verified) {
      return ResponseHelper.error(res, 'Email verification required', 403, {
        code: 'EMAIL_NOT_VERIFIED',
        message: 'Please verify your email address to access this resource'
      });
    }

    next();
  }

  // Generate JWT token
  static generateToken(userId, email, role = 'customer') {
    const now = Math.floor(Date.now() / 1000);
    const expiresIn = config.jwt.expiresIn;

    // Calculate expiration time in seconds
    let exp;
    if (expiresIn.endsWith('d')) {
      exp = now + (parseInt(expiresIn) * 24 * 60 * 60);
    } else if (expiresIn.endsWith('h')) {
      exp = now + (parseInt(expiresIn) * 60 * 60);
    } else if (expiresIn.endsWith('m')) {
      exp = now + (parseInt(expiresIn) * 60);
    } else {
      exp = now + parseInt(expiresIn);
    }

    return jwt.sign(
      {
        sub: userId,
        email: email,
        role: role,
        iat: now,
        exp: exp,
        // Keep userId for backward compatibility
        userId: userId
      },
      config.jwt.secret
    );
  }

  // Generate refresh token
  static generateRefreshToken() {
    return jwt.sign(
      { type: 'refresh', timestamp: Date.now() },
      config.jwt.secret,
      { expiresIn: '30d' }
    );
  }

  // Verify refresh token
  static verifyRefreshToken(token) {
    try {
      const decoded = jwt.verify(token, config.jwt.secret);
      return decoded.type === 'refresh' ? decoded : null;
    } catch (error) {
      return null;
    }
  }

  // Require admin role (placeholder - implement based on your user role system)
  static requireAdmin(req, res, next) {
    if (!req.user) {
      return ResponseHelper.error(res, 'Authentication required', 401);
    }

    // For now, check if user email contains 'admin' or implement proper role checking
    // In a real application, you would check user.role === 'admin' or similar
    const isAdmin = req.user.email.includes('admin') || req.user.role === 'admin';

    if (!isAdmin) {
      return ResponseHelper.error(res, 'Admin access required', 403, {
        code: 'FORBIDDEN',
        message: 'This action requires administrator privileges'
      });
    }

    next();
  }
}

module.exports = AuthMiddleware;
