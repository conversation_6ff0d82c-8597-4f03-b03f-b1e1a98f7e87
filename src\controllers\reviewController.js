// Review Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const Review = require('../models/Review');

class ReviewController {
  // Get restaurant reviews
  static async getRestaurantReviews(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.restaurantId;
      const filters = {
        rating: req.query.rating ? parseInt(req.query.rating) : null,
        page: parseInt(req.query.page) || 1,
        limit: Math.min(parseInt(req.query.limit) || 20, 50),
        sortBy: req.query.sortBy || 'created_at',
        sortOrder: req.query.sortOrder || 'DESC'
      };

      const reviewModel = new Review();
      const result = await reviewModel.getRestaurantReviews(restaurantId, filters);

      return ResponseHelper.success(res, result, 'Restaurant reviews retrieved successfully');
    } catch (error) {
      console.error('Get restaurant reviews error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve restaurant reviews', 500);
    }
  }

  // Get menu item reviews
  static async getMenuItemReviews(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const menuItemId = req.params.menuItemId;
      const filters = {
        rating: req.query.rating ? parseInt(req.query.rating) : null,
        page: parseInt(req.query.page) || 1,
        limit: Math.min(parseInt(req.query.limit) || 20, 50)
      };

      const reviewModel = new Review();
      const result = await reviewModel.getMenuItemReviews(menuItemId, filters);

      return ResponseHelper.success(res, result, 'Menu item reviews retrieved successfully');
    } catch (error) {
      console.error('Get menu item reviews error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve menu item reviews', 500);
    }
  }

  // Create a review
  static async createReview(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;

      // Normalize field names from frontend format to backend format
      const reviewData = {
        ...req.body,
        user_id: userId,
        restaurant_id: req.body.restaurantId || req.body.restaurant_id,
        order_id: req.body.orderId || req.body.order_id,
        menu_item_id: req.body.menuItemId || req.body.menu_item_id,
        food_rating: req.body.foodRating || req.body.food_rating,
        service_rating: req.body.serviceRating || req.body.service_rating,
        delivery_rating: req.body.deliveryRating || req.body.delivery_rating
      };

      // Remove frontend field names to avoid conflicts
      delete reviewData.restaurantId;
      delete reviewData.orderId;
      delete reviewData.menuItemId;
      delete reviewData.foodRating;
      delete reviewData.serviceRating;
      delete reviewData.deliveryRating;

      const reviewModel = new Review();

      // Validate review data
      const validation = reviewModel.validateReviewData(reviewData);
      if (!validation.isValid) {
        return ResponseHelper.error(res, validation.errors.join(', '), 400, {
          code: 'VALIDATION_ERROR',
          errors: validation.errors
        });
      }

      const review = await reviewModel.createReview(reviewData);

      return ResponseHelper.success(res, review, 'Review created successfully', 201);
    } catch (error) {
      console.error('Create review error:', error);

      if (error.message.includes('already reviewed')) {
        return ResponseHelper.error(res, error.message, 409);
      }

      if (error.message.includes('not found') || error.message.includes('not eligible')) {
        return ResponseHelper.error(res, error.message, 400);
      }

      return ResponseHelper.error(res, 'Failed to create review', 500);
    }
  }

  // Update a review
  static async updateReview(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const reviewId = req.params.id;
      const updateData = req.body;
      const reviewModel = new Review();

      const review = await reviewModel.updateReview(reviewId, userId, updateData);

      return ResponseHelper.success(res, review, 'Review updated successfully');
    } catch (error) {
      console.error('Update review error:', error);

      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to update review', 500);
    }
  }

  // Delete a review
  static async deleteReview(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const reviewId = req.params.id;
      const reviewModel = new Review();

      const result = await reviewModel.deleteReview(reviewId, userId);

      return ResponseHelper.success(res, { deleted: true }, 'Review deleted successfully');
    } catch (error) {
      console.error('Delete review error:', error);

      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to delete review', 500);
    }
  }

  // Get user's reviews
  static async getUserReviews(req, res) {
    try {
      const userId = req.user.id;
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);
      const reviewModel = new Review();

      const result = await reviewModel.getUserReviews(userId, page, limit);

      return ResponseHelper.success(res, result, 'User reviews retrieved successfully');
    } catch (error) {
      console.error('Get user reviews error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve user reviews', 500);
    }
  }

  // Get restaurant review statistics
  static async getRestaurantReviewStats(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.restaurantId;
      const reviewModel = new Review();

      const stats = await reviewModel.getRestaurantReviewStats(restaurantId);

      return ResponseHelper.success(res, stats, 'Restaurant review statistics retrieved successfully');
    } catch (error) {
      console.error('Get restaurant review stats error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve restaurant review statistics', 500);
    }
  }

  // Mark review as helpful
  static async markReviewHelpful(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const reviewId = req.params.id;
      const reviewModel = new Review();

      const helpfulCount = await reviewModel.markReviewHelpful(reviewId);

      return ResponseHelper.success(res, { helpfulCount }, 'Review marked as helpful successfully');
    } catch (error) {
      console.error('Mark review helpful error:', error);

      if (error.message.includes('not found')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to mark review as helpful', 500);
    }
  }

  // Add restaurant response to review (admin only)
  static async addRestaurantResponse(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const reviewId = req.params.id;
      const { response } = req.body;
      const reviewModel = new Review();

      const review = await reviewModel.addRestaurantResponse(reviewId, response);

      return ResponseHelper.success(res, review, 'Restaurant response added successfully');
    } catch (error) {
      console.error('Add restaurant response error:', error);

      if (error.message.includes('not found')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to add restaurant response', 500);
    }
  }
}

module.exports = ReviewController;
