// PromoCode Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const PromoCode = require('../models/PromoCode');

class PromoCodeController {
  // Validate promo code
  static async validatePromoCode(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { code, orderAmount } = req.body;
      const userId = req.user.id;
      const promoCodeModel = new PromoCode();

      const validation = await promoCodeModel.validatePromoCode(code, userId, orderAmount);

      if (!validation.isValid) {
        return ResponseHelper.error(res, validation.error, 400, {
          code: 'INVALID_PROMO_CODE',
          message: validation.error
        });
      }

      return ResponseHelper.success(res, {
        isValid: true,
        promoCode: {
          id: validation.promoCode.id,
          code: validation.promoCode.code,
          name: validation.promoCode.name,
          type: validation.promoCode.type,
          value: validation.promoCode.value,
          discountAmount: validation.discountAmount,
          minimumOrder: validation.promoCode.minimum_order,
          maximumDiscount: validation.promoCode.maximum_discount
        }
      }, 'Promo code is valid');
    } catch (error) {
      console.error('Validate promo code error:', error);
      return ResponseHelper.error(res, 'Failed to validate promo code', 500);
    }
  }

  // Get active promo codes (public)
  static async getActivePromoCodes(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);
      const promoCodeModel = new PromoCode();

      const result = await promoCodeModel.getActivePromoCodes(page, limit);

      return ResponseHelper.success(res, result, 'Active promo codes retrieved successfully');
    } catch (error) {
      console.error('Get active promo codes error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve active promo codes', 500);
    }
  }

  // Create promo code (admin only)
  static async createPromoCode(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const promoData = req.body;
      const promoCodeModel = new PromoCode();

      const promoCode = await promoCodeModel.createPromoCode(promoData);

      return ResponseHelper.success(res, promoCode, 'Promo code created successfully', 201);
    } catch (error) {
      console.error('Create promo code error:', error);
      
      if (error.message.includes('already exists')) {
        return ResponseHelper.error(res, error.message, 409);
      }

      return ResponseHelper.error(res, 'Failed to create promo code', 500);
    }
  }

  // Update promo code (admin only)
  static async updatePromoCode(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const promoCodeId = req.params.id;
      const updateData = req.body;
      const promoCodeModel = new PromoCode();

      const promoCode = await promoCodeModel.updatePromoCode(promoCodeId, updateData);

      if (!promoCode) {
        return ResponseHelper.error(res, 'Promo code not found', 404, {
          code: 'NOT_FOUND',
          message: 'Promo code not found'
        });
      }

      return ResponseHelper.success(res, promoCode, 'Promo code updated successfully');
    } catch (error) {
      console.error('Update promo code error:', error);
      
      if (error.message.includes('already exists')) {
        return ResponseHelper.error(res, error.message, 409);
      }

      return ResponseHelper.error(res, 'Failed to update promo code', 500);
    }
  }

  // Delete promo code (admin only)
  static async deletePromoCode(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const promoCodeId = req.params.id;
      const promoCodeModel = new PromoCode();

      const result = await promoCodeModel.deleteById(promoCodeId);

      if (!result) {
        return ResponseHelper.error(res, 'Promo code not found', 404, {
          code: 'NOT_FOUND',
          message: 'Promo code not found'
        });
      }

      return ResponseHelper.success(res, { deleted: true }, 'Promo code deleted successfully');
    } catch (error) {
      console.error('Delete promo code error:', error);
      return ResponseHelper.error(res, 'Failed to delete promo code', 500);
    }
  }

  // Get all promo codes (admin only)
  static async getAllPromoCodes(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);
      const promoCodeModel = new PromoCode();

      const result = await promoCodeModel.findAllWithPagination({}, page, limit, 'created_at DESC');

      return ResponseHelper.success(res, result, 'All promo codes retrieved successfully');
    } catch (error) {
      console.error('Get all promo codes error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve promo codes', 500);
    }
  }

  // Get promo code by ID (admin only)
  static async getPromoCodeById(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const promoCodeId = req.params.id;
      const promoCodeModel = new PromoCode();

      const promoCode = await promoCodeModel.findById(promoCodeId);

      if (!promoCode) {
        return ResponseHelper.error(res, 'Promo code not found', 404, {
          code: 'NOT_FOUND',
          message: 'Promo code not found'
        });
      }

      return ResponseHelper.success(res, promoCode, 'Promo code retrieved successfully');
    } catch (error) {
      console.error('Get promo code by ID error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve promo code', 500);
    }
  }

  // Get promo code statistics (admin only)
  static async getPromoCodeStats(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const promoCodeId = req.params.id;
      const days = parseInt(req.query.days) || 30;
      const promoCodeModel = new PromoCode();

      const stats = await promoCodeModel.getPromoCodeStats(promoCodeId, days);

      if (!stats) {
        return ResponseHelper.error(res, 'Promo code not found', 404, {
          code: 'NOT_FOUND',
          message: 'Promo code not found'
        });
      }

      return ResponseHelper.success(res, stats, 'Promo code statistics retrieved successfully');
    } catch (error) {
      console.error('Get promo code stats error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve promo code statistics', 500);
    }
  }

  // Deactivate expired promo codes (admin only)
  static async deactivateExpiredPromoCodes(req, res) {
    try {
      const promoCodeModel = new PromoCode();
      const deactivatedCodes = await promoCodeModel.deactivateExpiredPromoCodes();

      return ResponseHelper.success(res, {
        deactivatedCount: deactivatedCodes.length,
        deactivatedCodes
      }, 'Expired promo codes deactivated successfully');
    } catch (error) {
      console.error('Deactivate expired promo codes error:', error);
      return ResponseHelper.error(res, 'Failed to deactivate expired promo codes', 500);
    }
  }

  // Apply promo code to order (internal use)
  static async applyPromoCode(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { promoCodeId, orderId, discountAmount } = req.body;
      const userId = req.user.id;
      const promoCodeModel = new PromoCode();

      const result = await promoCodeModel.applyPromoCode(promoCodeId, userId, orderId, discountAmount);

      return ResponseHelper.success(res, { applied: result }, 'Promo code applied successfully');
    } catch (error) {
      console.error('Apply promo code error:', error);
      return ResponseHelper.error(res, 'Failed to apply promo code', 500);
    }
  }
}

module.exports = PromoCodeController;
