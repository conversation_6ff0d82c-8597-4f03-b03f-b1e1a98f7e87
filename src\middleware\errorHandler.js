// Error Handling Middleware
const config = require('../config/app');

// 404 handler
const notFoundHandler = (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl,
    method: req.method
  });
};

// Global error handler
const errorHandler = (error, req, res, next) => {
  console.error('Error:', error);
  
  // Default error response
  const errorResponse = {
    error: 'Internal server error',
    message: config.isDevelopment ? error.message : 'Something went wrong'
  };

  // Handle specific error types
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Validation error',
      details: error.details || error.message
    });
  }

  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid or missing authentication token'
    });
  }

  if (error.code === '23505') { // PostgreSQL unique violation
    return res.status(409).json({
      error: 'Conflict',
      message: 'Resource already exists'
    });
  }

  if (error.code === '23503') { // PostgreSQL foreign key violation
    return res.status(400).json({
      error: 'Bad request',
      message: 'Referenced resource does not exist'
    });
  }

  // Default 500 error
  res.status(500).json(errorResponse);
};

module.exports = {
  notFoundHandler,
  errorHandler
};
