# FoodWay Database Schema Migration Updates

## Overview
This document outlines the updates made to the database migration script (`scripts/migrate.js`) to align with the comprehensive schema specification in `BACKEND_GUIDE_FOR_RESTAURANT_APP.md`.

## Tables Updated/Fixed

### 1. Users Table
**Changes:**
- Changed `avatar_url` to `avatar` (TEXT)
- Added `date_of_birth` (DATE)
- Added `preferences` (JSONB DEFAULT '{}')

### 2. User Addresses Table
**Changes:**
- Changed `type` and `label` structure - now `label` is required
- Changed `street_address` to `street`
- Added `apartment` field
- Changed `postal_code` to `zip_code`
- Changed `country` from VARCHAR(2) to VARCHAR(100) with default 'USA'
- Added `delivery_instructions` (TEXT)
- Added `is_active` (BOOLEAN DEFAULT TRUE)

### 3. Payment Methods Table
**Changes:**
- Removed `stripe_payment_method_id` (made generic)
- Added `provider` (VARCHAR(100))
- Added `external_id` (VARCHAR(255))
- Changed `exp_month/exp_year` to `expiry_month/expiry_year`
- Added `cardholder_name` (VARCHAR(255))
- Added `is_active` (BOOLEAN DEFAULT TRUE)

### 4. Restaurants Table
**Major restructure:**
- Changed `cuisine_type` to `cuisine_types` (TEXT[] array)
- Added `image`, `logo`, `slug` (unique)
- Changed `website_url` to `website`
- Removed individual address fields, replaced with `address` (JSONB)
- Added `max_delivery_distance`, `estimated_delivery_time`
- Added `accepting_orders`, `features` (TEXT[] array)
- Added `opening_hours` (JSONB), `social_media` (JSONB)
- Removed `price_range`, `delivery_time_min/max`, `delivery_radius`
- Removed `is_open`, `is_featured`

### 5. Menu Categories Table
**Changes:**
- Added `image` (TEXT)

### 6. Menu Items Table
**Changes:**
- Made `category_id` NOT NULL with CASCADE delete
- Changed `image_url` to `image`
- Added `ingredients` (TEXT[] array)
- Changed `calories` to `nutrition_info` (JSONB)
- Changed `prep_time` to `preparation_time`
- Added `is_popular` (BOOLEAN)

### 7. Menu Item Customizations Table
**Changes:**
- Added `description` (TEXT)
- Added `allow_multiple`, `min_selections`, `max_selections`
- Added `updated_at` timestamp

### 8. Customization Options Table
**Renamed and restructured:**
- Renamed from `menu_item_customization_options` to `customization_options`
- Added `description` (TEXT)
- Changed `price` to `price_modifier`
- Added `is_available` (BOOLEAN)
- Added `updated_at` timestamp

## New Tables Added

### 9. Order Status History Table
**Purpose:** Track order status changes over time
**Fields:**
- `id`, `order_id`, `status`, `note`, `changed_by`, `created_at`

### 10. User Favorites Table
**Purpose:** Store user's favorite restaurants and menu items
**Fields:**
- `id`, `user_id`, `restaurant_id`, `menu_item_id`, `created_at`
- Constraint: Either restaurant_id OR menu_item_id must be set

### 11. User Sessions Table
**Purpose:** Manage user authentication sessions (Redis alternative)
**Fields:**
- `id`, `user_id`, `refresh_token`, `device_info`, `ip_address`
- `user_agent`, `is_active`, `expires_at`, `created_at`, `last_used_at`

### 12. Promo Codes Table
**Purpose:** Manage discount codes and promotions
**Fields:**
- `id`, `code`, `name`, `description`, `type`, `value`
- `minimum_order`, `maximum_discount`, `usage_limit`, `usage_count`
- `user_limit`, `is_active`, `starts_at`, `expires_at`

### 13. Promo Code Usage Table
**Purpose:** Track promo code usage by users
**Fields:**
- `id`, `promo_code_id`, `user_id`, `order_id`, `discount_amount`, `created_at`

## Updated Existing Tables

### Orders Table
**Changes:**
- Added `discount` field
- Restructured payment fields
- Added detailed timestamp fields for order lifecycle
- Added `cancellation_reason`

### Order Items Table
**Changes:**
- Removed `addons` (consolidated into customizations)
- Added `updated_at` timestamp

### Reviews Table
**Changes:**
- Added `menu_item_id` for item-specific reviews
- Added `title`, `helpful_count`, `response`, `response_date`
- Added `is_active` for soft deletes
- Removed unique constraint on user_id + order_id

## Database Functions & Triggers Added

### 1. Order Number Generation
- Sequence: `order_number_seq`
- Function: `generate_order_number()`
- Trigger: Auto-generates order numbers like 'ORD-000001'

### 2. Restaurant Rating Updates
- Function: `update_restaurant_rating()`
- Trigger: Auto-updates restaurant rating when reviews change

### 3. Order Status History Tracking
- Function: `update_order_status_history()`
- Trigger: Auto-tracks status changes and updates timestamps

## Analytics Views Added

### 1. Order Analytics View
- Daily order statistics
- Completion rates, revenue tracking

### 2. Popular Menu Items View
- Item popularity based on orders
- Includes ratings and review counts

## Indexes Added/Updated

### New Indexes:
- Restaurant slug, cuisine types (GIN), accepting orders
- Menu items: popular, dietary preferences
- Customization tables
- Order payment status, order number
- Reviews: menu items, verification status
- Favorites: user, restaurant, menu item relationships
- Sessions: refresh tokens, expiration
- Promo codes: code lookup, expiration
- Push tokens: platform, device uniqueness

### Unique Constraints:
- Reviews: one per user per order
- Favorites: unique user-restaurant and user-menu-item pairs
- Promo usage: unique per user per order
- Push tokens: unique per device

## Sample Data
- Added sample restaurant data matching the schema specification
- Includes proper JSONB structure for address and opening hours

## Migration Safety
- All changes use `IF NOT EXISTS` or `ON CONFLICT DO NOTHING`
- Backward compatible field additions
- Proper foreign key constraints and cascading deletes
- Comprehensive indexing for performance
