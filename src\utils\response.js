// Response Utility Functions
class ResponseHelper {
  static success(res, data, message = 'Success', statusCode = 200) {
    return res.status(statusCode).json({
      success: true,
      data,
      message,
      timestamp: new Date().toISOString()
    });
  }

  static error(res, message = 'Internal server error', statusCode = 500, details = null) {
    const response = {
      success: false,
      error: {
        code: details?.code || 'SERVER_ERROR',
        message: message,
        details: details?.details || null
      },
      timestamp: new Date().toISOString()
    };

    return res.status(statusCode).json(response);
  }

  static validationError(res, errors) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Request validation failed',
        details: errors
      },
      timestamp: new Date().toISOString()
    });
  }

  static notFound(res, resource = 'Resource') {
    return res.status(404).json({
      success: false,
      error: `${resource} not found`,
      timestamp: new Date().toISOString()
    });
  }

  static unauthorized(res, message = 'Unauthorized access') {
    return res.status(401).json({
      success: false,
      error: message,
      timestamp: new Date().toISOString()
    });
  }

  static forbidden(res, message = 'Access forbidden') {
    return res.status(403).json({
      success: false,
      error: message,
      timestamp: new Date().toISOString()
    });
  }
}

module.exports = ResponseHelper;
