// MenuCategory Model
const BaseModel = require('./BaseModel');

class MenuCategory extends BaseModel {
  constructor() {
    super('menu_categories');
  }

  // Get categories by restaurant
  async getCategoriesByRestaurant(restaurantId, activeOnly = true) {
    try {
      const conditions = { restaurant_id: restaurantId };
      if (activeOnly) {
        conditions.is_active = true;
      }

      return await this.findAll(conditions, 'sort_order ASC, name ASC');
    } catch (error) {
      throw error;
    }
  }

  // Get category with menu items
  async getCategoryWithItems(categoryId) {
    try {
      const query = `
        SELECT 
          mc.*,
          json_agg(
            json_build_object(
              'id', mi.id,
              'name', mi.name,
              'description', mi.description,
              'price', mi.price,
              'image', mi.image,
              'isAvailable', mi.is_available,
              'isPopular', mi.is_popular,
              'isVegetarian', mi.is_vegetarian,
              'isVegan', mi.is_vegan,
              'isGlutenFree', mi.is_gluten_free,
              'preparationTime', mi.preparation_time,
              'ingredients', mi.ingredients,
              'allergens', mi.allergens,
              'nutritionInfo', mi.nutrition_info,
              'sortOrder', mi.sort_order
            )
            ORDER BY mi.sort_order ASC, mi.name ASC
          ) FILTER (WHERE mi.id IS NOT NULL) as menu_items
        FROM menu_categories mc
        LEFT JOIN menu_items mi ON mc.id = mi.category_id AND mi.is_available = true
        WHERE mc.id = $1 AND mc.is_active = true
        GROUP BY mc.id
      `;

      const result = await this.query(query, [categoryId]);
      const category = result.rows[0];

      if (category) {
        category.menu_items = category.menu_items || [];
      }

      return category;
    } catch (error) {
      throw error;
    }
  }

  // Create category with sort order
  async createCategory(categoryData) {
    try {
      // Get the next sort order for this restaurant
      const maxSortQuery = `
        SELECT COALESCE(MAX(sort_order), 0) + 1 as next_sort_order
        FROM menu_categories
        WHERE restaurant_id = $1
      `;

      const sortResult = await this.query(maxSortQuery, [categoryData.restaurant_id]);
      const nextSortOrder = sortResult.rows[0].next_sort_order;

      return await this.create({
        ...categoryData,
        sort_order: categoryData.sort_order || nextSortOrder
      });
    } catch (error) {
      throw error;
    }
  }

  // Update category sort order
  async updateSortOrder(categoryId, newSortOrder) {
    try {
      return await this.updateById(categoryId, { sort_order: newSortOrder });
    } catch (error) {
      throw error;
    }
  }

  // Reorder categories for a restaurant
  async reorderCategories(restaurantId, categoryOrders) {
    try {
      const client = await this.beginTransaction();

      try {
        for (const { categoryId, sortOrder } of categoryOrders) {
          await client.query(
            'UPDATE menu_categories SET sort_order = $1 WHERE id = $2 AND restaurant_id = $3',
            [sortOrder, categoryId, restaurantId]
          );
        }

        await this.commitTransaction(client);
        return true;
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Toggle category active status
  async toggleActive(categoryId) {
    try {
      const category = await this.findById(categoryId);
      if (!category) {
        throw new Error('Category not found');
      }

      return await this.updateById(categoryId, { is_active: !category.is_active });
    } catch (error) {
      throw error;
    }
  }

  // Get category statistics
  async getCategoryStats(categoryId, days = 30) {
    try {
      const query = `
        SELECT 
          mc.name,
          COUNT(mi.id) as total_items,
          COUNT(mi.id) FILTER (WHERE mi.is_available = true) as available_items,
          COALESCE(order_stats.total_orders, 0) as total_orders,
          COALESCE(order_stats.total_revenue, 0) as total_revenue
        FROM menu_categories mc
        LEFT JOIN menu_items mi ON mc.id = mi.category_id
        LEFT JOIN (
          SELECT 
            mi.category_id,
            COUNT(oi.id) as total_orders,
            SUM(oi.total_price) as total_revenue
          FROM order_items oi
          JOIN menu_items mi ON oi.menu_item_id = mi.id
          JOIN orders o ON oi.order_id = o.id
          WHERE o.created_at >= NOW() - INTERVAL '${days} days'
          AND o.status = 'delivered'
          GROUP BY mi.category_id
        ) order_stats ON mc.id = order_stats.category_id
        WHERE mc.id = $1
        GROUP BY mc.id, mc.name, order_stats.total_orders, order_stats.total_revenue
      `;

      const result = await this.query(query, [categoryId]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Delete category and reassign items
  async deleteCategoryAndReassignItems(categoryId, newCategoryId = null) {
    try {
      const client = await this.beginTransaction();

      try {
        if (newCategoryId) {
          // Reassign menu items to new category
          await client.query(
            'UPDATE menu_items SET category_id = $1 WHERE category_id = $2',
            [newCategoryId, categoryId]
          );
        } else {
          // Delete all menu items in this category
          await client.query(
            'DELETE FROM menu_items WHERE category_id = $1',
            [categoryId]
          );
        }

        // Delete the category
        await client.query(
          'DELETE FROM menu_categories WHERE id = $1',
          [categoryId]
        );

        await this.commitTransaction(client);
        return true;
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Get popular categories by order volume
  async getPopularCategories(restaurantId, limit = 10, days = 30) {
    try {
      const query = `
        SELECT 
          mc.*,
          COALESCE(order_stats.order_count, 0) as order_count,
          COALESCE(order_stats.total_revenue, 0) as total_revenue
        FROM menu_categories mc
        LEFT JOIN (
          SELECT 
            mi.category_id,
            COUNT(oi.id) as order_count,
            SUM(oi.total_price) as total_revenue
          FROM order_items oi
          JOIN menu_items mi ON oi.menu_item_id = mi.id
          JOIN orders o ON oi.order_id = o.id
          WHERE o.restaurant_id = $1
          AND o.created_at >= NOW() - INTERVAL '${days} days'
          AND o.status = 'delivered'
          GROUP BY mi.category_id
        ) order_stats ON mc.id = order_stats.category_id
        WHERE mc.restaurant_id = $1 AND mc.is_active = true
        ORDER BY order_stats.order_count DESC NULLS LAST, mc.sort_order ASC
        LIMIT $2
      `;

      const result = await this.query(query, [restaurantId, limit]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = MenuCategory;
