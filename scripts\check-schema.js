// Check current database schema
require('dotenv').config();
const { Pool } = require('pg');

async function checkSchema() {
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not found in environment variables');
    process.exit(1);
  }

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    console.log('🔍 Checking current database schema...\n');

    // Get all tables
    const tablesResult = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);

    console.log('📋 Existing tables:');
    for (const row of tablesResult.rows) {
      console.log(`  - ${row.table_name}`);
    }

    // Check restaurants table structure specifically
    if (tablesResult.rows.some(row => row.table_name === 'restaurants')) {
      console.log('\n🍕 Restaurants table columns:');
      const columnsResult = await pool.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'restaurants' 
        ORDER BY ordinal_position
      `);
      
      for (const col of columnsResult.rows) {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      }
    }

    // Check user_favorites table structure
    if (tablesResult.rows.some(row => row.table_name === 'user_favorites')) {
      console.log('\n❤️ User favorites table columns:');
      const columnsResult = await pool.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = 'user_favorites'
        ORDER BY ordinal_position
      `);

      for (const col of columnsResult.rows) {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      }

      // Check sample data
      const sampleResult = await pool.query('SELECT * FROM user_favorites LIMIT 3');
      if (sampleResult.rows.length > 0) {
        console.log('\n📊 Sample user_favorites data:');
        console.log(sampleResult.rows);
      } else {
        console.log('\n📊 No user_favorites data found');
      }
    }

    // Check menu_categories table structure
    if (tablesResult.rows.some(row => row.table_name === 'menu_categories')) {
      console.log('\n📋 Menu categories table columns:');
      const columnsResult = await pool.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = 'menu_categories'
        ORDER BY ordinal_position
      `);

      for (const col of columnsResult.rows) {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      }
    }

  } catch (error) {
    console.error('❌ Schema check failed:', error.message);
  } finally {
    await pool.end();
  }
}

checkSchema();
