// Order Model
const BaseModel = require('./BaseModel');

class Order extends BaseModel {
  constructor() {
    super('orders');
  }

  // Get order with items and tracking
  async getOrderWithDetails(orderId) {
    try {
      const query = `
        SELECT 
          o.*,
          r.name as restaurant_name,
          r.cover_image_url as restaurant_image,
          json_agg(
            json_build_object(
              'id', oi.id,
              'menuItemId', oi.menu_item_id,
              'menuItemName', mi.name,
              'menuItemImage', mi.image_url,
              'quantity', oi.quantity,
              'unitPrice', oi.unit_price,
              'totalPrice', oi.total_price,
              'customizations', oi.customizations,
              'specialInstructions', oi.special_instructions
            )
          ) FILTER (WHERE oi.id IS NOT NULL) as items
        FROM orders o
        LEFT JOIN restaurants r ON o.restaurant_id = r.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN menu_items mi ON oi.menu_item_id = mi.id
        WHERE o.id = $1
        GROUP BY o.id, r.name, r.cover_image_url
      `;

      const result = await this.query(query, [orderId]);
      const order = result.rows[0];

      if (order) {
        order.items = order.items || [];
        // Parse JSON fields if they're strings
        if (typeof order.delivery_address === 'string') {
          order.delivery_address = JSON.parse(order.delivery_address || '{}');
        }
      }

      return order;
    } catch (error) {
      throw error;
    }
  }

  // Create a new order with items
  async createOrderWithItems(orderData, orderItems) {
    try {
      const client = await this.beginTransaction();

      try {
        // Create the order
        const orderResult = await client.query(`
          INSERT INTO orders (
            user_id, restaurant_id, subtotal, tax, delivery_fee, tip,
            discount, total_amount, delivery_address, delivery_instructions,
            estimated_delivery_time, payment_method_id, special_instructions
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
          RETURNING *
        `, [
          orderData.user_id,
          orderData.restaurant_id,
          orderData.subtotal,
          orderData.tax || 0,
          orderData.delivery_fee || 0,
          orderData.tip || 0,
          orderData.discount || 0,
          orderData.total_amount,
          JSON.stringify(orderData.delivery_address),
          orderData.delivery_instructions || null,
          orderData.estimated_delivery_time || null,
          orderData.payment_method_id || null,
          orderData.special_instructions || null
        ]);

        const order = orderResult.rows[0];

        // Create order items
        for (const item of orderItems) {
          await client.query(`
            INSERT INTO order_items (
              order_id, menu_item_id, quantity, unit_price, total_price,
              special_instructions, customizations
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
          `, [
            order.id,
            item.menu_item_id,
            item.quantity,
            item.unit_price,
            item.total_price,
            item.special_instructions || null,
            JSON.stringify(item.customizations || [])
          ]);
        }

        // Add initial status history
        await client.query(`
          INSERT INTO order_status_history (order_id, status, note)
          VALUES ($1, $2, $3)
        `, [order.id, 'pending', 'Order placed successfully']);

        await this.commitTransaction(client);

        // Return order with items
        return await this.getOrderWithDetails(order.id);
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Get user orders with pagination
  async getUserOrders(userId, filters = {}) {
    try {
      const { status, page = 1, limit = 10, startDate, endDate } = filters;
      const offset = (page - 1) * limit;

      let whereConditions = ['o.user_id = $1'];
      let params = [userId];
      let paramIndex = 2;

      if (status) {
        whereConditions.push(`o.status = $${paramIndex}`);
        params.push(status);
        paramIndex++;
      }

      if (startDate) {
        whereConditions.push(`o.created_at >= $${paramIndex}`);
        params.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        whereConditions.push(`o.created_at <= $${paramIndex}`);
        params.push(endDate);
        paramIndex++;
      }

      const whereClause = whereConditions.join(' AND ');

      const query = `
        SELECT 
          o.id, o.order_number, o.status, o.total_amount, o.created_at,
          o.actual_delivery_time,
          COUNT(oi.id) as item_count,
          r.name as restaurant_name,
          r.cover_image_url as restaurant_image
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN restaurants r ON o.restaurant_id = r.id
        WHERE ${whereClause}
        GROUP BY o.id, r.name, r.cover_image_url
        ORDER BY o.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const countQuery = `
        SELECT COUNT(*) as count
        FROM orders o
        WHERE ${whereClause}
      `;

      params.push(limit, offset);
      const countParams = params.slice(0, -2); // Remove limit and offset for count

      const [results, countResult] = await Promise.all([
        this.query(query, params),
        this.query(countQuery, countParams)
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        orders: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get order tracking history
  async getOrderTracking(orderId) {
    try {
      const query = `
        SELECT 
          status, message, latitude, longitude, driver_name, driver_phone, created_at
        FROM order_tracking
        WHERE order_id = $1
        ORDER BY created_at ASC
      `;

      const result = await this.query(query, [orderId]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Update order status
  async updateStatus(orderId, status, message = null) {
    try {
      const client = await this.beginTransaction();

      try {
        // Update order status
        await client.query(
          'UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2',
          [status, orderId]
        );

        // Add tracking entry
        const trackingMessage = message || this.getDefaultStatusMessage(status);
        await client.query(
          'INSERT INTO order_tracking (order_id, status, message) VALUES ($1, $2, $3)',
          [orderId, status, trackingMessage]
        );

        await this.commitTransaction(client);
        return true;
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Get orders by status
  async getOrdersByStatus(status, limit = 50) {
    try {
      const query = `
        SELECT 
          o.*,
          r.name as restaurant_name,
          u.first_name, u.last_name, u.phone
        FROM orders o
        LEFT JOIN restaurants r ON o.restaurant_id = r.id
        LEFT JOIN users u ON o.user_id = u.id
        WHERE o.status = $1
        ORDER BY o.created_at ASC
        LIMIT $2
      `;

      const result = await this.query(query, [status, limit]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Get order statistics
  async getOrderStats(startDate = null, endDate = null) {
    try {
      let whereClause = '';
      const params = [];

      if (startDate && endDate) {
        whereClause = 'WHERE created_at BETWEEN $1 AND $2';
        params.push(startDate, endDate);
      }

      const query = `
        SELECT 
          COUNT(*) as total_orders,
          COUNT(*) FILTER (WHERE status = 'delivered') as delivered_orders,
          COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_orders,
          COUNT(*) FILTER (WHERE status IN ('pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery')) as active_orders,
          COALESCE(SUM(total_amount), 0) as total_revenue,
          COALESCE(AVG(total_amount), 0) as average_order_value
        FROM orders
        ${whereClause}
      `;

      const result = await this.query(query, params);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Get popular items
  async getPopularItems(limit = 10, days = 30) {
    try {
      const query = `
        SELECT 
          mi.id, mi.name, mi.image_url,
          COUNT(oi.id) as order_count,
          SUM(oi.quantity) as total_quantity,
          SUM(oi.total_price) as total_revenue
        FROM order_items oi
        JOIN menu_items mi ON oi.menu_item_id = mi.id
        JOIN orders o ON oi.order_id = o.id
        WHERE o.created_at >= NOW() - INTERVAL '${days} days'
        AND o.status = 'delivered'
        GROUP BY mi.id, mi.name, mi.image_url
        ORDER BY order_count DESC
        LIMIT $1
      `;

      const result = await this.query(query, [limit]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Cancel order
  async cancelOrder(orderId, reason = null) {
    try {
      const client = await this.beginTransaction();

      try {
        // Check if order can be cancelled
        const orderResult = await client.query(
          'SELECT status FROM orders WHERE id = $1',
          [orderId]
        );

        if (orderResult.rows.length === 0) {
          throw new Error('Order not found');
        }

        const order = orderResult.rows[0];
        if (!['pending', 'confirmed'].includes(order.status)) {
          throw new Error('Order cannot be cancelled at this stage');
        }

        // Update order status
        await client.query(
          'UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2',
          ['cancelled', orderId]
        );

        // Add tracking entry
        await client.query(
          'INSERT INTO order_tracking (order_id, status, message) VALUES ($1, $2, $3)',
          [orderId, 'cancelled', reason || 'Order cancelled']
        );

        await this.commitTransaction(client);
        return true;
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Get default status message
  getDefaultStatusMessage(status) {
    const messages = {
      'pending': 'Order received and waiting for confirmation',
      'confirmed': 'Order confirmed by restaurant',
      'preparing': 'Kitchen started preparing your order',
      'ready': 'Order ready for pickup',
      'out_for_delivery': 'Driver picked up your order',
      'delivered': 'Order delivered successfully',
      'cancelled': 'Order has been cancelled'
    };

    return messages[status] || `Order status updated to ${status}`;
  }

  // Get order with status history
  async getOrderWithHistory(orderId) {
    try {
      const order = await this.getOrderWithDetails(orderId);
      if (!order) return null;

      // Get status history
      const historyQuery = `
        SELECT
          osh.*,
          u.first_name,
          u.last_name
        FROM order_status_history osh
        LEFT JOIN users u ON osh.changed_by = u.id
        WHERE osh.order_id = $1
        ORDER BY osh.created_at ASC
      `;

      const historyResult = await this.query(historyQuery, [orderId]);
      order.status_history = historyResult.rows;

      return order;
    } catch (error) {
      throw error;
    }
  }

  // Update order payment status
  async updatePaymentStatus(orderId, paymentStatus, paymentIntentId = null) {
    try {
      const updateData = {
        payment_status: paymentStatus,
        updated_at: new Date()
      };

      if (paymentIntentId) {
        updateData.payment_intent_id = paymentIntentId;
      }

      return await this.updateById(orderId, updateData);
    } catch (error) {
      throw error;
    }
  }

  // Get orders by status
  async getOrdersByStatus(status, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      const query = `
        SELECT
          o.*,
          r.name as restaurant_name,
          u.first_name,
          u.last_name,
          u.phone
        FROM orders o
        JOIN restaurants r ON o.restaurant_id = r.id
        JOIN users u ON o.user_id = u.id
        WHERE o.status = $1
        ORDER BY o.created_at DESC
        LIMIT $2 OFFSET $3
      `;

      const countQuery = `
        SELECT COUNT(*) as count
        FROM orders
        WHERE status = $1
      `;

      const [results, countResult] = await Promise.all([
        this.query(query, [status, limit, offset]),
        this.query(countQuery, [status])
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        orders: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get order analytics
  async getOrderAnalytics(days = 30) {
    try {
      const query = `
        SELECT
          DATE_TRUNC('day', created_at) as date,
          COUNT(*) as total_orders,
          COUNT(*) FILTER (WHERE status = 'delivered') as completed_orders,
          COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_orders,
          AVG(total_amount) FILTER (WHERE status = 'delivered') as avg_order_value,
          SUM(total_amount) FILTER (WHERE status = 'delivered') as total_revenue
        FROM orders
        WHERE created_at >= NOW() - INTERVAL '${days} days'
        GROUP BY DATE_TRUNC('day', created_at)
        ORDER BY date DESC
      `;

      const result = await this.query(query);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Get order delivery time estimates
  async updateDeliveryEstimate(orderId, estimatedTime) {
    try {
      return await this.updateById(orderId, {
        estimated_delivery_time: estimatedTime
      });
    } catch (error) {
      throw error;
    }
  }

  // Mark order as delivered
  async markAsDelivered(orderId, actualDeliveryTime = null) {
    try {
      const client = await this.beginTransaction();

      try {
        // Update order status and delivery time
        await client.query(
          'UPDATE orders SET status = $1, delivered_at = $2, actual_delivery_time = $3, updated_at = NOW() WHERE id = $4',
          ['delivered', new Date(), actualDeliveryTime || new Date(), orderId]
        );

        // Add status history
        await client.query(
          'INSERT INTO order_status_history (order_id, status, note) VALUES ($1, $2, $3)',
          [orderId, 'delivered', 'Order delivered successfully']
        );

        await this.commitTransaction(client);
        return true;
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Get orders requiring attention (stuck in status)
  async getOrdersRequiringAttention(hoursThreshold = 2) {
    try {
      const query = `
        SELECT
          o.*,
          r.name as restaurant_name,
          u.first_name,
          u.last_name,
          EXTRACT(EPOCH FROM (NOW() - o.updated_at))/3600 as hours_since_update
        FROM orders o
        JOIN restaurants r ON o.restaurant_id = r.id
        JOIN users u ON o.user_id = u.id
        WHERE o.status IN ('confirmed', 'preparing', 'ready', 'out_for_delivery')
        AND o.updated_at < NOW() - INTERVAL '${hoursThreshold} hours'
        ORDER BY o.updated_at ASC
      `;

      const result = await this.query(query);
      return result.rows.map(row => ({
        ...row,
        hours_since_update: Math.round(row.hours_since_update * 100) / 100
      }));
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Order;
