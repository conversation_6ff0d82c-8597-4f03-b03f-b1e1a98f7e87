// Payment Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const PaymentService = require('../services/paymentService');
const databaseManager = require('../config/database');

class PaymentController {
  // Create payment intent for order
  static async createPaymentIntent(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { orderId, amount } = req.body;
      const userId = req.user.id;

      // Verify order belongs to user and get order details
      const pool = databaseManager.getPool();
      const orderResult = await pool.query(
        'SELECT id, total_amount, status FROM orders WHERE id = $1 AND user_id = $2',
        [orderId, userId]
      );

      if (orderResult.rows.length === 0) {
        return ResponseHelper.error(res, 'Order not found', 404);
      }

      const order = orderResult.rows[0];

      if (order.status !== 'pending') {
        return ResponseHelper.error(res, 'Order cannot be paid at this stage', 400);
      }

      // Verify amount matches order total
      if (Math.abs(parseFloat(order.total_amount) - amount) > 0.01) {
        return ResponseHelper.error(res, 'Payment amount does not match order total', 400);
      }

      const paymentIntent = await PaymentService.createPaymentIntent(orderId, amount);
      
      // Update order with payment intent ID
      await pool.query(
        'UPDATE orders SET stripe_payment_intent_id = $1, updated_at = NOW() WHERE id = $2',
        [paymentIntent.paymentIntentId, orderId]
      );

      return ResponseHelper.success(res, paymentIntent, 'Payment intent created successfully');

    } catch (error) {
      console.error('Create payment intent error:', error);
      return ResponseHelper.error(res, 'Failed to create payment intent', 500);
    }
  }

  // Confirm payment
  static async confirmPayment(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { paymentIntentId } = req.body;

      const paymentIntent = await PaymentService.confirmPaymentIntent(paymentIntentId);
      
      return ResponseHelper.success(res, {
        status: paymentIntent.status,
        paymentIntentId: paymentIntent.id
      }, 'Payment confirmed successfully');

    } catch (error) {
      console.error('Confirm payment error:', error);
      return ResponseHelper.error(res, 'Failed to confirm payment', 500);
    }
  }

  // Handle Stripe webhook
  static async handleWebhook(req, res) {
    try {
      const signature = req.headers['stripe-signature'];
      
      if (!signature) {
        return ResponseHelper.error(res, 'Missing stripe signature', 400);
      }

      await PaymentService.handleWebhook(req.body, signature);
      
      return ResponseHelper.success(res, { received: true }, 'Webhook processed successfully');

    } catch (error) {
      console.error('Webhook error:', error);
      return ResponseHelper.error(res, 'Webhook processing failed', 400);
    }
  }

  // Get payment intent details
  static async getPaymentIntent(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const paymentIntentId = req.params.id;
      const userId = req.user.id;

      // Verify the payment intent belongs to user's order
      const pool = databaseManager.getPool();
      const orderResult = await pool.query(
        'SELECT id FROM orders WHERE stripe_payment_intent_id = $1 AND user_id = $2',
        [paymentIntentId, userId]
      );

      if (orderResult.rows.length === 0) {
        return ResponseHelper.error(res, 'Payment intent not found', 404);
      }

      const paymentIntent = await PaymentService.getPaymentIntent(paymentIntentId);
      
      return ResponseHelper.success(res, {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount / 100, // Convert from cents
        currency: paymentIntent.currency,
        created: paymentIntent.created
      }, 'Payment intent retrieved successfully');

    } catch (error) {
      console.error('Get payment intent error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve payment intent', 500);
    }
  }

  // Process refund
  static async processRefund(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { orderId, amount, reason } = req.body;
      const userId = req.user.id;

      // Verify order belongs to user and get payment intent
      const pool = databaseManager.getPool();
      const orderResult = await pool.query(
        'SELECT stripe_payment_intent_id, total_amount, status FROM orders WHERE id = $1 AND user_id = $2',
        [orderId, userId]
      );

      if (orderResult.rows.length === 0) {
        return ResponseHelper.error(res, 'Order not found', 404);
      }

      const order = orderResult.rows[0];

      if (!order.stripe_payment_intent_id) {
        return ResponseHelper.error(res, 'No payment found for this order', 400);
      }

      if (!['delivered', 'cancelled'].includes(order.status)) {
        return ResponseHelper.error(res, 'Order cannot be refunded at this stage', 400);
      }

      const refund = await PaymentService.processRefund(
        order.stripe_payment_intent_id,
        amount,
        reason
      );

      // Update order status if full refund
      if (!amount || amount >= parseFloat(order.total_amount)) {
        await pool.query(
          'UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2',
          ['refunded', orderId]
        );

        await pool.query(
          'INSERT INTO order_tracking (order_id, status, message) VALUES ($1, $2, $3)',
          [orderId, 'refunded', `Refund processed: ${reason || 'Customer request'}`]
        );
      }

      return ResponseHelper.success(res, {
        refundId: refund.id,
        amount: refund.amount / 100, // Convert from cents
        status: refund.status
      }, 'Refund processed successfully');

    } catch (error) {
      console.error('Process refund error:', error);
      return ResponseHelper.error(res, 'Failed to process refund', 500);
    }
  }
}

module.exports = PaymentController;
