# Frontend Environment Variables for Restaurant App
# Copy this file to your frontend project as .env.local

# ================================
# API CONFIGURATION
# ================================
NEXT_PUBLIC_API_BASE_URL=https://backend-production-f106.up.railway.app/api/v1
NEXT_PUBLIC_WEBSOCKET_URL=wss://backend-production-f106.up.railway.app

# ================================
# DEVELOPMENT CONFIGURATION
# ================================
# For local development, use:
# NEXT_PUBLIC_API_BASE_URL=http://localhost:5000/api/v1
# NEXT_PUBLIC_WEBSOCKET_URL=ws://localhost:5000

# ================================
# EXTERNAL SERVICES
# ================================
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# ================================
# FIREBASE CONFIGURATION (if using)
# ================================
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456

# ================================
# APP CONFIGURATION
# ================================
NEXT_PUBLIC_APP_NAME=Restaurant Management App
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=production

# ================================
# FEATURE FLAGS
# ================================
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_PUSH_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_REAL_TIME_UPDATES=true
