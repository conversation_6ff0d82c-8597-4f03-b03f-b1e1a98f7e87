// PaymentMethod Model
const BaseModel = require('./BaseModel');

class PaymentMethod extends BaseModel {
  constructor() {
    super('payment_methods');
  }

  // Get payment methods by user
  async getPaymentMethodsByUser(userId, activeOnly = true) {
    try {
      const conditions = { user_id: userId };
      if (activeOnly) {
        conditions.is_active = true;
      }

      return await this.findAll(conditions, 'is_default DESC, created_at DESC');
    } catch (error) {
      throw error;
    }
  }

  // Get user's default payment method
  async getDefaultPaymentMethod(userId) {
    try {
      return await this.findOne({ 
        user_id: userId, 
        is_default: true, 
        is_active: true 
      });
    } catch (error) {
      throw error;
    }
  }

  // Create payment method
  async createPaymentMethod(paymentData) {
    try {
      const client = await this.beginTransaction();

      try {
        // If this is set as default, unset other defaults for this user
        if (paymentData.is_default) {
          await client.query(
            'UPDATE payment_methods SET is_default = false WHERE user_id = $1',
            [paymentData.user_id]
          );
        }

        // If this is the user's first payment method, make it default
        const existingCount = await client.query(
          'SELECT COUNT(*) as count FROM payment_methods WHERE user_id = $1 AND is_active = true',
          [paymentData.user_id]
        );

        const isFirstPaymentMethod = parseInt(existingCount.rows[0].count) === 0;
        if (isFirstPaymentMethod) {
          paymentData.is_default = true;
        }

        // Create the payment method
        const result = await client.query(`
          INSERT INTO payment_methods (
            user_id, type, provider, external_id, last_four, brand,
            expiry_month, expiry_year, cardholder_name, is_default, is_active
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
          RETURNING *
        `, [
          paymentData.user_id,
          paymentData.type,
          paymentData.provider || 'stripe',
          paymentData.external_id,
          paymentData.last_four || null,
          paymentData.brand || null,
          paymentData.expiry_month || null,
          paymentData.expiry_year || null,
          paymentData.cardholder_name || null,
          paymentData.is_default || false,
          true
        ]);

        await this.commitTransaction(client);
        return result.rows[0];
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Update payment method
  async updatePaymentMethod(paymentMethodId, userId, updateData) {
    try {
      const client = await this.beginTransaction();

      try {
        // Verify payment method belongs to user
        const existingPaymentMethod = await client.query(
          'SELECT * FROM payment_methods WHERE id = $1 AND user_id = $2',
          [paymentMethodId, userId]
        );

        if (existingPaymentMethod.rows.length === 0) {
          throw new Error('Payment method not found or access denied');
        }

        // If setting as default, unset other defaults
        if (updateData.is_default) {
          await client.query(
            'UPDATE payment_methods SET is_default = false WHERE user_id = $1 AND id != $2',
            [userId, paymentMethodId]
          );
        }

        // Update the payment method
        const updateFields = [];
        const updateValues = [];
        let paramIndex = 1;

        const allowedFields = [
          'last_four', 'brand', 'expiry_month', 'expiry_year', 
          'cardholder_name', 'is_default'
        ];

        for (const field of allowedFields) {
          if (updateData[field] !== undefined) {
            updateFields.push(`${field} = $${paramIndex}`);
            updateValues.push(updateData[field]);
            paramIndex++;
          }
        }

        if (updateFields.length === 0) {
          throw new Error('No valid fields to update');
        }

        updateFields.push(`updated_at = NOW()`);
        updateValues.push(paymentMethodId);

        const query = `
          UPDATE payment_methods 
          SET ${updateFields.join(', ')}
          WHERE id = $${paramIndex}
          RETURNING *
        `;

        const result = await client.query(query, updateValues);

        await this.commitTransaction(client);
        return result.rows[0];
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Set default payment method
  async setDefaultPaymentMethod(paymentMethodId, userId) {
    try {
      const client = await this.beginTransaction();

      try {
        // Verify payment method belongs to user
        const existingPaymentMethod = await client.query(
          'SELECT * FROM payment_methods WHERE id = $1 AND user_id = $2 AND is_active = true',
          [paymentMethodId, userId]
        );

        if (existingPaymentMethod.rows.length === 0) {
          throw new Error('Payment method not found or access denied');
        }

        // Unset all defaults for this user
        await client.query(
          'UPDATE payment_methods SET is_default = false WHERE user_id = $1',
          [userId]
        );

        // Set new default
        await client.query(
          'UPDATE payment_methods SET is_default = true WHERE id = $1',
          [paymentMethodId]
        );

        await this.commitTransaction(client);
        return true;
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Soft delete payment method
  async deletePaymentMethod(paymentMethodId, userId) {
    try {
      const client = await this.beginTransaction();

      try {
        // Verify payment method belongs to user
        const existingPaymentMethod = await client.query(
          'SELECT * FROM payment_methods WHERE id = $1 AND user_id = $2',
          [paymentMethodId, userId]
        );

        if (existingPaymentMethod.rows.length === 0) {
          throw new Error('Payment method not found or access denied');
        }

        const paymentMethod = existingPaymentMethod.rows[0];

        // Soft delete the payment method
        await client.query(
          'UPDATE payment_methods SET is_active = false WHERE id = $1',
          [paymentMethodId]
        );

        // If this was the default payment method, set another one as default
        if (paymentMethod.is_default) {
          const nextPaymentMethod = await client.query(
            'SELECT id FROM payment_methods WHERE user_id = $1 AND is_active = true ORDER BY created_at DESC LIMIT 1',
            [userId]
          );

          if (nextPaymentMethod.rows.length > 0) {
            await client.query(
              'UPDATE payment_methods SET is_default = true WHERE id = $1',
              [nextPaymentMethod.rows[0].id]
            );
          }
        }

        await this.commitTransaction(client);
        return true;
      } catch (error) {
        await this.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      throw error;
    }
  }

  // Validate payment method data
  validatePaymentMethodData(paymentData) {
    const errors = [];

    // Validate type
    const validTypes = ['card', 'paypal', 'apple_pay', 'google_pay'];
    if (!paymentData.type || !validTypes.includes(paymentData.type)) {
      errors.push('Invalid payment method type');
    }

    // Validate card-specific fields
    if (paymentData.type === 'card') {
      if (!paymentData.external_id) {
        errors.push('External ID is required for card payments');
      }

      if (paymentData.expiry_month) {
        const month = parseInt(paymentData.expiry_month);
        if (isNaN(month) || month < 1 || month > 12) {
          errors.push('Invalid expiry month');
        }
      }

      if (paymentData.expiry_year) {
        const year = parseInt(paymentData.expiry_year);
        const currentYear = new Date().getFullYear();
        if (isNaN(year) || year < currentYear || year > currentYear + 20) {
          errors.push('Invalid expiry year');
        }
      }

      if (paymentData.last_four && !/^\d{4}$/.test(paymentData.last_four)) {
        errors.push('Last four digits must be exactly 4 digits');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get payment method statistics for user
  async getPaymentMethodStats(userId, days = 30) {
    try {
      const query = `
        SELECT 
          pm.type,
          pm.brand,
          COUNT(o.id) as usage_count,
          SUM(o.total_amount) as total_amount,
          MAX(o.created_at) as last_used
        FROM payment_methods pm
        LEFT JOIN orders o ON pm.id = o.payment_method_id 
          AND o.created_at >= NOW() - INTERVAL '${days} days'
          AND o.payment_status = 'paid'
        WHERE pm.user_id = $1 AND pm.is_active = true
        GROUP BY pm.id, pm.type, pm.brand
        ORDER BY usage_count DESC, pm.created_at DESC
      `;

      const result = await this.query(query, [userId]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Check if payment method is expired
  isPaymentMethodExpired(paymentMethod) {
    if (paymentMethod.type !== 'card' || !paymentMethod.expiry_month || !paymentMethod.expiry_year) {
      return false;
    }

    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1; // getMonth() returns 0-11

    const expiryYear = parseInt(paymentMethod.expiry_year);
    const expiryMonth = parseInt(paymentMethod.expiry_month);

    if (expiryYear < currentYear) {
      return true;
    }

    if (expiryYear === currentYear && expiryMonth < currentMonth) {
      return true;
    }

    return false;
  }

  // Get expired payment methods for user
  async getExpiredPaymentMethods(userId) {
    try {
      const paymentMethods = await this.getPaymentMethodsByUser(userId);
      return paymentMethods.filter(pm => this.isPaymentMethodExpired(pm));
    } catch (error) {
      throw error;
    }
  }
}

module.exports = PaymentMethod;
