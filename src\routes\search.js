// Search Routes
const express = require('express');
const SearchController = require('../controllers/searchController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { searchLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Apply search rate limiting to all routes
router.use(searchLimiter);

// Public routes (no authentication required)

// GET /api/search/restaurants - Search restaurants
router.get('/restaurants',
  ValidationMiddleware.validateSearch(),
  SearchController.searchRestaurants
);

// GET /api/search/suggestions - Get search suggestions
router.get('/suggestions',
  [
    ValidationMiddleware.validateSearch(),
    // Additional validation for suggestions
    ValidationMiddleware.validatePagination()
  ],
  SearchController.getSearchSuggestions
);

// GET /api/search/popular - Get popular search terms
router.get('/popular',
  SearchController.getPopularSearches
);

module.exports = router;
