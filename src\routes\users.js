// User Routes
const express = require('express');
const multer = require('multer');
const UserController = require('../controllers/userController');
const AuthMiddleware = require('../middleware/auth');
const ValidationMiddleware = require('../middleware/validation');
const { apiLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// Configure multer for avatar uploads
const upload = multer({
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check file type
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

// Apply rate limiting and authentication to all routes
router.use(apiLimiter);
router.use(AuthMiddleware.authenticate);

// Profile routes
// GET /api/v1/user/profile - Get current user profile
router.get('/profile',
  UserController.getProfile
);

// PUT /api/v1/user/profile - Update user profile
router.put('/profile',
  ValidationMiddleware.validateProfileUpdate(),
  UserController.updateProfile
);

// Avatar upload moved to /api/v1/upload/avatar endpoint

// Address routes
// GET /api/v1/user/addresses - Get user addresses
router.get('/addresses',
  UserController.getAddresses
);

// POST /api/v1/user/addresses - Add new address
router.post('/addresses',
  ValidationMiddleware.validateAddress(),
  UserController.addAddress
);

// PUT /api/v1/user/addresses/:id - Update address
router.put('/addresses/:id',
  ValidationMiddleware.validateUUID('id'),
  ValidationMiddleware.validateAddressUpdate(),
  UserController.updateAddress
);

// DELETE /api/v1/user/addresses/:id - Delete address
router.delete('/addresses/:id',
  ValidationMiddleware.validateUUID('id'),
  UserController.deleteAddress
);

// Payment method routes
// GET /api/v1/user/payment-methods - Get payment methods
router.get('/payment-methods',
  UserController.getPaymentMethods
);

// POST /api/v1/user/payment-methods - Add payment method
router.post('/payment-methods',
  ValidationMiddleware.validatePaymentMethod(),
  UserController.addPaymentMethod
);

// DELETE /api/v1/user/payment-methods/:id - Delete payment method
router.delete('/payment-methods/:id',
  ValidationMiddleware.validateUUID('id'),
  UserController.deletePaymentMethod
);

// Favorites routes
// GET /api/v1/user/favorites - Get user favorites
router.get('/favorites',
  UserController.getFavorites
);

// POST /api/v1/user/favorites - Add to favorites
router.post('/favorites',
  ValidationMiddleware.validateAddFavorite(),
  UserController.addFavorite
);

// DELETE /api/v1/user/favorites/:restaurantId - Remove from favorites
router.delete('/favorites/:restaurantId',
  ValidationMiddleware.validateUUID('restaurantId'),
  UserController.removeFavorite
);

// Reviews routes
// GET /api/v1/user/reviews - Get user reviews
router.get('/reviews',
  UserController.getReviews
);

// GET /api/v1/user/notification-settings - Get user notification settings
router.get('/notification-settings',
  UserController.getNotificationSettings
);

// PUT /api/v1/user/notification-settings - Update user notification settings
router.put('/notification-settings',
  UserController.updateNotificationSettings
);

module.exports = router;
