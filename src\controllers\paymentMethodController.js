// PaymentMethod Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const PaymentMethod = require('../models/PaymentMethod');

class PaymentMethodController {
  // Get user's payment methods
  static async getUserPaymentMethods(req, res) {
    try {
      const userId = req.user.id;
      const activeOnly = req.query.activeOnly !== 'false';
      
      const paymentMethodModel = new PaymentMethod();
      const paymentMethods = await paymentMethodModel.getPaymentMethodsByUser(userId, activeOnly);

      return ResponseHelper.success(res, { paymentMethods }, 'Payment methods retrieved successfully');
    } catch (error) {
      console.error('Get user payment methods error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve payment methods', 500);
    }
  }

  // Get user's default payment method
  static async getDefaultPaymentMethod(req, res) {
    try {
      const userId = req.user.id;
      const paymentMethodModel = new PaymentMethod();
      
      const paymentMethod = await paymentMethodModel.getDefaultPaymentMethod(userId);

      if (!paymentMethod) {
        return ResponseHelper.error(res, 'No default payment method found', 404, {
          code: 'NOT_FOUND',
          message: 'User has no default payment method set'
        });
      }

      return ResponseHelper.success(res, paymentMethod, 'Default payment method retrieved successfully');
    } catch (error) {
      console.error('Get default payment method error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve default payment method', 500);
    }
  }

  // Create new payment method
  static async createPaymentMethod(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const paymentData = { ...req.body, user_id: userId };
      const paymentMethodModel = new PaymentMethod();
      
      // Validate payment method data
      const validation = paymentMethodModel.validatePaymentMethodData(paymentData);
      if (!validation.isValid) {
        return ResponseHelper.error(res, validation.errors.join(', '), 400, {
          code: 'VALIDATION_ERROR',
          errors: validation.errors
        });
      }

      const paymentMethod = await paymentMethodModel.createPaymentMethod(paymentData);

      return ResponseHelper.success(res, paymentMethod, 'Payment method created successfully', 201);
    } catch (error) {
      console.error('Create payment method error:', error);
      return ResponseHelper.error(res, 'Failed to create payment method', 500);
    }
  }

  // Update payment method
  static async updatePaymentMethod(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const paymentMethodId = req.params.id;
      const updateData = req.body;
      const paymentMethodModel = new PaymentMethod();
      
      const paymentMethod = await paymentMethodModel.updatePaymentMethod(paymentMethodId, userId, updateData);

      return ResponseHelper.success(res, paymentMethod, 'Payment method updated successfully');
    } catch (error) {
      console.error('Update payment method error:', error);
      
      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to update payment method', 500);
    }
  }

  // Set default payment method
  static async setDefaultPaymentMethod(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const paymentMethodId = req.params.id;
      const paymentMethodModel = new PaymentMethod();
      
      const result = await paymentMethodModel.setDefaultPaymentMethod(paymentMethodId, userId);

      if (!result) {
        return ResponseHelper.error(res, 'Payment method not found', 404, {
          code: 'NOT_FOUND',
          message: 'Payment method not found or access denied'
        });
      }

      return ResponseHelper.success(res, { updated: true }, 'Default payment method updated successfully');
    } catch (error) {
      console.error('Set default payment method error:', error);
      
      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to set default payment method', 500);
    }
  }

  // Delete payment method
  static async deletePaymentMethod(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const paymentMethodId = req.params.id;
      const paymentMethodModel = new PaymentMethod();
      
      const result = await paymentMethodModel.deletePaymentMethod(paymentMethodId, userId);

      if (!result) {
        return ResponseHelper.error(res, 'Payment method not found', 404, {
          code: 'NOT_FOUND',
          message: 'Payment method not found or access denied'
        });
      }

      return ResponseHelper.success(res, { deleted: true }, 'Payment method deleted successfully');
    } catch (error) {
      console.error('Delete payment method error:', error);
      
      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to delete payment method', 500);
    }
  }

  // Get payment method statistics
  static async getPaymentMethodStats(req, res) {
    try {
      const userId = req.user.id;
      const days = parseInt(req.query.days) || 30;
      const paymentMethodModel = new PaymentMethod();
      
      const stats = await paymentMethodModel.getPaymentMethodStats(userId, days);

      return ResponseHelper.success(res, { stats }, 'Payment method statistics retrieved successfully');
    } catch (error) {
      console.error('Get payment method stats error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve payment method statistics', 500);
    }
  }

  // Get expired payment methods
  static async getExpiredPaymentMethods(req, res) {
    try {
      const userId = req.user.id;
      const paymentMethodModel = new PaymentMethod();
      
      const expiredMethods = await paymentMethodModel.getExpiredPaymentMethods(userId);

      return ResponseHelper.success(res, { expiredMethods }, 'Expired payment methods retrieved successfully');
    } catch (error) {
      console.error('Get expired payment methods error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve expired payment methods', 500);
    }
  }
}

module.exports = PaymentMethodController;
