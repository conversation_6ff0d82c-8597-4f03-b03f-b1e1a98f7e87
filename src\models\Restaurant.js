// Restaurant Model
const BaseModel = require('./BaseModel');

class Restaurant extends BaseModel {
  constructor() {
    super('restaurants');
  }

  // Get restaurant by slug
  async findBySlug(slug) {
    try {
      return await this.findOne({ slug });
    } catch (error) {
      throw error;
    }
  }

  // Get restaurant with menu categories
  async getRestaurantWithMenu(restaurantId) {
    try {
      const query = `
        SELECT 
          r.*,
          json_agg(
            json_build_object(
              'id', mc.id,
              'name', mc.name,
              'description', mc.description,
              'image', mc.image,
              'sortOrder', mc.sort_order,
              'items', (
                SELECT json_agg(
                  json_build_object(
                    'id', mi.id,
                    'name', mi.name,
                    'description', mi.description,
                    'price', mi.price,
                    'image', mi.image,
                    'isAvailable', mi.is_available,
                    'isPopular', mi.is_popular,
                    'isVegetarian', mi.is_vegetarian,
                    'isVegan', mi.is_vegan,
                    'isGlutenFree', mi.is_gluten_free,
                    'preparationTime', mi.preparation_time,
                    'ingredients', mi.ingredients,
                    'allergens', mi.allergens,
                    'nutritionInfo', mi.nutrition_info
                  )
                  ORDER BY mi.sort_order ASC, mi.name ASC
                )
                FROM menu_items mi
                WHERE mi.category_id = mc.id AND mi.is_available = true
              )
            )
            ORDER BY mc.sort_order ASC, mc.name ASC
          ) FILTER (WHERE mc.id IS NOT NULL) as menu_categories
        FROM restaurants r
        LEFT JOIN menu_categories mc ON r.id = mc.restaurant_id AND mc.is_active = true
        WHERE r.id = $1 AND r.is_active = true
        GROUP BY r.id
      `;

      const result = await this.query(query, [restaurantId]);
      const restaurant = result.rows[0];

      if (restaurant) {
        restaurant.menu_categories = restaurant.menu_categories || [];
      }

      return restaurant;
    } catch (error) {
      throw error;
    }
  }

  // Search restaurants
  async searchRestaurants(searchParams) {
    try {
      const { 
        q, 
        cuisineTypes = [], 
        minRating = 0, 
        maxDeliveryFee = null,
        features = [],
        acceptingOrders = true,
        page = 1, 
        limit = 20 
      } = searchParams;

      const offset = (page - 1) * limit;
      let whereConditions = ['r.is_active = true'];
      let params = [];
      let paramIndex = 1;

      if (acceptingOrders) {
        whereConditions.push('r.accepting_orders = true');
      }

      if (q) {
        whereConditions.push(`(r.name ILIKE $${paramIndex} OR r.description ILIKE $${paramIndex})`);
        params.push(`%${q}%`);
        paramIndex++;
      }

      if (cuisineTypes.length > 0) {
        whereConditions.push(`r.cuisine_types && $${paramIndex}`);
        params.push(cuisineTypes);
        paramIndex++;
      }

      if (minRating > 0) {
        whereConditions.push(`r.rating >= $${paramIndex}`);
        params.push(minRating);
        paramIndex++;
      }

      if (maxDeliveryFee !== null) {
        whereConditions.push(`r.delivery_fee <= $${paramIndex}`);
        params.push(maxDeliveryFee);
        paramIndex++;
      }

      if (features.length > 0) {
        whereConditions.push(`r.features && $${paramIndex}`);
        params.push(features);
        paramIndex++;
      }

      const whereClause = whereConditions.join(' AND ');

      const query = `
        SELECT 
          r.id, r.name, r.description, r.image, r.logo, r.slug,
          r.rating, r.review_count, r.cuisine_types, r.delivery_fee,
          r.minimum_order, r.estimated_delivery_time, r.features,
          r.address
        FROM restaurants r
        WHERE ${whereClause}
        ORDER BY r.rating DESC, r.review_count DESC, r.name ASC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const countQuery = `
        SELECT COUNT(*) as count
        FROM restaurants r
        WHERE ${whereClause}
      `;

      params.push(limit, offset);
      const countParams = params.slice(0, -2);

      const [results, countResult] = await Promise.all([
        this.query(query, params),
        this.query(countQuery, countParams)
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        restaurants: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get restaurants by cuisine type
  async getRestaurantsByCuisine(cuisineType, limit = 10) {
    try {
      const query = `
        SELECT 
          id, name, description, image, logo, slug, rating, review_count,
          cuisine_types, delivery_fee, minimum_order, estimated_delivery_time
        FROM restaurants
        WHERE $1 = ANY(cuisine_types) 
        AND is_active = true 
        AND accepting_orders = true
        ORDER BY rating DESC, review_count DESC
        LIMIT $2
      `;

      const result = await this.query(query, [cuisineType, limit]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Get popular restaurants
  async getPopularRestaurants(limit = 10) {
    try {
      const query = `
        SELECT 
          r.*,
          COALESCE(order_stats.order_count, 0) as recent_order_count
        FROM restaurants r
        LEFT JOIN (
          SELECT 
            restaurant_id,
            COUNT(*) as order_count
          FROM orders
          WHERE created_at >= NOW() - INTERVAL '7 days'
          AND status = 'delivered'
          GROUP BY restaurant_id
        ) order_stats ON r.id = order_stats.restaurant_id
        WHERE r.is_active = true AND r.accepting_orders = true
        ORDER BY order_stats.order_count DESC NULLS LAST, r.rating DESC, r.review_count DESC
        LIMIT $1
      `;

      const result = await this.query(query, [limit]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Update restaurant status
  async updateAcceptingOrders(restaurantId, acceptingOrders) {
    try {
      return await this.updateById(restaurantId, { accepting_orders: acceptingOrders });
    } catch (error) {
      throw error;
    }
  }

  // Get restaurant statistics
  async getRestaurantStats(restaurantId, days = 30) {
    try {
      const query = `
        SELECT 
          r.name,
          r.rating,
          r.review_count,
          COALESCE(order_stats.total_orders, 0) as total_orders,
          COALESCE(order_stats.completed_orders, 0) as completed_orders,
          COALESCE(order_stats.cancelled_orders, 0) as cancelled_orders,
          COALESCE(order_stats.total_revenue, 0) as total_revenue,
          COALESCE(order_stats.avg_order_value, 0) as avg_order_value
        FROM restaurants r
        LEFT JOIN (
          SELECT 
            restaurant_id,
            COUNT(*) as total_orders,
            COUNT(*) FILTER (WHERE status = 'delivered') as completed_orders,
            COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_orders,
            SUM(total_amount) FILTER (WHERE status = 'delivered') as total_revenue,
            AVG(total_amount) FILTER (WHERE status = 'delivered') as avg_order_value
          FROM orders
          WHERE created_at >= NOW() - INTERVAL '${days} days'
          GROUP BY restaurant_id
        ) order_stats ON r.id = order_stats.restaurant_id
        WHERE r.id = $1
      `;

      const result = await this.query(query, [restaurantId]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Check if restaurant is open
  async isRestaurantOpen(restaurantId) {
    try {
      const restaurant = await this.findById(restaurantId);
      if (!restaurant || !restaurant.accepting_orders) {
        return false;
      }

      const now = new Date();
      const dayOfWeek = now.toLocaleLowerCase().substring(0, 3); // mon, tue, etc.
      const currentTime = now.toTimeString().substring(0, 5); // HH:MM format

      const openingHours = restaurant.opening_hours;
      if (!openingHours || !openingHours[dayOfWeek]) {
        return false;
      }

      const dayHours = openingHours[dayOfWeek];
      if (dayHours.closed) {
        return false;
      }

      return currentTime >= dayHours.open && currentTime <= dayHours.close;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Restaurant;
