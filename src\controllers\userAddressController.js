// UserAddress Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const UserAddress = require('../models/UserAddress');

class UserAddressController {
  // Get user's addresses
  static async getUserAddresses(req, res) {
    try {
      const userId = req.user.id;
      const activeOnly = req.query.activeOnly !== 'false';

      const userAddressModel = new UserAddress();
      const addresses = await userAddressModel.getAddressesByUser(userId, activeOnly);

      return ResponseHelper.success(res, { addresses }, 'User addresses retrieved successfully');
    } catch (error) {
      console.error('Get user addresses error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve user addresses', 500);
    }
  }

  // Get user's default address
  static async getDefaultAddress(req, res) {
    try {
      const userId = req.user.id;
      const userAddressModel = new UserAddress();

      const address = await userAddressModel.getDefaultAddress(userId);

      if (!address) {
        return ResponseHelper.error(res, 'No default address found', 404, {
          code: 'NOT_FOUND',
          message: 'User has no default address set'
        });
      }

      return ResponseHelper.success(res, address, 'Default address retrieved successfully');
    } catch (error) {
      console.error('Get default address error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve default address', 500);
    }
  }

  // Create new address
  static async createAddress(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;

      // Normalize field names from frontend format to backend format
      const addressData = {
        ...req.body,
        user_id: userId,
        zip_code: req.body.zipCode || req.body.zip_code,
        delivery_instructions: req.body.deliveryInstructions || req.body.delivery_instructions,
        is_default: req.body.isDefault !== undefined ? req.body.isDefault : req.body.is_default
      };

      // Remove frontend field names to avoid conflicts
      delete addressData.zipCode;
      delete addressData.deliveryInstructions;
      delete addressData.isDefault;

      const userAddressModel = new UserAddress();

      const address = await userAddressModel.createAddress(addressData);

      return ResponseHelper.success(res, address, 'Address created successfully', 201);
    } catch (error) {
      console.error('Create address error:', error);
      return ResponseHelper.error(res, 'Failed to create address', 500);
    }
  }

  // Update address
  static async updateAddress(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const addressId = req.params.id;
      const updateData = req.body;
      const userAddressModel = new UserAddress();

      const address = await userAddressModel.updateAddress(addressId, userId, updateData);

      return ResponseHelper.success(res, address, 'Address updated successfully');
    } catch (error) {
      console.error('Update address error:', error);

      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to update address', 500);
    }
  }

  // Set default address
  static async setDefaultAddress(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const addressId = req.params.id;
      const userAddressModel = new UserAddress();

      const result = await userAddressModel.setDefaultAddress(addressId, userId);

      if (!result) {
        return ResponseHelper.error(res, 'Address not found', 404, {
          code: 'NOT_FOUND',
          message: 'Address not found or access denied'
        });
      }

      return ResponseHelper.success(res, { updated: true }, 'Default address updated successfully');
    } catch (error) {
      console.error('Set default address error:', error);

      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to set default address', 500);
    }
  }

  // Delete address
  static async deleteAddress(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const addressId = req.params.id;
      const userAddressModel = new UserAddress();

      const result = await userAddressModel.deleteAddress(addressId, userId);

      if (!result) {
        return ResponseHelper.error(res, 'Address not found', 404, {
          code: 'NOT_FOUND',
          message: 'Address not found or access denied'
        });
      }

      return ResponseHelper.success(res, { deleted: true }, 'Address deleted successfully');
    } catch (error) {
      console.error('Delete address error:', error);

      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to delete address', 500);
    }
  }

  // Validate delivery address
  static async validateAddress(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const addressData = req.body;
      const userAddressModel = new UserAddress();

      const validation = await userAddressModel.validateDeliveryAddress(addressData);

      return ResponseHelper.success(res, validation, 'Address validation completed');
    } catch (error) {
      console.error('Validate address error:', error);
      return ResponseHelper.error(res, 'Failed to validate address', 500);
    }
  }

  // Calculate delivery distance
  static async calculateDeliveryDistance(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const addressId = req.params.addressId;
      const restaurantId = req.params.restaurantId;
      const userAddressModel = new UserAddress();

      const distance = await userAddressModel.calculateDeliveryDistance(addressId, restaurantId);

      return ResponseHelper.success(res, distance, 'Delivery distance calculated successfully');
    } catch (error) {
      console.error('Calculate delivery distance error:', error);

      if (error.message.includes('not found')) {
        return ResponseHelper.error(res, error.message, 404);
      }

      return ResponseHelper.error(res, 'Failed to calculate delivery distance', 500);
    }
  }
}

module.exports = UserAddressController;
