// Logging Utility
const config = require('../config/app');

class Logger {
  static info(message, ...args) {
    if (config.logging.level === 'info' || config.logging.debug) {
      console.log(`ℹ️  ${message}`, ...args);
    }
  }

  static warn(message, ...args) {
    console.warn(`⚠️  ${message}`, ...args);
  }

  static error(message, ...args) {
    console.error(`❌ ${message}`, ...args);
  }

  static success(message, ...args) {
    console.log(`✅ ${message}`, ...args);
  }

  static debug(message, ...args) {
    if (config.logging.debug) {
      console.log(`🐛 ${message}`, ...args);
    }
  }
}

module.exports = Logger;
