// Notification Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');

class NotificationController {
  // Get user notifications
  static async getNotifications(req, res) {
    try {
      const userId = req.user.id;
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);

      // For now, return mock notifications
      // In a real implementation, you would have a notifications table
      const mockNotifications = [
        {
          id: '1',
          title: 'Order Delivered',
          message: 'Your order from Pizza Palace has been delivered!',
          type: 'order_update',
          isRead: false,
          createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
          data: {
            orderId: 'order-123',
            restaurantName: 'Pizza Palace'
          }
        },
        {
          id: '2',
          title: 'New Promotion Available',
          message: 'Get 20% off your next order with code SAVE20',
          type: 'promotion',
          isRead: false,
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
          data: {
            promoCode: 'SAVE20',
            discount: 20
          }
        },
        {
          id: '3',
          title: 'Order Confirmed',
          message: 'Your order from Burger House has been confirmed',
          type: 'order_update',
          isRead: true,
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
          data: {
            orderId: 'order-122',
            restaurantName: 'Burger House'
          }
        }
      ];

      const notifications = mockNotifications.slice((page - 1) * limit, page * limit);
      const total = mockNotifications.length;

      const result = {
        notifications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

      return ResponseHelper.success(res, result, 'Notifications retrieved successfully');
    } catch (error) {
      console.error('Get notifications error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve notifications', 500);
    }
  }

  // Mark all notifications as read
  static async markAllAsRead(req, res) {
    try {
      const userId = req.user.id;

      // In a real implementation, you would update the notifications table
      // For now, just return success

      return ResponseHelper.success(res, { updated: true }, 'All notifications marked as read');
    } catch (error) {
      console.error('Mark all as read error:', error);
      return ResponseHelper.error(res, 'Failed to mark notifications as read', 500);
    }
  }

  // Mark specific notification as read
  static async markAsRead(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const userId = req.user.id;
      const notificationId = req.params.id;

      // In a real implementation, you would update the specific notification
      // For now, just return success

      return ResponseHelper.success(res, { updated: true }, 'Notification marked as read');
    } catch (error) {
      console.error('Mark as read error:', error);
      return ResponseHelper.error(res, 'Failed to mark notification as read', 500);
    }
  }

  // Get unread notification count
  static async getUnreadCount(req, res) {
    try {
      const userId = req.user.id;

      // In a real implementation, you would count unread notifications
      const unreadCount = 2; // Mock count

      return ResponseHelper.success(res, { unreadCount }, 'Unread count retrieved successfully');
    } catch (error) {
      console.error('Get unread count error:', error);
      return ResponseHelper.error(res, 'Failed to get unread count', 500);
    }
  }
}

module.exports = NotificationController;
