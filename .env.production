# Production Environment Variables for Food Delivery Backend

# Server Configuration
NODE_ENV=production
PORT=5000

# Database Configuration
DATABASE_URL=your_production_database_url_here

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_here
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration - Add your frontend domains
CORS_ORIGIN=https://your-frontend-domain.cloudworkstations.dev,https://your-production-domain.com
CORS_CREDENTIALS=true

# Socket.IO CORS
SOCKET_CORS_ORIGIN=https://your-frontend-domain.cloudworkstations.dev,https://your-production-domain.com
SOCKET_CORS_CREDENTIALS=true

# Redis Configuration (optional)
REDIS_URL=your_redis_url_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12

# Logging
LOG_LEVEL=info
LOG_FILE=true

# External Services (when implemented)
STRIPE_SECRET_KEY=your_stripe_secret_key
EMAIL_SERVICE_API_KEY=your_email_service_key
SMS_SERVICE_API_KEY=your_sms_service_key
