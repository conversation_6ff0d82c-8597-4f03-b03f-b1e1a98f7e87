// Query Optimization Service
// Provides optimized database queries with caching and performance improvements

const cacheService = require('./cacheService');
const databaseManager = require('../config/database');

class QueryOptimizationService {
  constructor() {
    this.pool = null;
  }

  // Initialize with database pool
  initialize() {
    this.pool = databaseManager.getPool();
  }

  // Optimized user favorites query with caching
  async getUserFavorites(userId, type = null) {
    const cacheKey = cacheService.generateKey('user_favorites', userId, type || 'all');
    
    // Try cache first
    const cached = await cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      let restaurants = [];
      let menuItems = [];

      if (!type || type === 'restaurants') {
        // Optimized restaurant favorites query
        const restaurantsQuery = `
          SELECT
            uf.id as favorite_id,
            uf.created_at as favorited_at,
            r.id, r.name, r.description, r.image, r.logo, r.slug,
            r.rating, r.review_count, r.cuisine_types, r.delivery_fee,
            r.minimum_order, r.estimated_delivery_time, r.accepting_orders
          FROM user_favorites uf
          INNER JOIN restaurants r ON uf.item_id = r.id
          WHERE uf.user_id = $1 AND uf.item_type = 'restaurant'
          AND r.is_active = true
          ORDER BY uf.created_at DESC
          LIMIT 50
        `;

        const restaurantsResult = await this.pool.query(restaurantsQuery, [userId]);
        restaurants = restaurantsResult.rows;
      }

      if (!type || type === 'menu_items') {
        // Optimized menu items favorites query with single JOIN
        const menuItemsQuery = `
          SELECT
            uf.id as favorite_id,
            uf.created_at as favorited_at,
            mi.id, mi.name, mi.description, mi.price, mi.image,
            mi.is_available, mi.is_popular, mi.is_vegetarian, mi.is_vegan, mi.is_gluten_free,
            mi.preparation_time, mi.ingredients, mi.allergens,
            r.id as restaurant_id, r.name as restaurant_name, r.logo as restaurant_logo,
            mc.name as category_name
          FROM user_favorites uf
          INNER JOIN menu_items mi ON uf.item_id = mi.id
          INNER JOIN restaurants r ON mi.restaurant_id = r.id
          INNER JOIN menu_categories mc ON mi.category_id = mc.id
          WHERE uf.user_id = $1 AND uf.item_type = 'menu_item'
          AND mi.is_available = true AND r.is_active = true
          ORDER BY uf.created_at DESC
          LIMIT 50
        `;

        const menuItemsResult = await this.pool.query(menuItemsQuery, [userId]);
        menuItems = menuItemsResult.rows;
      }

      const result = { restaurants, menuItems };
      
      // Cache for 10 minutes
      await cacheService.set(cacheKey, result, 600);
      
      return result;
    } catch (error) {
      console.error('Optimized user favorites query error:', error);
      throw error;
    }
  }

  // Optimized order details with single query
  async getOrderWithDetails(orderId, userId = null) {
    const cacheKey = cacheService.generateKey('order_details', orderId);

    // Try cache first (short cache for orders)
    const cached = await cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const query = `
        SELECT
          o.id, o.order_number, o.status, o.total_amount, o.delivery_fee,
          o.tax, o.discount, o.created_at, o.updated_at,
          o.estimated_delivery_time, o.actual_delivery_time,
          o.delivery_address, o.special_instructions, o.payment_method_id,
          r.id as restaurant_id, r.name as restaurant_name,
          r.image as restaurant_image, r.phone as restaurant_phone,
          u.first_name, u.last_name, u.phone as user_phone,
          json_agg(
            json_build_object(
              'id', oi.id,
              'menuItemId', oi.menu_item_id,
              'menuItemName', mi.name,
              'menuItemImage', mi.image,
              'quantity', oi.quantity,
              'unitPrice', oi.unit_price,
              'totalPrice', oi.total_price,
              'customizations', oi.customizations,
              'specialInstructions', oi.special_instructions
            ) ORDER BY oi.id
          ) FILTER (WHERE oi.id IS NOT NULL) as items
        FROM orders o
        INNER JOIN restaurants r ON o.restaurant_id = r.id
        INNER JOIN users u ON o.user_id = u.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN menu_items mi ON oi.menu_item_id = mi.id
        WHERE o.id = $1 ${userId ? 'AND o.user_id = $2' : ''}
        GROUP BY o.id, r.id, r.name, r.image, r.phone, u.first_name, u.last_name, u.phone
      `;

      const params = userId ? [orderId, userId] : [orderId];
      const result = await this.pool.query(query, params);

      if (result.rows.length === 0) {
        return null;
      }

      const orderDetails = result.rows[0];

      // Cache for 2 minutes (orders change frequently)
      await cacheService.set(cacheKey, orderDetails, 120);

      return orderDetails;
    } catch (error) {
      console.error('Optimized order details query error:', error);
      throw error;
    }
  }

  // Optimized user orders list with pagination
  async getUserOrders(userId, status = null, page = 1, limit = 20) {
    const cacheKey = cacheService.generateKey('user_orders', userId, status || 'all', page, limit);
    
    // Try cache first
    const cached = await cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const offset = (page - 1) * limit;
      let whereClause = 'o.user_id = $1';
      let params = [userId];
      let paramIndex = 1;

      if (status) {
        paramIndex++;
        whereClause += ` AND o.status = $${paramIndex}`;
        params.push(status);
      }

      // Single query for orders with restaurant info and item count
      const ordersQuery = `
        SELECT
          o.id, o.order_number, o.status, o.total_amount, o.created_at,
          o.estimated_delivery_time, o.actual_delivery_time,
          r.name as restaurant_name, r.image as restaurant_image,
          r.logo as restaurant_logo,
          COUNT(oi.id) as item_count
        FROM orders o
        INNER JOIN restaurants r ON o.restaurant_id = r.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE ${whereClause}
        GROUP BY o.id, r.name, r.image, r.logo
        ORDER BY o.created_at DESC
        LIMIT $${paramIndex + 1} OFFSET $${paramIndex + 2}
      `;

      const countQuery = `
        SELECT COUNT(*) as total
        FROM orders o
        WHERE ${whereClause}
      `;

      params.push(limit, offset);

      const [ordersResult, countResult] = await Promise.all([
        this.pool.query(ordersQuery, params),
        this.pool.query(countQuery, params.slice(0, -2)) // Remove limit and offset for count
      ]);

      const total = parseInt(countResult.rows[0].total);
      const result = {
        orders: ordersResult.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

      // Cache for 5 minutes
      await cacheService.set(cacheKey, result, 300);
      
      return result;
    } catch (error) {
      console.error('Optimized user orders query error:', error);
      throw error;
    }
  }

  // Optimized restaurant with menu query
  async getRestaurantWithMenu(restaurantId, categoryId = null) {
    const cacheKey = cacheService.generateKey('restaurant_menu', restaurantId, categoryId || 'all');
    
    // Try cache first
    const cached = await cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // Get restaurant details
      const restaurantQuery = `
        SELECT 
          r.*,
          AVG(rev.rating) as avg_rating,
          COUNT(rev.id) as review_count
        FROM restaurants r
        LEFT JOIN reviews rev ON r.id = rev.restaurant_id
        WHERE r.id = $1 AND r.is_active = true
        GROUP BY r.id
      `;

      // Get menu items with categories in single query
      let menuQuery = `
        SELECT
          mi.id, mi.name, mi.description, mi.price, mi.image,
          mi.is_available, mi.is_popular, mi.is_vegetarian, mi.is_vegan, mi.is_gluten_free,
          mi.preparation_time, mi.ingredients, mi.allergens,
          mc.id as category_id, mc.name as category_name, mc.sort_order as category_order
        FROM menu_items mi
        INNER JOIN menu_categories mc ON mi.category_id = mc.id
        WHERE mi.restaurant_id = $1 AND mi.is_available = true
      `;

      let menuParams = [restaurantId];

      if (categoryId) {
        menuQuery += ' AND mc.id = $2';
        menuParams.push(categoryId);
      }

      menuQuery += ' ORDER BY mc.sort_order, mi.sort_order, mi.name';

      const [restaurantResult, menuResult] = await Promise.all([
        this.pool.query(restaurantQuery, [restaurantId]),
        this.pool.query(menuQuery, menuParams)
      ]);

      if (restaurantResult.rows.length === 0) {
        return null;
      }

      const restaurant = restaurantResult.rows[0];
      
      // Group menu items by category
      const categoriesMap = new Map();
      menuResult.rows.forEach(item => {
        const categoryKey = item.category_id;
        if (!categoriesMap.has(categoryKey)) {
          categoriesMap.set(categoryKey, {
            id: item.category_id,
            name: item.category_name,
            sort_order: item.category_order,
            items: []
          });
        }
        
        categoriesMap.get(categoryKey).items.push({
          id: item.id,
          name: item.name,
          description: item.description,
          price: item.price,
          image: item.image,
          is_available: item.is_available,
          is_popular: item.is_popular,
          is_vegetarian: item.is_vegetarian,
          is_vegan: item.is_vegan,
          is_gluten_free: item.is_gluten_free,
          preparation_time: item.preparation_time,
          ingredients: item.ingredients,
          allergens: item.allergens
        });
      });

      restaurant.menu_categories = Array.from(categoriesMap.values())
        .sort((a, b) => a.sort_order - b.sort_order);

      // Cache for 15 minutes
      await cacheService.set(cacheKey, restaurant, 900);
      
      return restaurant;
    } catch (error) {
      console.error('Optimized restaurant with menu query error:', error);
      throw error;
    }
  }

  // Optimized popular items query
  async getPopularItems(type = 'restaurants', limit = 10, days = 30) {
    const cacheKey = cacheService.generateKey('popular_items', type, limit, days);
    
    // Try cache first (longer cache for popular items)
    const cached = await cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      let query;
      let params = [days, limit];

      if (type === 'restaurants') {
        query = `
          SELECT 
            r.id, r.name, r.description, r.image, r.logo, r.slug,
            r.rating, r.cuisine_types, r.delivery_fee, r.minimum_order,
            r.estimated_delivery_time, r.accepting_orders,
            COUNT(uf.id) as favorite_count,
            COUNT(DISTINCT o.id) as order_count
          FROM restaurants r
          LEFT JOIN user_favorites uf ON (
            (uf.restaurant_id = r.id OR (uf.item_id = r.id AND uf.item_type = 'restaurant'))
            AND uf.created_at >= NOW() - INTERVAL '$1 days'
          )
          LEFT JOIN orders o ON r.id = o.restaurant_id AND o.created_at >= NOW() - INTERVAL '$1 days'
          WHERE r.is_active = true
          GROUP BY r.id
          ORDER BY (COUNT(uf.id) + COUNT(DISTINCT o.id)) DESC, r.rating DESC
          LIMIT $2
        `;
      } else {
        query = `
          SELECT 
            mi.id, mi.name, mi.description, mi.price, mi.image,
            mi.is_vegetarian, mi.is_vegan, mi.is_gluten_free, mi.preparation_time,
            r.id as restaurant_id, r.name as restaurant_name, r.logo as restaurant_logo,
            mc.name as category_name,
            COUNT(uf.id) as favorite_count,
            COUNT(DISTINCT oi.id) as order_count
          FROM menu_items mi
          INNER JOIN restaurants r ON mi.restaurant_id = r.id
          LEFT JOIN menu_categories mc ON mi.category_id = mc.id
          LEFT JOIN user_favorites uf ON (
            (uf.menu_item_id = mi.id OR (uf.item_id = mi.id AND uf.item_type = 'menu_item'))
            AND uf.created_at >= NOW() - INTERVAL '$1 days'
          )
          LEFT JOIN order_items oi ON mi.id = oi.menu_item_id 
            AND oi.created_at >= NOW() - INTERVAL '$1 days'
          WHERE mi.is_available = true AND r.is_active = true
          GROUP BY mi.id, r.id, r.name, r.logo, mc.name
          ORDER BY (COUNT(uf.id) + COUNT(DISTINCT oi.id)) DESC, mi.is_popular DESC
          LIMIT $2
        `;
      }

      const result = await this.pool.query(query, params);
      
      // Cache for 1 hour
      await cacheService.set(cacheKey, result.rows, 3600);
      
      return result.rows;
    } catch (error) {
      console.error('Optimized popular items query error:', error);
      throw error;
    }
  }

  // Clear related caches when data changes
  async invalidateUserCaches(userId) {
    await Promise.all([
      cacheService.clearUserCache(userId),
      cacheService.delPattern(cacheService.generateKey('user_orders', userId, '*')),
      cacheService.delPattern(cacheService.generateKey('user_favorites', userId, '*'))
    ]);
  }

  async invalidateRestaurantCaches(restaurantId) {
    await Promise.all([
      cacheService.clearRestaurantCache(restaurantId),
      cacheService.clearMenuCache(restaurantId),
      cacheService.delPattern(cacheService.generateKey('popular_items', '*')),
      cacheService.delPattern(cacheService.generateKey('search', '*'))
    ]);
  }

  async invalidateOrderCaches(orderId, userId = null) {
    const promises = [
      cacheService.del(cacheService.generateKey('order_details', orderId))
    ];
    
    if (userId) {
      promises.push(
        cacheService.delPattern(cacheService.generateKey('user_orders', userId, '*'))
      );
    }
    
    await Promise.all(promises);
  }
}

// Export singleton instance
module.exports = new QueryOptimizationService();
