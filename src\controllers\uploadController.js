// Upload Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const UploadService = require('../services/uploadService');

class UploadController {
  // Upload menu item image
  static async uploadMenuItemImage(req, res) {
    try {
      if (!req.file) {
        return ResponseHelper.error(res, 'No image file provided', 400, {
          code: 'VALIDATION_ERROR',
          message: 'Image file is required'
        });
      }

      const result = await UploadService.uploadMenuItemImage(req.file);

      return ResponseHelper.success(res, result, 'Menu item image uploaded successfully');
    } catch (error) {
      console.error('Upload menu item image error:', error);

      if (error.message.includes('Invalid file type') || error.message.includes('File size too large')) {
        return ResponseHelper.error(res, error.message, 400);
      }

      return ResponseHelper.error(res, 'Failed to upload menu item image', 500);
    }
  }

  // Upload user avatar
  static async uploadUserAvatar(req, res) {
    try {
      if (!req.file) {
        return ResponseHelper.error(res, 'No avatar file provided', 400, {
          code: 'VALIDATION_ERROR',
          message: 'Avatar file is required'
        });
      }

      const result = await UploadService.uploadUserAvatar(req.file);

      return ResponseHelper.success(res, result, 'User avatar uploaded successfully');
    } catch (error) {
      console.error('Upload user avatar error:', error);

      if (error.message.includes('Invalid file type') || error.message.includes('File size too large')) {
        return ResponseHelper.error(res, error.message, 400);
      }

      return ResponseHelper.error(res, 'Failed to upload user avatar', 500);
    }
  }
}

module.exports = UploadController;
