// User Service
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
// Note: Stripe will be initialized when STRIPE_SECRET_KEY is available
let stripe = null;
if (process.env.STRIPE_SECRET_KEY) {
  stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
}
const databaseManager = require('../config/database');

class UserService {
  // Get user profile with addresses and payment methods
  static async getProfile(userId) {
    const pool = databaseManager.getPool();

    try {
      // Get user data
      const userResult = await pool.query(`
        SELECT 
          id, email, first_name, last_name, phone, avatar_url, 
          email_verified, phone_verified, created_at
        FROM users 
        WHERE id = $1 AND is_active = true
      `, [userId]);

      if (userResult.rows.length === 0) {
        throw new Error('User not found');
      }

      const user = userResult.rows[0];

      // Get user addresses
      const addressesResult = await pool.query(`
        SELECT 
          id, type, label, street_address, city, state, postal_code, 
          country, latitude, longitude, is_default, created_at
        FROM user_addresses 
        WHERE user_id = $1
        ORDER BY is_default DESC, created_at DESC
      `, [userId]);

      // Get payment methods
      const paymentMethodsResult = await pool.query(`
        SELECT 
          id, stripe_payment_method_id, type, last_four, 
          brand, exp_month, exp_year, is_default, created_at
        FROM payment_methods 
        WHERE user_id = $1
        ORDER BY is_default DESC, created_at DESC
      `, [userId]);

      // Format response
      return {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phone: user.phone,
        avatarUrl: user.avatar_url,
        emailVerified: user.email_verified,
        phoneVerified: user.phone_verified,
        addresses: addressesResult.rows.map(addr => ({
          id: addr.id,
          type: addr.type,
          label: addr.label,
          streetAddress: addr.street_address,
          city: addr.city,
          state: addr.state,
          postalCode: addr.postal_code,
          country: addr.country,
          latitude: addr.latitude ? parseFloat(addr.latitude) : null,
          longitude: addr.longitude ? parseFloat(addr.longitude) : null,
          isDefault: addr.is_default,
          createdAt: addr.created_at
        })),
        paymentMethods: paymentMethodsResult.rows.map(pm => ({
          id: pm.id,
          type: pm.type,
          lastFour: pm.last_four,
          brand: pm.brand,
          expMonth: pm.exp_month,
          expYear: pm.exp_year,
          isDefault: pm.is_default,
          createdAt: pm.created_at
        })),
        createdAt: user.created_at
      };
    } catch (error) {
      throw error;
    }
  }

  // Update user profile
  static async updateProfile(userId, updateData) {
    const pool = databaseManager.getPool();
    const { firstName, lastName, phone } = updateData;

    try {
      // Build update query dynamically
      const updates = [];
      const values = [userId];
      let valueIndex = 2;

      if (firstName !== undefined) {
        updates.push(`first_name = $${valueIndex++}`);
        values.push(firstName);
      }

      if (lastName !== undefined) {
        updates.push(`last_name = $${valueIndex++}`);
        values.push(lastName);
      }

      if (phone !== undefined) {
        updates.push(`phone = $${valueIndex++}`);
        values.push(phone);
      }

      if (updates.length === 0) {
        // No updates to make
        return this.getProfile(userId);
      }

      // Update user
      await pool.query(`
        UPDATE users 
        SET ${updates.join(', ')} 
        WHERE id = $1
      `, values);

      // Return updated profile
      return this.getProfile(userId);
    } catch (error) {
      throw error;
    }
  }

  // Upload avatar
  static async uploadAvatar(userId, file) {
    const pool = databaseManager.getPool();

    try {
      // In a real application, you would upload to a cloud storage service
      // For this example, we'll simulate a successful upload and return a URL

      // Generate a unique filename
      const filename = `${uuidv4()}-${file.originalname}`;

      // In a real app, this would be a cloud storage URL
      const avatarUrl = `/uploads/avatars/${filename}`;

      // Update user with new avatar URL
      await pool.query(
        'UPDATE users SET avatar_url = $1 WHERE id = $2',
        [avatarUrl, userId]
      );

      return avatarUrl;
    } catch (error) {
      throw error;
    }
  }

  // Get user addresses
  static async getAddresses(userId) {
    const pool = databaseManager.getPool();

    try {
      const addressesResult = await pool.query(`
        SELECT 
          id, type, label, street_address, city, state, postal_code, 
          country, latitude, longitude, is_default, created_at
        FROM user_addresses 
        WHERE user_id = $1
        ORDER BY is_default DESC, created_at DESC
      `, [userId]);

      return addressesResult.rows.map(addr => ({
        id: addr.id,
        type: addr.type,
        label: addr.label,
        streetAddress: addr.street_address,
        city: addr.city,
        state: addr.state,
        postalCode: addr.postal_code,
        country: addr.country,
        latitude: addr.latitude ? parseFloat(addr.latitude) : null,
        longitude: addr.longitude ? parseFloat(addr.longitude) : null,
        isDefault: addr.is_default,
        createdAt: addr.created_at
      }));
    } catch (error) {
      throw error;
    }
  }

  // Add new address
  static async addAddress(userId, addressData) {
    const pool = databaseManager.getPool();
    const {
      type = 'other',
      label,
      streetAddress,
      city,
      state,
      postalCode,
      country = 'US',
      latitude,
      longitude,
      isDefault = false
    } = addressData;

    try {
      // If this is the default address, unset any existing default
      if (isDefault) {
        await pool.query(
          'UPDATE user_addresses SET is_default = false WHERE user_id = $1',
          [userId]
        );
      }

      // Insert new address
      const result = await pool.query(`
        INSERT INTO user_addresses (
          user_id, type, label, street_address, city, state, postal_code,
          country, latitude, longitude, is_default
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING 
          id, type, label, street_address, city, state, postal_code,
          country, latitude, longitude, is_default, created_at
      `, [
        userId, type, label, streetAddress, city, state, postalCode,
        country, latitude, longitude, isDefault
      ]);

      const address = result.rows[0];

      return {
        id: address.id,
        type: address.type,
        label: address.label,
        streetAddress: address.street_address,
        city: address.city,
        state: address.state,
        postalCode: address.postal_code,
        country: address.country,
        latitude: address.latitude ? parseFloat(address.latitude) : null,
        longitude: address.longitude ? parseFloat(address.longitude) : null,
        isDefault: address.is_default,
        createdAt: address.created_at
      };
    } catch (error) {
      throw error;
    }
  }

  // Update address
  static async updateAddress(userId, addressId, updateData) {
    const pool = databaseManager.getPool();
    const {
      type,
      label,
      streetAddress,
      city,
      state,
      postalCode,
      country,
      latitude,
      longitude,
      isDefault
    } = updateData;

    try {
      // Check if address exists and belongs to user
      const addressCheck = await pool.query(
        'SELECT id FROM user_addresses WHERE id = $1 AND user_id = $2',
        [addressId, userId]
      );

      if (addressCheck.rows.length === 0) {
        return null; // Address not found or doesn't belong to user
      }

      // Build update query dynamically
      const updates = [];
      const values = [addressId, userId];
      let valueIndex = 3;

      if (type !== undefined) {
        updates.push(`type = $${valueIndex++}`);
        values.push(type);
      }

      if (label !== undefined) {
        updates.push(`label = $${valueIndex++}`);
        values.push(label);
      }

      if (streetAddress !== undefined) {
        updates.push(`street_address = $${valueIndex++}`);
        values.push(streetAddress);
      }

      if (city !== undefined) {
        updates.push(`city = $${valueIndex++}`);
        values.push(city);
      }

      if (state !== undefined) {
        updates.push(`state = $${valueIndex++}`);
        values.push(state);
      }

      if (postalCode !== undefined) {
        updates.push(`postal_code = $${valueIndex++}`);
        values.push(postalCode);
      }

      if (country !== undefined) {
        updates.push(`country = $${valueIndex++}`);
        values.push(country);
      }

      if (latitude !== undefined) {
        updates.push(`latitude = $${valueIndex++}`);
        values.push(latitude);
      }

      if (longitude !== undefined) {
        updates.push(`longitude = $${valueIndex++}`);
        values.push(longitude);
      }

      if (isDefault !== undefined) {
        updates.push(`is_default = $${valueIndex++}`);
        values.push(isDefault);
      }

      if (updates.length === 0) {
        // No updates to make, return current address
        const currentAddress = await pool.query(
          `SELECT 
            id, type, label, street_address, city, state, postal_code,
            country, latitude, longitude, is_default, created_at
          FROM user_addresses 
          WHERE id = $1 AND user_id = $2`,
          [addressId, userId]
        );

        const address = currentAddress.rows[0];

        return {
          id: address.id,
          type: address.type,
          label: address.label,
          streetAddress: address.street_address,
          city: address.city,
          state: address.state,
          postalCode: address.postal_code,
          country: address.country,
          latitude: address.latitude ? parseFloat(address.latitude) : null,
          longitude: address.longitude ? parseFloat(address.longitude) : null,
          isDefault: address.is_default,
          createdAt: address.created_at
        };
      }

      // If setting as default, unset any existing default
      if (isDefault) {
        await pool.query(
          'UPDATE user_addresses SET is_default = false WHERE user_id = $1 AND id != $2',
          [userId, addressId]
        );
      }

      // Update address
      const result = await pool.query(`
        UPDATE user_addresses 
        SET ${updates.join(', ')} 
        WHERE id = $1 AND user_id = $2
        RETURNING 
          id, type, label, street_address, city, state, postal_code,
          country, latitude, longitude, is_default, created_at
      `, values);

      const address = result.rows[0];

      return {
        id: address.id,
        type: address.type,
        label: address.label,
        streetAddress: address.street_address,
        city: address.city,
        state: address.state,
        postalCode: address.postal_code,
        country: address.country,
        latitude: address.latitude ? parseFloat(address.latitude) : null,
        longitude: address.longitude ? parseFloat(address.longitude) : null,
        isDefault: address.is_default,
        createdAt: address.created_at
      };
    } catch (error) {
      throw error;
    }
  }

  // Delete address
  static async deleteAddress(userId, addressId) {
    const pool = databaseManager.getPool();

    try {
      const result = await pool.query(
        'DELETE FROM user_addresses WHERE id = $1 AND user_id = $2 RETURNING id',
        [addressId, userId]
      );

      return result.rows.length > 0;
    } catch (error) {
      throw error;
    }
  }

  // Get payment methods
  static async getPaymentMethods(userId) {
    const pool = databaseManager.getPool();

    try {
      const paymentMethodsResult = await pool.query(`
        SELECT 
          id, stripe_payment_method_id, type, last_four, 
          brand, exp_month, exp_year, is_default, created_at
        FROM payment_methods 
        WHERE user_id = $1
        ORDER BY is_default DESC, created_at DESC
      `, [userId]);

      return paymentMethodsResult.rows.map(pm => ({
        id: pm.id,
        type: pm.type,
        lastFour: pm.last_four,
        brand: pm.brand,
        expMonth: pm.exp_month,
        expYear: pm.exp_year,
        isDefault: pm.is_default,
        createdAt: pm.created_at
      }));
    } catch (error) {
      throw error;
    }
  }

  // Add payment method
  static async addPaymentMethod(userId, stripePaymentMethodId, isDefault = false) {
    const pool = databaseManager.getPool();

    try {
      // Retrieve payment method details from Stripe
      const stripePaymentMethod = await stripe.paymentMethods.retrieve(stripePaymentMethodId);

      if (!stripePaymentMethod) {
        throw new Error('Invalid Stripe payment method');
      }

      const { card } = stripePaymentMethod;

      // If this is the default payment method, unset any existing default
      if (isDefault) {
        await pool.query(
          'UPDATE payment_methods SET is_default = false WHERE user_id = $1',
          [userId]
        );
      }

      // Insert new payment method
      const result = await pool.query(`
        INSERT INTO payment_methods (
          user_id, stripe_payment_method_id, type, last_four, 
          brand, exp_month, exp_year, is_default
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING 
          id, stripe_payment_method_id, type, last_four, 
          brand, exp_month, exp_year, is_default, created_at
      `, [
        userId,
        stripePaymentMethodId,
        stripePaymentMethod.type,
        card.last4,
        card.brand,
        card.exp_month,
        card.exp_year,
        isDefault
      ]);

      const paymentMethod = result.rows[0];

      return {
        id: paymentMethod.id,
        type: paymentMethod.type,
        lastFour: paymentMethod.last_four,
        brand: paymentMethod.brand,
        expMonth: paymentMethod.exp_month,
        expYear: paymentMethod.exp_year,
        isDefault: paymentMethod.is_default,
        createdAt: paymentMethod.created_at
      };
    } catch (error) {
      if (error.type === 'StripeInvalidRequestError') {
        throw new Error(`Stripe error: ${error.message}`);
      }
      throw error;
    }
  }

  // Delete payment method
  static async deletePaymentMethod(userId, paymentMethodId) {
    const pool = databaseManager.getPool();

    try {
      // Get Stripe payment method ID
      const paymentMethodResult = await pool.query(
        'SELECT stripe_payment_method_id, is_default FROM payment_methods WHERE id = $1 AND user_id = $2',
        [paymentMethodId, userId]
      );

      if (paymentMethodResult.rows.length === 0) {
        return false; // Payment method not found or doesn't belong to user
      }

      const { stripe_payment_method_id, is_default } = paymentMethodResult.rows[0];

      // Delete from database
      const result = await pool.query(
        'DELETE FROM payment_methods WHERE id = $1 AND user_id = $2 RETURNING id',
        [paymentMethodId, userId]
      );

      // If this was the default payment method, set a new default if available
      if (is_default) {
        const remainingPaymentMethods = await pool.query(
          'SELECT id FROM payment_methods WHERE user_id = $1 ORDER BY created_at DESC LIMIT 1',
          [userId]
        );

        if (remainingPaymentMethods.rows.length > 0) {
          await pool.query(
            'UPDATE payment_methods SET is_default = true WHERE id = $1',
            [remainingPaymentMethods.rows[0].id]
          );
        }
      }

      // Detach from Stripe (optional, depends on your use case)
      try {
        await stripe.paymentMethods.detach(stripe_payment_method_id);
      } catch (stripeError) {
        console.error('Failed to detach payment method from Stripe:', stripeError);
        // Continue with deletion even if Stripe detach fails
      }

      return result.rows.length > 0;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = UserService;
