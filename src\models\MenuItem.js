// MenuItem Model
const BaseModel = require('./BaseModel');

class MenuItem extends BaseModel {
  constructor() {
    super('menu_items');
  }

  // Get menu items with category info
  async getMenuItemsWithCategory(filters = {}) {
    try {
      const { categoryId, available = true, page = 1, limit = 20 } = filters;
      const offset = (page - 1) * limit;

      let whereConditions = [];
      let params = [];
      let paramIndex = 1;

      if (available !== null) {
        whereConditions.push(`mi.is_available = $${paramIndex}`);
        params.push(available);
        paramIndex++;
      }

      if (categoryId) {
        whereConditions.push(`mi.category_id = $${paramIndex}`);
        params.push(categoryId);
        paramIndex++;
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      const query = `
        SELECT 
          mi.*,
          mc.name as category_name,
          mc.description as category_description
        FROM menu_items mi
        LEFT JOIN menu_categories mc ON mi.category_id = mc.id
        ${whereClause}
        ORDER BY mi.sort_order ASC, mi.name ASC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const countQuery = `
        SELECT COUNT(*) as count
        FROM menu_items mi
        ${whereClause}
      `;

      params.push(limit, offset);
      const countParams = params.slice(0, -2);

      const [results, countResult] = await Promise.all([
        this.query(query, params),
        this.query(countQuery, countParams)
      ]);

      const total = parseInt(countResult.rows[0].count);

      return {
        items: results.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get menu item with customizations
  async getMenuItemWithCustomizations(itemId) {
    try {
      const query = `
        SELECT 
          mi.*,
          mc.name as category_name,
          json_agg(
            json_build_object(
              'id', mic.id,
              'name', mic.name,
              'required', mic.is_required,
              'multiple', mic.allow_multiple,
              'options', (
                SELECT json_agg(
                  json_build_object(
                    'id', mico.id,
                    'name', mico.name,
                    'price', mico.price
                  )
                  ORDER BY mico.sort_order
                )
                FROM menu_item_customization_options mico
                WHERE mico.customization_id = mic.id
              )
            )
            ORDER BY mic.sort_order
          ) FILTER (WHERE mic.id IS NOT NULL) as customizations
        FROM menu_items mi
        LEFT JOIN menu_categories mc ON mi.category_id = mc.id
        LEFT JOIN menu_item_customizations mic ON mi.id = mic.menu_item_id
        WHERE mi.id = $1
        GROUP BY mi.id, mc.name
      `;

      const result = await this.query(query, [itemId]);
      const item = result.rows[0];

      if (item) {
        item.customizations = item.customizations || [];
      }

      return item;
    } catch (error) {
      throw error;
    }
  }

  // Search menu items
  async searchMenuItems(searchParams) {
    try {
      const { q, categoryId, minPrice, maxPrice, dietary = [] } = searchParams;

      let whereConditions = ['mi.is_available = true'];
      let params = [];
      let paramIndex = 1;

      if (q) {
        whereConditions.push(`(mi.name ILIKE $${paramIndex} OR mi.description ILIKE $${paramIndex})`);
        params.push(`%${q}%`);
        paramIndex++;
      }

      if (categoryId) {
        whereConditions.push(`mi.category_id = $${paramIndex}`);
        params.push(categoryId);
        paramIndex++;
      }

      if (minPrice !== null && minPrice !== undefined) {
        whereConditions.push(`mi.price >= $${paramIndex}`);
        params.push(minPrice);
        paramIndex++;
      }

      if (maxPrice !== null && maxPrice !== undefined) {
        whereConditions.push(`mi.price <= $${paramIndex}`);
        params.push(maxPrice);
        paramIndex++;
      }

      if (dietary.includes('vegetarian')) {
        whereConditions.push('mi.is_vegetarian = true');
      }

      if (dietary.includes('vegan')) {
        whereConditions.push('mi.is_vegan = true');
      }

      if (dietary.includes('gluten_free')) {
        whereConditions.push('mi.is_gluten_free = true');
      }

      const whereClause = whereConditions.join(' AND ');

      const query = `
        SELECT 
          mi.id, mi.name, mi.description, mi.price, mi.image_url,
          mi.category_id, mi.is_available,
          mc.name as category_name
        FROM menu_items mi
        LEFT JOIN menu_categories mc ON mi.category_id = mc.id
        WHERE ${whereClause}
        ORDER BY mi.name ASC
        LIMIT 50
      `;

      const result = await this.query(query, params);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Get popular menu items
  async getPopularItems(limit = 10) {
    try {
      const query = `
        SELECT 
          mi.*,
          mc.name as category_name,
          COALESCE(order_stats.order_count, 0) as order_count,
          COALESCE(order_stats.total_quantity, 0) as total_quantity
        FROM menu_items mi
        LEFT JOIN menu_categories mc ON mi.category_id = mc.id
        LEFT JOIN (
          SELECT 
            oi.menu_item_id,
            COUNT(oi.id) as order_count,
            SUM(oi.quantity) as total_quantity
          FROM order_items oi
          JOIN orders o ON oi.order_id = o.id
          WHERE o.created_at >= NOW() - INTERVAL '30 days'
          AND o.status = 'delivered'
          GROUP BY oi.menu_item_id
        ) order_stats ON mi.id = order_stats.menu_item_id
        WHERE mi.is_available = true
        ORDER BY order_stats.order_count DESC NULLS LAST, mi.name ASC
        LIMIT $1
      `;

      const result = await this.query(query, [limit]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Update menu item availability
  async updateAvailability(itemId, isAvailable) {
    try {
      return await this.updateById(itemId, { is_available: isAvailable });
    } catch (error) {
      throw error;
    }
  }

  // Get menu items by category
  async getItemsByCategory(categoryId, availableOnly = true) {
    try {
      const conditions = { category_id: categoryId };
      if (availableOnly) {
        conditions.is_available = true;
      }

      return await this.findAll(conditions, 'sort_order ASC, name ASC');
    } catch (error) {
      throw error;
    }
  }

  // Get menu item statistics
  async getItemStats(itemId, days = 30) {
    try {
      const query = `
        SELECT 
          mi.name,
          mi.price,
          COALESCE(order_stats.order_count, 0) as order_count,
          COALESCE(order_stats.total_quantity, 0) as total_quantity,
          COALESCE(order_stats.total_revenue, 0) as total_revenue,
          COALESCE(review_stats.avg_rating, 0) as avg_rating,
          COALESCE(review_stats.review_count, 0) as review_count
        FROM menu_items mi
        LEFT JOIN (
          SELECT 
            oi.menu_item_id,
            COUNT(oi.id) as order_count,
            SUM(oi.quantity) as total_quantity,
            SUM(oi.total_price) as total_revenue
          FROM order_items oi
          JOIN orders o ON oi.order_id = o.id
          WHERE o.created_at >= NOW() - INTERVAL '${days} days'
          AND o.status = 'delivered'
          GROUP BY oi.menu_item_id
        ) order_stats ON mi.id = order_stats.menu_item_id
        LEFT JOIN (
          SELECT 
            menu_item_id,
            AVG(rating) as avg_rating,
            COUNT(*) as review_count
          FROM menu_item_reviews
          WHERE created_at >= NOW() - INTERVAL '${days} days'
          GROUP BY menu_item_id
        ) review_stats ON mi.id = review_stats.menu_item_id
        WHERE mi.id = $1
      `;

      const result = await this.query(query, [itemId]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Update menu item price
  async updatePrice(itemId, newPrice) {
    try {
      return await this.updateById(itemId, { price: newPrice });
    } catch (error) {
      throw error;
    }
  }
}

module.exports = MenuItem;
