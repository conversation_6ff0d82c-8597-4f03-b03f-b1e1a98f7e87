# FoodWay Backend - Railway Deployment Guide 🚀

This guide covers deploying the FoodWay backend to Railway with PostgreSQL and Redis.

## 🏗️ Railway Setup

### 1. Create Railway Project

1. Go to [Railway](https://railway.app)
2. Sign in with GitHub
3. Click "New Project"
4. Select "Deploy from GitHub repo"
5. Choose your FoodWay backend repository

### 2. Add Database Services

#### PostgreSQL Database
1. In your Railway project dashboard
2. Click "New Service"
3. Select "Database" → "PostgreSQL"
4. Railway will automatically provision a PostgreSQL instance
5. Note the connection details from the "Connect" tab

#### Redis Cache
1. Click "New Service" again
2. Select "Database" → "Redis"
3. Railway will automatically provision a Redis instance
4. Note the connection details from the "Connect" tab

### 3. Configure Environment Variables

In your Railway project dashboard, go to your backend service and add these environment variables:

#### Required Variables
```bash
# Database (automatically provided by Railway PostgreSQL service)
DATABASE_URL=${{Postgres.DATABASE_URL}}

# Redis (automatically provided by Railway Redis service)  
REDIS_URL=${{Redis.REDIS_URL}}

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# Security
BCRYPT_SALT_ROUNDS=12

# Application URLs
FRONTEND_URL=https://your-frontend-domain.com
BACKEND_URL=${{RAILWAY_PUBLIC_DOMAIN}}
```

#### Optional Variables
```bash
NODE_ENV=production
LOG_LEVEL=info
DEBUG=false
```

### 4. Deploy Configuration

Railway automatically detects the `railway.json` configuration:

```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm start",
    "healthcheckPath": "/health",
    "healthcheckTimeout": 300,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

## 🔄 Deployment Process

### Automatic Deployment
1. Push code to your main branch
2. Railway automatically detects changes
3. Builds and deploys your application
4. Health check ensures successful deployment

### Manual Deployment
1. Go to Railway dashboard
2. Select your backend service
3. Click "Deploy" → "Redeploy"

## 🗄️ Database Management

### Initial Setup
After deployment, run database migrations:

```bash
# SSH into Railway container or use Railway CLI
railway run npm run db:migrate

# Optional: Add sample data
railway run npm run db:seed
```

### Using Railway CLI
Install Railway CLI for easier management:

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login
railway login

# Link to your project
railway link

# Run commands
railway run npm run db:migrate
railway run npm run db:seed
```

## 📊 Monitoring & Health Checks

### Health Check Endpoint
Railway monitors your application using `/health` endpoint:

- **URL**: `https://your-app.railway.app/health`
- **Expected**: HTTP 200 with service status
- **Timeout**: 300 seconds
- **Retry**: Up to 10 times on failure

### Monitoring Services
Check service status:
- **Database**: `https://your-app.railway.app/test-db`
- **Redis**: `https://your-app.railway.app/test-redis`
- **General**: `https://your-app.railway.app/`

## 🔧 Troubleshooting

### Common Issues

#### 1. Database Connection Failed
```bash
# Check DATABASE_URL environment variable
echo $DATABASE_URL

# Test connection
railway run npm run db:migrate
```

#### 2. Redis Connection Failed
```bash
# Check REDIS_URL environment variable
echo $REDIS_URL

# Test Redis connection via health endpoint
curl https://your-app.railway.app/test-redis
```

#### 3. Build Failures
```bash
# Check build logs in Railway dashboard
# Ensure all dependencies are in package.json
# Verify Node.js version compatibility
```

#### 4. Health Check Failures
```bash
# Check if server starts properly
railway logs

# Test health endpoint locally
curl http://localhost:5000/health
```

### Debugging Commands

```bash
# View logs
railway logs

# Connect to database
railway connect postgres

# Connect to Redis
railway connect redis

# Run shell commands
railway shell
```

## 🔐 Security Considerations

### Environment Variables
- Never commit sensitive data to git
- Use Railway's environment variable system
- Rotate secrets regularly

### Database Security
- Railway PostgreSQL includes SSL by default
- Use connection pooling (already configured)
- Regular backups (Railway handles this)

### Application Security
- Helmet middleware enabled
- CORS configured
- Input validation with express-validator
- Password hashing with bcrypt

## 📈 Performance Optimization

### Database
- Connection pooling configured (max 20 connections)
- Indexes on frequently queried columns
- Query optimization

### Redis
- Connection reuse
- Automatic reconnection
- Error handling

### Application
- Compression middleware
- Request logging
- Graceful shutdown handling

## 🔄 CI/CD Pipeline

### Automatic Deployment
1. Code pushed to main branch
2. Railway detects changes
3. Runs build process
4. Deploys new version
5. Health check verification
6. Traffic routing to new version

### Rollback Process
1. Go to Railway dashboard
2. Select "Deployments" tab
3. Click "Redeploy" on previous version

## 📝 Environment-Specific Notes

### Development
- Use local PostgreSQL/Redis or Railway services
- Set `NODE_ENV=development`
- Enable debug logging

### Staging
- Separate Railway project recommended
- Use production-like data
- Test deployment process

### Production
- Set `NODE_ENV=production`
- Monitor performance and errors
- Regular database backups
- Security updates

## 🆘 Support Resources

- [Railway Documentation](https://docs.railway.app)
- [Railway Discord](https://discord.gg/railway)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Redis Documentation](https://redis.io/documentation)

---

**Happy Deploying!** 🎉
