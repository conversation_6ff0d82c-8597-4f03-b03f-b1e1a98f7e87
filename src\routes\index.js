// Main Routes Index
const express = require('express');
const healthRoutes = require('./health');
const authRoutes = require('./auth');
const userRoutes = require('./users');
const restaurantRoutes = require('./restaurants');
const searchRoutes = require('./search');
const menuRoutes = require('./menu');
const orderRoutes = require('./orders');
const favoritesRoutes = require('./favorites');
const uploadRoutes = require('./upload');
const paymentRoutes = require('./payments');
const addressRoutes = require('./addresses');
const paymentMethodRoutes = require('./paymentMethods');
const reviewRoutes = require('./reviews');
const promoCodeRoutes = require('./promoCodes');
const analyticsRoutes = require('./analytics');
const categoryRoutes = require('./categories');
const notificationRoutes = require('./notifications');
const promotionRoutes = require('./promotions');

const router = express.Router();

// Mount health routes (non-versioned)
router.use('/', healthRoutes);

// Mount API v1 routes
router.use('/api/v1/auth', authRoutes);
router.use('/api/v1/user', userRoutes);
router.use('/api/v1/restaurants', restaurantRoutes); // Updated to plural for consistency
router.use('/api/v1/categories', categoryRoutes);
router.use('/api/v1/menu', menuRoutes);
router.use('/api/v1/orders', orderRoutes);
router.use('/api/v1/payments', paymentRoutes);
router.use('/api/v1/payment-methods', paymentMethodRoutes);
router.use('/api/v1/addresses', addressRoutes);
router.use('/api/v1/favorites', favoritesRoutes);
router.use('/api/v1/reviews', reviewRoutes);
router.use('/api/v1/promo-codes', promoCodeRoutes);
router.use('/api/v1/notifications', notificationRoutes);
router.use('/api/v1/promotions', promotionRoutes);
router.use('/api/v1/analytics', analyticsRoutes);
router.use('/api/v1/upload', uploadRoutes);
router.use('/api/v1/search', searchRoutes);

module.exports = router;
