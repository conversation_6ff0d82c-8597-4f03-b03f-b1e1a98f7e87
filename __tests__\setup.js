// Jest Test Setup for FoodWay Backend
// This file runs before all tests

require('dotenv').config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';

// Increase timeout for database operations
jest.setTimeout(30000);

// Global test utilities
global.testUtils = {
  // Helper function to wait for async operations
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Helper to generate test data
  generateTestUser: () => ({
    email: `test${Date.now()}@example.com`,
    password: 'testpassword123',
    first_name: 'Test',
    last_name: 'User'
  }),
  
  generateTestRestaurant: () => ({
    name: `Test Restaurant ${Date.now()}`,
    email: `restaurant${Date.now()}@example.com`,
    password: 'testpassword123',
    city: 'Test City',
    cuisine_type: 'Test Cuisine'
  })
};

// Console log suppression for cleaner test output
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeAll(() => {
  // Suppress console.log during tests unless DEBUG is set
  if (!process.env.DEBUG) {
    console.log = jest.fn();
    console.error = jest.fn();
  }
});

afterAll(() => {
  // Restore console methods
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
});
