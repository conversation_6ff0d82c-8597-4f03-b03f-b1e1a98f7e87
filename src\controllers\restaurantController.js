// Restaurant Controller
const { validationResult } = require('express-validator');
const ResponseHelper = require('../utils/response');
const Restaurant = require('../models/Restaurant');
const queryOptimizationService = require('../services/queryOptimizationService');
const MenuCategory = require('../models/MenuCategory');
const Review = require('../models/Review');

class RestaurantController {
  // Get restaurants with filtering and pagination
  static async getRestaurants(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const searchParams = {
        q: req.query.search,
        cuisineTypes: req.query.cuisine ? req.query.cuisine.split(',') : [],
        minRating: req.query.minRating ? parseFloat(req.query.minRating) : 0,
        maxDeliveryFee: req.query.maxDeliveryFee ? parseFloat(req.query.maxDeliveryFee) : null,
        features: req.query.features ? req.query.features.split(',') : [],
        acceptingOrders: req.query.acceptingOrders !== 'false',
        page: parseInt(req.query.page) || 1,
        limit: Math.min(parseInt(req.query.limit) || 20, 50) // Max 50 per page
      };

      const restaurantModel = new Restaurant();
      const result = await restaurantModel.searchRestaurants(searchParams);

      return ResponseHelper.success(res, result, 'Restaurants retrieved successfully');
    } catch (error) {
      console.error('Get restaurants error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve restaurants', 500);
    }
  }

  // Get restaurant details with menu (OPTIMIZED)
  static async getRestaurantById(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.id;
      const categoryId = req.query.category;

      // Use optimized query service with caching
      const restaurant = await queryOptimizationService.getRestaurantWithMenu(restaurantId, categoryId);

      if (!restaurant) {
        return ResponseHelper.error(res, 'Restaurant not found', 404, {
          code: 'NOT_FOUND',
          message: 'Restaurant not found or is not active'
        });
      }

      return ResponseHelper.success(res, restaurant, 'Restaurant details retrieved successfully');
    } catch (error) {
      console.error('Get restaurant by ID error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve restaurant details', 500);
    }
  }

  // Get restaurant by slug
  static async getRestaurantBySlug(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const slug = req.params.slug;
      const restaurantModel = new Restaurant();

      const restaurant = await restaurantModel.findBySlug(slug);

      if (!restaurant) {
        return ResponseHelper.error(res, 'Restaurant not found', 404, {
          code: 'NOT_FOUND',
          message: 'Restaurant not found or is not active'
        });
      }

      // Get full restaurant details with menu
      const restaurantWithMenu = await restaurantModel.getRestaurantWithMenu(restaurant.id);

      return ResponseHelper.success(res, restaurantWithMenu, 'Restaurant details retrieved successfully');
    } catch (error) {
      console.error('Get restaurant by slug error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve restaurant details', 500);
    }
  }

  // Get nearby restaurants
  static async getNearbyRestaurants(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const { lat, lng, radius = 10000 } = req.query;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);

      // For now, return all restaurants since we don't have location-based filtering implemented
      // In a real implementation, you would filter by distance
      const restaurantModel = new Restaurant();
      const searchParams = {
        acceptingOrders: true,
        page: 1,
        limit
      };

      const result = await restaurantModel.searchRestaurants(searchParams);

      return ResponseHelper.success(res, result, 'Nearby restaurants retrieved successfully');
    } catch (error) {
      console.error('Get nearby restaurants error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve nearby restaurants', 500);
    }
  }

  // Get featured restaurants
  static async getFeaturedRestaurants(req, res) {
    try {
      const limit = Math.min(parseInt(req.query.limit) || 10, 50);
      const restaurantModel = new Restaurant();

      // For now, return popular restaurants as featured
      // In a real implementation, you would have a featured flag in the database
      const restaurants = await restaurantModel.getPopularRestaurants(limit);

      return ResponseHelper.success(res, { restaurants }, 'Featured restaurants retrieved successfully');
    } catch (error) {
      console.error('Get featured restaurants error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve featured restaurants', 500);
    }
  }

  // Get popular restaurants
  static async getPopularRestaurants(req, res) {
    try {
      const limit = Math.min(parseInt(req.query.limit) || 10, 50);
      const restaurantModel = new Restaurant();

      const restaurants = await restaurantModel.getPopularRestaurants(limit);

      return ResponseHelper.success(res, { restaurants }, 'Popular restaurants retrieved successfully');
    } catch (error) {
      console.error('Get popular restaurants error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve popular restaurants', 500);
    }
  }

  // Get restaurants by cuisine type
  static async getRestaurantsByCuisine(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const cuisineType = req.params.cuisine;
      const limit = Math.min(parseInt(req.query.limit) || 10, 50);
      const restaurantModel = new Restaurant();

      const restaurants = await restaurantModel.getRestaurantsByCuisine(cuisineType, limit);

      return ResponseHelper.success(res, { restaurants }, `${cuisineType} restaurants retrieved successfully`);
    } catch (error) {
      console.error('Get restaurants by cuisine error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve restaurants by cuisine', 500);
    }
  }

  // Get restaurant reviews
  static async getRestaurantReviews(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.id;
      const filters = {
        rating: req.query.rating ? parseInt(req.query.rating) : null,
        page: parseInt(req.query.page) || 1,
        limit: Math.min(parseInt(req.query.limit) || 20, 50),
        sortBy: req.query.sortBy || 'created_at',
        sortOrder: req.query.sortOrder || 'DESC'
      };

      const reviewModel = new Review();
      const result = await reviewModel.getRestaurantReviews(restaurantId, filters);

      return ResponseHelper.success(res, result, 'Restaurant reviews retrieved successfully');
    } catch (error) {
      console.error('Get restaurant reviews error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve restaurant reviews', 500);
    }
  }

  // Get restaurant review statistics
  static async getRestaurantReviewStats(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.id;
      const reviewModel = new Review();

      const stats = await reviewModel.getRestaurantReviewStats(restaurantId);

      return ResponseHelper.success(res, stats, 'Restaurant review statistics retrieved successfully');
    } catch (error) {
      console.error('Get restaurant review stats error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve restaurant review statistics', 500);
    }
  }

  // Get restaurant statistics
  static async getRestaurantStats(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.id;
      const days = parseInt(req.query.days) || 30;
      const restaurantModel = new Restaurant();

      const stats = await restaurantModel.getRestaurantStats(restaurantId, days);

      if (!stats) {
        return ResponseHelper.error(res, 'Restaurant not found', 404, {
          code: 'NOT_FOUND',
          message: 'Restaurant not found'
        });
      }

      return ResponseHelper.success(res, stats, 'Restaurant statistics retrieved successfully');
    } catch (error) {
      console.error('Get restaurant stats error:', error);
      return ResponseHelper.error(res, 'Failed to retrieve restaurant statistics', 500);
    }
  }

  // Check if restaurant is open
  static async checkRestaurantStatus(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.id;
      const restaurantModel = new Restaurant();

      const isOpen = await restaurantModel.isRestaurantOpen(restaurantId);
      const restaurant = await restaurantModel.findById(restaurantId);

      if (!restaurant) {
        return ResponseHelper.error(res, 'Restaurant not found', 404, {
          code: 'NOT_FOUND',
          message: 'Restaurant not found'
        });
      }

      const status = {
        isOpen,
        acceptingOrders: restaurant.accepting_orders,
        openingHours: restaurant.opening_hours,
        estimatedDeliveryTime: restaurant.estimated_delivery_time
      };

      return ResponseHelper.success(res, status, 'Restaurant status retrieved successfully');
    } catch (error) {
      console.error('Check restaurant status error:', error);
      return ResponseHelper.error(res, 'Failed to check restaurant status', 500);
    }
  }

  // Update restaurant accepting orders status (admin only)
  static async updateAcceptingOrders(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseHelper.validationError(res, errors.array());
      }

      const restaurantId = req.params.id;
      const { acceptingOrders } = req.body;
      const restaurantModel = new Restaurant();

      const updatedRestaurant = await restaurantModel.updateAcceptingOrders(restaurantId, acceptingOrders);

      if (!updatedRestaurant) {
        return ResponseHelper.error(res, 'Restaurant not found', 404, {
          code: 'NOT_FOUND',
          message: 'Restaurant not found'
        });
      }

      return ResponseHelper.success(res, updatedRestaurant, 'Restaurant status updated successfully');
    } catch (error) {
      console.error('Update restaurant accepting orders error:', error);
      return ResponseHelper.error(res, 'Failed to update restaurant status', 500);
    }
  }
}

module.exports = RestaurantController;
