// Test Fixtures - Sample Data for Testing

const sampleUser = {
  email: '<EMAIL>',
  password: 'password123',
  first_name: 'Test',
  last_name: 'User',
  phone: '+1234567890'
};

const sampleRestaurant = {
  name: 'Test Restaurant',
  email: '<EMAIL>',
  password: 'password123',
  phone: '+1234567891',
  address: '123 Test Street',
  city: 'Test City',
  cuisine_type: 'Test Cuisine',
  description: 'A test restaurant for testing purposes'
};

const sampleUsers = [
  {
    ...sampleUser,
    email: '<EMAIL>',
    first_name: 'User',
    last_name: 'One'
  },
  {
    ...sampleUser,
    email: '<EMAIL>',
    first_name: 'User',
    last_name: 'Two'
  }
];

const sampleRestaurants = [
  {
    ...sampleRestaurant,
    name: 'Pizza Place',
    email: '<EMAIL>',
    cuisine_type: 'Italian'
  },
  {
    ...sampleRestaurant,
    name: 'Burger Joint',
    email: '<EMAIL>',
    cuisine_type: 'American'
  }
];

const generateTestUser = (overrides = {}) => ({
  ...sampleUser,
  email: `test${Date.now()}@example.com`,
  ...overrides
});

const generateTestRestaurant = (overrides = {}) => ({
  ...sampleRestaurant,
  name: `Test Restaurant ${Date.now()}`,
  email: `restaurant${Date.now()}@example.com`,
  ...overrides
});

module.exports = {
  sampleUser,
  sampleRestaurant,
  sampleUsers,
  sampleRestaurants,
  generateTestUser,
  generateTestRestaurant
};
