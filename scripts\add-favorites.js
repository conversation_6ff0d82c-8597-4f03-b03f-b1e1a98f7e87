// Add Favorites Table Migration
require('dotenv').config();
const { Pool } = require('pg');

async function addFavoritesTable() {
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not found in environment variables');
    process.exit(1);
  }

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    console.log('🚀 Adding favorites table...\n');

    // Check if favorites table already exists
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'user_favorites'
      );
    `);

    if (tableCheck.rows[0].exists) {
      console.log('ℹ️  Favorites table already exists, skipping...');
      return;
    }

    // Create user_favorites table
    console.log('📋 Creating user_favorites table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_favorites (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        item_type VARCHAR(20) NOT NULL CHECK (item_type IN ('restaurant', 'menu_item')),
        item_id UUID NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        UNIQUE(user_id, item_type, item_id)
      )
    `);
    console.log('✅ User favorites table created');

    // Create indexes for better performance
    console.log('🔍 Creating favorites indexes...');
    await pool.query('CREATE INDEX IF NOT EXISTS idx_user_favorites_user_id ON user_favorites(user_id)');
    await pool.query('CREATE INDEX IF NOT EXISTS idx_user_favorites_type ON user_favorites(item_type)');
    await pool.query('CREATE INDEX IF NOT EXISTS idx_user_favorites_item ON user_favorites(item_id)');
    console.log('✅ Favorites indexes created');

    // Record the migration
    await pool.query(
      "INSERT INTO migrations (name) VALUES ('add_favorites_table')"
    );

    console.log('\n🎉 Favorites table migration completed successfully!');
    console.log('================================');
    console.log('📊 Favorites system is ready');
    console.log('================================\n');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run migration if called directly
if (require.main === module) {
  addFavoritesTable();
}

module.exports = addFavoritesTable;
